using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Newtonsoft.Json;

namespace WpfApp
{
    public partial class DirectorInfoWindow : Window
    {
        private readonly string _tmdbApiKey = "ecc77229228f64458ca8eb70df558b4c";
        private readonly HttpClient _httpClient = new HttpClient();
        private readonly Supabase.Client _supabaseClient;
        private string _directorName;

        public DirectorInfoWindow(string directorName, Supabase.Client supabaseClient)
        {
            InitializeComponent();
            _directorName = directorName;
            _supabaseClient = supabaseClient;
            DirectorNameTitle.Text = $"{directorName} - 导演资料";
            LoadDirectorData();
        }

        private async void LoadDirectorData()
        {
            try
            {
                LoadingGrid.Visibility = Visibility.Visible;

                // 1. 先搜索人物
                var searchResponse = await SearchPerson(_directorName);
                if (searchResponse == null || searchResponse.Results == null || searchResponse.Results.Count == 0)
                {
                    MessageBox.Show($"未找到导演\"{_directorName}\"的相关信息", "查询结果", MessageBoxButton.OK, MessageBoxImage.Information);
                    LoadingGrid.Visibility = Visibility.Collapsed;
                    return;
                }

                // 获取第一个匹配的人物
                var person = searchResponse.Results[0];

                // 2. 获取详细信息
                var directorDetails = await GetPersonDetails(person.Id);
                if (directorDetails != null)
                {
                    DisplayDirectorInfo(directorDetails);
                }

                LoadingGrid.Visibility = Visibility.Collapsed;
            }
            catch (Exception ex)
            {
                LoadingGrid.Visibility = Visibility.Collapsed;
                MessageBox.Show($"获取导演信息失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task<SearchPersonResponse> SearchPerson(string query)
        {
            string url = $"https://api.themoviedb.org/3/search/person?api_key={_tmdbApiKey}&language=zh-CN&query={Uri.EscapeDataString(query)}&page=1&include_adult=false";
            
            var response = await _httpClient.GetAsync(url);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<SearchPersonResponse>(content) ?? new SearchPersonResponse { Results = new List<PersonSearchResult>() };
            }
            return new SearchPersonResponse { Results = new List<PersonSearchResult>() };
        }

        private async Task<PersonDetails> GetPersonDetails(int personId)
        {
            string url = $"https://api.themoviedb.org/3/person/{personId}?api_key={_tmdbApiKey}&language=zh-CN&append_to_response=combined_credits";
            
            var response = await _httpClient.GetAsync(url);
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<PersonDetails>(content) ?? new PersonDetails();
            }
            return new PersonDetails();
        }

        private void DisplayDirectorInfo(PersonDetails director)
        {
            // 填充基本信息
            NameText.Text = director.Name;
            BirthdayText.Text = director.Birthday ?? "-";
            PlaceOfBirthText.Text = director.PlaceOfBirth ?? "-";
            PopularityText.Text = director.Popularity?.ToString() ?? "-";
            IdText.Text = director.Id.ToString();
            BiographyText.Text = !string.IsNullOrEmpty(director.Biography) ? director.Biography : "暂无介绍";

            // 设置图片
            if (!string.IsNullOrEmpty(director.ProfilePath))
            {
                DirectorImage.Source = new System.Windows.Media.Imaging.BitmapImage(
                    new Uri($"https://image.tmdb.org/t/p/w300{director.ProfilePath}"));
            }

            // 显示代表作品
            List<string> knownFor = new List<string>();
            if (director.CombinedCredits?.Cast != null)
            {
                // 按照受欢迎程度排序
                director.CombinedCredits.Cast.Sort((a, b) => b.Popularity.CompareTo(a.Popularity));

                // 取前10个作品
                int count = Math.Min(director.CombinedCredits.Cast.Count, 10);
                for (int i = 0; i < count; i++)
                {
                    var cast = director.CombinedCredits.Cast[i];
                    string title = !string.IsNullOrEmpty(cast.Title) ? cast.Title : cast.Name;
                    string year = "";
                    
                    if (!string.IsNullOrEmpty(cast.ReleaseDate) && cast.ReleaseDate.Length >= 4)
                    {
                        year = $"({cast.ReleaseDate.Substring(0, 4)})";
                    }
                    else if (!string.IsNullOrEmpty(cast.FirstAirDate) && cast.FirstAirDate.Length >= 4)
                    {
                        year = $"({cast.FirstAirDate.Substring(0, 4)})";
                    }
                    
                    knownFor.Add($"{title} {year} - {cast.CharacterOrJob}");
                }
            }

            if (director.CombinedCredits?.Crew != null)
            {
                // 按照受欢迎程度排序
                director.CombinedCredits.Crew.Sort((a, b) => b.Popularity.CompareTo(a.Popularity));

                // 取导演工作
                var directorJobs = director.CombinedCredits.Crew.FindAll(c => c.Job == "Director");
                
                // 取前10个作品
                int count = Math.Min(directorJobs.Count, 10);
                for (int i = 0; i < count; i++)
                {
                    var crew = directorJobs[i];
                    string title = !string.IsNullOrEmpty(crew.Title) ? crew.Title : crew.Name;
                    string year = "";
                    
                    if (!string.IsNullOrEmpty(crew.ReleaseDate) && crew.ReleaseDate.Length >= 4)
                    {
                        year = $"({crew.ReleaseDate.Substring(0, 4)})";
                    }
                    else if (!string.IsNullOrEmpty(crew.FirstAirDate) && crew.FirstAirDate.Length >= 4)
                    {
                        year = $"({crew.FirstAirDate.Substring(0, 4)})";
                    }
                    
                    knownFor.Add($"{title} {year} - 导演");
                }
            }

            if (knownFor.Count > 0)
            {
                KnownForList.ItemsSource = knownFor;
            }
            else
            {
                KnownForList.ItemsSource = new List<string> { "暂无代表作品信息" };
            }
        }

        #region 窗口控制事件
        private void TitleBar_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                // 双击最大化/还原窗口
                if (WindowState == WindowState.Maximized)
                    WindowState = WindowState.Normal;
                else
                    WindowState = WindowState.Maximized;
            }
            else
            {
                // 单击拖动窗口
                DragMove();
            }
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        private void MaximizeButton_Click(object sender, RoutedEventArgs e)
        {
            if (WindowState == WindowState.Maximized)
            {
                WindowState = WindowState.Normal;
            }
            else
            {
                WindowState = WindowState.Maximized;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
        #endregion

        #region 复制功能
        private void CopyWork_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as System.Windows.Controls.Button;
                if (button?.Tag != null)
                {
                    System.Windows.Clipboard.SetText(button.Tag.ToString());
                    // 不显示提示消息，保持低调
                }
            }
            catch (Exception ex)
            {
                // 静默处理错误，不打扰用户
                System.Diagnostics.Debug.WriteLine($"复制失败: {ex.Message}");
            }
        }
        #endregion
    }

    #region TMDB API 模型
    public class SearchPersonResponse
    {
        [JsonProperty("page")]
        public int Page { get; set; }

        [JsonProperty("results")]
        public List<PersonSearchResult> Results { get; set; }

        [JsonProperty("total_pages")]
        public int TotalPages { get; set; }

        [JsonProperty("total_results")]
        public int TotalResults { get; set; }
    }

    public class PersonSearchResult
    {
        [JsonProperty("id")]
        public int Id { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("popularity")]
        public double Popularity { get; set; }

        [JsonProperty("profile_path")]
        public string ProfilePath { get; set; }

        [JsonProperty("known_for_department")]
        public string KnownForDepartment { get; set; }
    }

    public class PersonDetails
    {
        [JsonProperty("id")]
        public int Id { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("birthday")]
        public string Birthday { get; set; }

        [JsonProperty("place_of_birth")]
        public string PlaceOfBirth { get; set; }

        [JsonProperty("profile_path")]
        public string ProfilePath { get; set; }

        [JsonProperty("biography")]
        public string Biography { get; set; }

        [JsonProperty("popularity")]
        public double? Popularity { get; set; }

        [JsonProperty("combined_credits")]
        public CombinedCredits CombinedCredits { get; set; }
    }

    public class CombinedCredits
    {
        [JsonProperty("cast")]
        public List<CreditItem> Cast { get; set; }

        [JsonProperty("crew")]
        public List<CreditItem> Crew { get; set; }
    }

    public class CreditItem
    {
        [JsonProperty("id")]
        public int Id { get; set; }

        [JsonProperty("title")]
        public string Title { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("release_date")]
        public string ReleaseDate { get; set; }

        [JsonProperty("first_air_date")]
        public string FirstAirDate { get; set; }

        [JsonProperty("popularity")]
        public double Popularity { get; set; }

        [JsonProperty("character")]
        public string Character { get; set; }

        [JsonProperty("job")]
        public string Job { get; set; }

        [JsonIgnore]
        public string CharacterOrJob => !string.IsNullOrEmpty(Character) ? Character : Job;
    }
    #endregion
} 