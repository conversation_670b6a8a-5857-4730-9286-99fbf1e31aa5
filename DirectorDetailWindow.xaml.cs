using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media.Imaging;
using System.Diagnostics;
using WpfApp.Services;
using System.IO;
using Supabase;

namespace WpfApp
{
    public partial class DirectorDetailWindow : Window
    {
        private string? _imdbUrl;
        private readonly Supabase.Client _supabaseClient;
        
        public DirectorDetailWindow(string directorName, string? directorChineseName = null, Supabase.Client supabaseClient = null!)
        {
            InitializeComponent();
            _supabaseClient = supabaseClient;
            
            // 更新窗口标题
            string displayName = !string.IsNullOrEmpty(directorChineseName) ? directorChineseName : directorName;
            TitleText.Text = $"导演详情 - {displayName}";
            
            // 加载导演信息
            LoadDirectorInfoAsync(directorName);
        }
        
        private async void LoadDirectorInfoAsync(string directorName)
        {
            try
            {
                LoadingPanel.Visibility = Visibility.Visible;
                
                // 使用导演服务获取导演信息
                var directorService = new DirectorService();
                var directorInfo = await directorService.GetDirectorInfoAsync(directorName);
                
                // 更新UI显示
                DirectorNameText.Text = directorInfo.Name;
                DirectorEnglishNameText.Text = directorName; // 使用原始英文名
                BiographyText.Text = directorInfo.Biography;
                BirthdateText.Text = directorInfo.Birthdate;
                PlaceOfBirthText.Text = directorInfo.PlaceOfBirth;
                
                // 设置IMDb链接
                _imdbUrl = directorInfo.ImdbUrl;
                if (string.IsNullOrEmpty(_imdbUrl))
                {
                    ImdbText.Text = "暂无IMDb信息";
                    ImdbLink.IsEnabled = false;
                }
                
                // 设置导演照片
                try
                {
                    if (!string.IsNullOrEmpty(directorInfo.ProfileImagePath))
                    {
                        if (directorInfo.ProfileImagePath.StartsWith("/Images/"))
                        {
                            // 使用应用内默认图片
                            var uri = new Uri($"pack://application:,,,{directorInfo.ProfileImagePath}", UriKind.Absolute);
                            ProfileImage.Source = new BitmapImage(uri);
                        }
                        else if (File.Exists(directorInfo.ProfileImagePath))
                        {
                            // 使用本地文件
                            ProfileImage.Source = new BitmapImage(new Uri(directorInfo.ProfileImagePath));
                        }
                        else if (directorInfo.ProfileImagePath.StartsWith("http"))
                        {
                            // 使用网络图片
                            var bitmap = new BitmapImage();
                            bitmap.BeginInit();
                            bitmap.UriSource = new Uri(directorInfo.ProfileImagePath);
                            bitmap.CacheOption = BitmapCacheOption.OnLoad;
                            bitmap.EndInit();
                            ProfileImage.Source = bitmap;
                        }
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"加载导演照片失败: {ex.Message}");
                    // 使用默认图片
                    ProfileImage.Source = new BitmapImage(new Uri("pack://application:,,,/Images/default_profile.jpg", UriKind.Absolute));
                }
                
                // 设置导演作品
                if (directorInfo.Movies != null && directorInfo.Movies.Count > 0)
                {
                    MoviesItemsControl.ItemsSource = directorInfo.Movies;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载导演信息失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                LoadingPanel.Visibility = Visibility.Collapsed;
            }
        }
        
        private void ImdbLink_Click(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrEmpty(_imdbUrl))
            {
                try
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = _imdbUrl,
                        UseShellExecute = true
                    });
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"打开IMDb链接失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
        
        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                // 双击最大化/还原窗口
                if (WindowState == WindowState.Maximized)
                    WindowState = WindowState.Normal;
                else
                    WindowState = WindowState.Maximized;
            }
            else
            {
                // 单击拖动窗口
                DragMove();
            }
        }
        
        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        private void MaximizeButton_Click(object sender, RoutedEventArgs e)
        {
            if (WindowState == WindowState.Maximized)
            {
                WindowState = WindowState.Normal;
            }
            else
            {
                WindowState = WindowState.Maximized;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        // 复制功能实现
        private void CopyToClipboard(string text, string itemName)
        {
            try
            {
                Clipboard.SetText(text);
                MessageBox.Show($"{itemName}已复制到剪贴板", "复制成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"复制失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CopyBirthdate_Click(object sender, RoutedEventArgs e)
        {
            CopyToClipboard(BirthdateText.Text, "出生日期");
        }

        private void CopyPlaceOfBirth_Click(object sender, RoutedEventArgs e)
        {
            CopyToClipboard(PlaceOfBirthText.Text, "出生地");
        }

        private void CopyImdbLink_Click(object sender, RoutedEventArgs e)
        {
            if (!string.IsNullOrEmpty(_imdbUrl))
            {
                CopyToClipboard(_imdbUrl, "IMDb链接");
            }
            else
            {
                MessageBox.Show("暂无IMDb链接信息", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void CopyDirectorName_Click(object sender, RoutedEventArgs e)
        {
            CopyToClipboard(DirectorNameText.Text, "导演中文名");
        }

        private void CopyDirectorEnglishName_Click(object sender, RoutedEventArgs e)
        {
            CopyToClipboard(DirectorEnglishNameText.Text, "导演英文名");
        }

        private void CopyBiography_Click(object sender, RoutedEventArgs e)
        {
            CopyToClipboard(BiographyText.Text, "导演简介");
        }
    }
} 