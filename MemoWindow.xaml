﻿<Window x:Class="WpfApp.MemoWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="咖啡详情" Height="500" Width="700"
        WindowStyle="None" WindowStartupLocation="CenterScreen"
        Background="#121a30"
        SizeToContent="WidthAndHeight"
        MinWidth="500" MinHeight="400"
        MaxWidth="1200" MaxHeight="900"
        AllowsTransparency="False"
        BorderThickness="1"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- 窗口控制按钮样式 -->
        <Style x:Key="WindowButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="40"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#4A5AE8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3A4AD8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border BorderThickness="1" BorderBrush="#1a2747" CornerRadius="8">
        <Grid>
            <!-- 顶部标题区 -->
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Border Grid.Row="0" MouseLeftButtonDown="Border_MouseDown">
                <Border.Background>
                    <ImageBrush ImageSource="/img/head_bg.png" Stretch="UniformToFill"/>
                </Border.Background>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock x:Name="TitleTextBlock"
                           Text="咖啡详情"
                           Foreground="White"
                           FontSize="14"
                           VerticalAlignment="Center"
                           Margin="15,0,0,0"/>

                    <!-- 窗口控制按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button x:Name="MinimizeButton" Style="{StaticResource WindowButtonStyle}"
                                Click="MinimizeButton_Click">
                            <TextBlock Text="&#xE921;" FontFamily="Segoe MDL2 Assets"
                                       FontSize="12" Foreground="White"/>
                        </Button>
                        <Button x:Name="CloseButton" Style="{StaticResource WindowButtonStyle}"
                                Click="CloseButton_Click">
                            <TextBlock Text="&#xE8BB;" FontFamily="Segoe MDL2 Assets"
                                       FontSize="12" Foreground="White"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 图片显示区 -->
            <Image Grid.Row="1" x:Name="CoffeeImage" Stretch="Uniform" Margin="10"
                   HorizontalAlignment="Center" VerticalAlignment="Center"
                   Loaded="CoffeeImage_Loaded"/>

            <!-- 文字显示区 - 支持HTML的区域 -->
            <Border Grid.Row="2" Background="#121a30" Margin="0,5,0,0" BorderBrush="#1a2747" BorderThickness="1" CornerRadius="4">
                <WebBrowser x:Name="MemoWebBrowser" Margin="5" Loaded="MemoWebBrowser_Loaded"/>
            </Border>

            <!-- 退出按钮 -->
            <Button Grid.Row="3" Content="退出"
                    Width="80" Height="30"
                    Margin="0,10,20,10"
                    HorizontalAlignment="Right"
                    Background="#4B91F1"
                    Foreground="White"
                    BorderThickness="0"
                    Click="CloseButton_Click"/>
        </Grid>
    </Border>
</Window>



