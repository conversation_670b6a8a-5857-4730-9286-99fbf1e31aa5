<Window x:Class="WpfAdmin.MonthlyStepsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="月度步数统计" Height="750" Width="900"
        WindowStartupLocation="CenterOwner"
        Background="#F5F5F5"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- 日历单元格样式 -->
        <Style x:Key="CalendarCellStyle" TargetType="Border">
            <Setter Property="Width" Value="110"/>
            <Setter Property="Height" Value="80"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F0F8FF"/>
                    <Setter Property="BorderBrush" Value="#4A90E2"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- 今天的单元格样式 -->
        <Style x:Key="TodayCellStyle" TargetType="Border" BasedOn="{StaticResource CalendarCellStyle}">
            <Setter Property="BorderBrush" Value="#4A90E2"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Background" Value="#E3F2FD"/>
        </Style>

        <!-- 其他月份的单元格样式 -->
        <Style x:Key="OtherMonthCellStyle" TargetType="Border" BasedOn="{StaticResource CalendarCellStyle}">
            <Setter Property="Background" Value="#F8F8F8"/>
            <Setter Property="Opacity" Value="0.6"/>
        </Style>

        <!-- 步数颜色样式 -->
        <Style x:Key="StepsColorStyle" TargetType="Rectangle">
            <Setter Property="Width" Value="100"/>
            <Setter Property="Height" Value="4"/>
            <Setter Property="Margin" Value="0,2,0,0"/>
            <Setter Property="RadiusX" Value="2"/>
            <Setter Property="RadiusY" Value="2"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题和导航 -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <Button x:Name="PrevMonthButton" Grid.Column="0" Content="◀"
                    Width="50" Height="40" Click="PrevMonthButton_Click"
                    FontSize="16" FontWeight="Bold"
                    Background="#4A90E2" Foreground="White"
                    BorderThickness="0"/>

            <TextBlock x:Name="MonthYearText" Grid.Column="1"
                       Text="2025年6月" FontSize="28" FontWeight="Bold"
                       HorizontalAlignment="Center" VerticalAlignment="Center"
                       Foreground="#333"/>

            <Button x:Name="NextMonthButton" Grid.Column="2" Content="▶"
                    Width="50" Height="40" Click="NextMonthButton_Click"
                    Margin="0,0,20,0" FontSize="16" FontWeight="Bold"
                    Background="#4A90E2" Foreground="White"
                    BorderThickness="0"/>

            <Button x:Name="DownloadReportButton" Grid.Column="3" Content="📊 下载报表"
                    Padding="15,8" Click="DownloadReportButton_Click"
                    Background="#28A745" Foreground="White"
                    BorderThickness="0" FontSize="14"/>
        </Grid>

        <!-- 星期标题 -->
        <Grid Grid.Row="1" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" Text="日" HorizontalAlignment="Center" FontWeight="Bold" FontSize="16" Foreground="#666"/>
            <TextBlock Grid.Column="1" Text="一" HorizontalAlignment="Center" FontWeight="Bold" FontSize="16" Foreground="#666"/>
            <TextBlock Grid.Column="2" Text="二" HorizontalAlignment="Center" FontWeight="Bold" FontSize="16" Foreground="#666"/>
            <TextBlock Grid.Column="3" Text="三" HorizontalAlignment="Center" FontWeight="Bold" FontSize="16" Foreground="#666"/>
            <TextBlock Grid.Column="4" Text="四" HorizontalAlignment="Center" FontWeight="Bold" FontSize="16" Foreground="#666"/>
            <TextBlock Grid.Column="5" Text="五" HorizontalAlignment="Center" FontWeight="Bold" FontSize="16" Foreground="#666"/>
            <TextBlock Grid.Column="6" Text="六" HorizontalAlignment="Center" FontWeight="Bold" FontSize="16" Foreground="#666"/>
        </Grid>

        <!-- 日历网格 -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
            <UniformGrid x:Name="CalendarGrid" Columns="7" Margin="0,0,0,20">
                <!-- 动态生成日历单元格 -->
            </UniformGrid>
        </ScrollViewer>

        <!-- 步数分布统计 -->
        <Border Grid.Row="3" Background="White" CornerRadius="12" Padding="25" Margin="0,0,0,20"
                BorderBrush="#E0E0E0" BorderThickness="1">
            <StackPanel>
                <TextBlock Text="📈 步数分布统计" FontSize="20" FontWeight="Bold" Margin="0,0,0,20" Foreground="#333"/>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 6000步以下 -->
                    <StackPanel Grid.Row="0" Grid.Column="0" Orientation="Horizontal" Margin="0,8">
                        <Rectangle Width="20" Height="20" Fill="#FF6B6B" Margin="0,0,15,0" RadiusX="4" RadiusY="4"/>
                        <TextBlock Text="6000 步以下" FontSize="16" VerticalAlignment="Center" Foreground="#555"/>
                        <Rectangle x:Name="Under6000Bar" Height="8" Fill="#FF6B6B" Margin="20,0,0,0"
                                   VerticalAlignment="Center" RadiusX="4" RadiusY="4" Width="0"/>
                    </StackPanel>
                    <TextBlock x:Name="Under6000Text" Grid.Row="0" Grid.Column="1" Text="0天"
                               FontSize="16" FontWeight="Bold" VerticalAlignment="Center" Foreground="#333"/>

                    <!-- 6000-8000步 -->
                    <StackPanel Grid.Row="1" Grid.Column="0" Orientation="Horizontal" Margin="0,8">
                        <Rectangle Width="20" Height="20" Fill="#FFB74D" Margin="0,0,15,0" RadiusX="4" RadiusY="4"/>
                        <TextBlock Text="6000-8000 步" FontSize="16" VerticalAlignment="Center" Foreground="#555"/>
                        <Rectangle x:Name="Under8000Bar" Height="8" Fill="#FFB74D" Margin="20,0,0,0"
                                   VerticalAlignment="Center" RadiusX="4" RadiusY="4" Width="0"/>
                    </StackPanel>
                    <TextBlock x:Name="Under8000Text" Grid.Row="1" Grid.Column="1" Text="0天"
                               FontSize="16" FontWeight="Bold" VerticalAlignment="Center" Foreground="#333"/>

                    <!-- 8000-10000步 -->
                    <StackPanel Grid.Row="2" Grid.Column="0" Orientation="Horizontal" Margin="0,8">
                        <Rectangle Width="20" Height="20" Fill="#4FC3F7" Margin="0,0,15,0" RadiusX="4" RadiusY="4"/>
                        <TextBlock Text="8000-10000 步" FontSize="16" VerticalAlignment="Center" Foreground="#555"/>
                        <Rectangle x:Name="Under10000Bar" Height="8" Fill="#4FC3F7" Margin="20,0,0,0"
                                   VerticalAlignment="Center" RadiusX="4" RadiusY="4" Width="0"/>
                    </StackPanel>
                    <TextBlock x:Name="Under10000Text" Grid.Row="2" Grid.Column="1" Text="0天"
                               FontSize="16" FontWeight="Bold" VerticalAlignment="Center" Foreground="#333"/>

                    <!-- 10000步以上 -->
                    <StackPanel Grid.Row="3" Grid.Column="0" Orientation="Horizontal" Margin="0,8">
                        <Rectangle Width="20" Height="20" Fill="#66BB6A" Margin="0,0,15,0" RadiusX="4" RadiusY="4"/>
                        <TextBlock Text="10000 步以上" FontSize="16" VerticalAlignment="Center" Foreground="#555"/>
                        <Rectangle x:Name="Over10000Bar" Height="8" Fill="#66BB6A" Margin="20,0,0,0"
                                   VerticalAlignment="Center" RadiusX="4" RadiusY="4" Width="0"/>
                    </StackPanel>
                    <TextBlock x:Name="Over10000Text" Grid.Row="3" Grid.Column="1" Text="0天"
                               FontSize="16" FontWeight="Bold" VerticalAlignment="Center" Foreground="#333"/>

                    <!-- 月度总计 -->
                    <StackPanel Grid.Row="4" Grid.Column="0" Orientation="Horizontal" Margin="0,15,0,5">
                        <Rectangle Width="20" Height="20" Fill="#9C27B0" Margin="0,0,15,0" RadiusX="4" RadiusY="4"/>
                        <TextBlock Text="本月总步数" FontSize="18" FontWeight="Bold" VerticalAlignment="Center" Foreground="#333"/>
                    </StackPanel>
                    <TextBlock x:Name="TotalStepsText" Grid.Row="4" Grid.Column="1" Text="0步"
                               FontSize="18" FontWeight="Bold" VerticalAlignment="Center" Foreground="#9C27B0"/>
                </Grid>
            </StackPanel>
        </Border>

        <!-- 关闭按钮 -->
        <Button Grid.Row="4" Content="关闭"
                HorizontalAlignment="Center" Click="CloseButton_Click"
                Width="120" Padding="12,8" FontSize="16"
                Background="#6C757D" Foreground="White"
                BorderThickness="0"/>
    </Grid>
</Window>
