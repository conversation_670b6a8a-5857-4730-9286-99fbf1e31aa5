using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using System.Linq;
using Supabase;
using Supabase.Postgrest;
using Supabase.Postgrest.Responses;
using static Supabase.Postgrest.Constants;

namespace WpfApp
{
    public class StockService
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiKey = "72430658081e51fc98";
        private readonly Supabase.Client _supabaseClient;
        
        // 用户ID - 实际项目中应该从登录系统获取
        private readonly string _userId = "default_user";
        
        // 预定义的自选股列表 - 仅在数据库为空时使用
        private readonly List<(string code, string name, int shares)> _defaultStocks = new List<(string code, string name, int shares)>
        {
            ("600298", "安琪酵母", 2800),
            
            ("300065", "海兰信", 1100)
            
            
        };

        public StockService(Supabase.Client supabaseClient)
        {
            _supabaseClient = supabaseClient;
            _httpClient = new HttpClient();
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        }

        public async Task<List<StockInfo>> GetStocksAsync()
        {
            List<StockInfo> stocks = new List<StockInfo>();
            
            // 从数据库获取用户自选股列表
            var userStocks = await GetUserStocksAsync();
            
            // 如果用户没有自选股，使用默认自选股列表
            if (userStocks.Count == 0)
            {
                foreach (var stock in _defaultStocks)
                {
                    try
                    {
                        var stockInfo = await GetStockInfoFromMairuiAsync(stock.code, stock.name, stock.shares);
                        if (stockInfo != null)
                        {
                            stocks.Add(stockInfo);
                        }
                        
                        // 添加短暂延迟，避免请求过于频繁
                        await Task.Delay(300);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"获取股票信息失败: {stock.name}({stock.code}) - {ex.Message}");
                    }
                }
            }
            else
            {
                // 使用用户自选股列表
                foreach (var stock in userStocks)
                {
                    try
                    {
                        var stockInfo = await GetStockInfoFromMairuiAsync(stock.Code, stock.Name, stock.Shares);
                        if (stockInfo != null)
                        {
                            stocks.Add(stockInfo);
                        }
                        
                        // 添加短暂延迟，避免请求过于频繁
                        await Task.Delay(300);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"获取股票信息失败: {stock.Name}({stock.Code}) - {ex.Message}");
                    }
                }
            }
            
            return stocks;
        }

        private async Task<StockInfo> GetStockInfoFromMairuiAsync(string code, string name, int shares)
        {
            try
            {
                // 构建麦蕊API URL - 使用新的实时交易数据接口
                string url = $"https://api.mairuiapi.com/hsstock/real/time/{code}/{_apiKey}";
                
                // 发送请求获取数据
                string response = await _httpClient.GetStringAsync(url);
                
                // 解析JSON数据
                return ParseMairuiData(response, code, name, shares);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取麦蕊股票数据失败: {ex.Message}");
                return null;
            }
        }

        private StockInfo ParseMairuiData(string jsonData, string code, string name, int shares)
        {
            try
            {
                using (JsonDocument doc = JsonDocument.Parse(jsonData))
                {
                    var root = doc.RootElement;
                    
                    // 解析实时交易数据
                    decimal currentPrice = root.GetProperty("p").GetDecimal();
                    decimal openPrice = root.GetProperty("o").GetDecimal();
                    decimal highPrice = root.GetProperty("h").GetDecimal();
                    decimal lowPrice = root.GetProperty("l").GetDecimal();
                    decimal previousClose = root.GetProperty("yc").GetDecimal();
                    decimal volume = root.GetProperty("v").GetDecimal();
                    decimal amount = root.GetProperty("cje").GetDecimal();
                    string updateTime = root.GetProperty("t").GetString();
                    
                    // 计算涨跌幅
                    decimal changePercent = (currentPrice - previousClose) / previousClose * 100;
                    
                    return new StockInfo
                    {
                        Code = code,
                        Name = name,
                        CurrentPrice = currentPrice,
                        OpenPrice = openPrice,
                        PreviousClose = previousClose,
                        HighPrice = highPrice,
                        LowPrice = lowPrice,
                        Volume = volume,
                        Amount = amount,
                        ChangePercent = Math.Round(changePercent, 2),
                        UpdateTime = DateTime.Parse(updateTime),
                        SharesOwned = shares
                    };
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析麦蕊数据失败: {ex.Message}");
                return null;
            }
        }

        private async Task<(string code, string name)?> GetStockCodeByNameAsync(string stockName)
        {
            try
            {
                var response = await _supabaseClient.Rpc("search_stock_by_name", new Dictionary<string, object>
                {
                    { "search_name", stockName }
                });

                if (response.Content != null)
                {
                    // 解析返回的JSON数据
                    using (JsonDocument doc = JsonDocument.Parse(response.Content))
                    {
                        var root = doc.RootElement;
                        if (root.ValueKind == JsonValueKind.Array && root.GetArrayLength() > 0)
                        {
                            var firstResult = root[0];
                            string code = firstResult.GetProperty("dm").GetString();
                            string name = firstResult.GetProperty("mc").GetString();
                            return (code, name);
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查询股票代码失败: {ex.Message}");
                return null;
            }
        }

        public async Task<StockInfo> GetStockInfoFromQueryAsync(string query)
        {
            try
            {
                string code;
                string name;

                // 如果输入的不是纯数字，通过数据库查询股票代码
                if (!query.All(char.IsDigit))
                {
                    var stockInfo = await GetStockCodeByNameAsync(query);
                    if (stockInfo == null)
                    {
                        return null;
                    }
                    code = stockInfo.Value.code;
                    name = stockInfo.Value.name;
                }
                else
                {
                    code = query;
                    name = query;
                }

                // 使用股票代码查询实时数据
                return await GetStockInfoFromMairuiAsync(code, name, 0);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"查询股票数据失败: {ex.Message}");
                return null;
            }
        }
        
        #region 自选股管理方法
        
        // 获取用户自选股列表
        public async Task<List<CustomStock>> GetUserStocksAsync()
        {
            try
            {
                var response = await _supabaseClient
                    .From<CustomStock>()
                    .Where(s => s.UserId == _userId)
                    .Get();
                
                return response.Models;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取用户自选股失败: {ex.Message}");
                
                // 出错时返回空列表
                return new List<CustomStock>();
            }
        }
        
        // 添加自选股
        public async Task<bool> AddUserStockAsync(string stockName, int shares)
        {
            try
            {
                // 检查是否已存在相同名称的自选股
                var existingStock = await _supabaseClient
                    .From<CustomStock>()
                    .Select("*")
                    .Filter("user_id", Operator.Equals, _userId)
                    .Filter("name", Operator.Equals, stockName)
                    .Get();

                if (existingStock.Models.Count > 0)
                {
                    // 如果存在，更新持有数量
                    var stock = existingStock.Models[0];
                    stock.Shares = shares;
                    stock.UpdatedAt = DateTime.UtcNow;
                    await _supabaseClient.From<CustomStock>().Update(stock);
                }
                else
                {
                    // 如果不存在，创建新的自选股
                    var newStock = new CustomStock
                    {
                        Name = stockName,
                        Code = stockName, // 暂时使用股票名称作为代码
                        Shares = shares,
                        UserId = _userId,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };
                    await _supabaseClient.From<CustomStock>().Insert(newStock);
                }
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加自选股失败: {ex.Message}");
                return false;
            }
        }
        
        // 更新自选股
        public async Task<bool> UpdateUserStockAsync(string code, int shares)
        {
            try
            {
                var existingStocks = await _supabaseClient
                    .From<CustomStock>()
                    .Where(s => s.UserId == _userId && s.Code == code)
                    .Get();
                
                if (existingStocks.Models.Count > 0)
                {
                    // 找到自选股，更新持股数量
                    var stock = existingStocks.Models[0];
                    stock.Shares = shares;
                    stock.UpdatedAt = DateTime.Now;
                    
                    await _supabaseClient
                        .From<CustomStock>()
                        .Update(stock);
                    
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"更新自选股失败: {ex.Message}");
                return false;
            }
        }
        
        // 删除自选股
        public async Task<bool> RemoveUserStockAsync(string code)
        {
            try
            {
                var existingStocks = await _supabaseClient
                    .From<CustomStock>()
                    .Where(s => s.UserId == _userId && s.Code == code)
                    .Get();
                
                if (existingStocks.Models.Count > 0)
                {
                    await _supabaseClient
                        .From<CustomStock>()
                        .Delete(existingStocks.Models[0]);
                    
                    return true;
                }
                
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"删除自选股失败: {ex.Message}");
                return false;
            }
        }
        
        // 初始化默认自选股
        public async Task InitializeDefaultStocksAsync()
        {
            try
            {
                // 检查用户是否已有自选股
                var existingStocks = await GetUserStocksAsync();
                if (existingStocks.Count > 0)
                {
                    return; // 已有自选股，不需要初始化
                }
                
                // 添加默认自选股
                foreach (var stock in _defaultStocks)
                {
                    // 查询股票名称对应的代码
                    var stockInfo = new CustomStock
                    {
                        Code = stock.code,
                        Name = stock.name,
                        Shares = stock.shares,
                        UserId = _userId,
                        CreatedAt = DateTime.Now,
                        UpdatedAt = DateTime.Now
                    };
                    
                    await _supabaseClient
                        .From<CustomStock>()
                        .Insert(stockInfo);
                    
                    // 添加延迟，避免请求过于频繁
                    await Task.Delay(100);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"初始化默认自选股失败: {ex.Message}");
            }
        }
        
        #endregion
    }
}