<Window x:Class="WpfApp.AwardsFilterWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="电影评奖筛选" Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        Background="#1E1E2E"
        WindowStyle="None"
        AllowsTransparency="True"
        ResizeMode="NoResize">

    <Window.Resources>
        <!-- 现代化按钮样式 -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#4A5AE8"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#5D6EFF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3A4AE8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 奖项标签样式 -->
        <Style x:Key="AwardTagStyle" TargetType="Button">
            <Setter Property="Background" Value="#2A2D47"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#4A5AE8"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="5,3"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Normal"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="15"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#4A5AE8"/>
                                <Setter Property="BorderBrush" Value="#5D6EFF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3A4AE8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 关闭按钮样式 -->
        <Style x:Key="CloseButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="30"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="15">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#FF4444"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border CornerRadius="10" Background="#1E1E2E" BorderThickness="1" BorderBrush="#4A5AE8">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="60"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Grid Grid.Row="0" Background="#2A2D47">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock Text="🏆" FontSize="20" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="电影评奖筛选" FontSize="18" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>

                <Button Grid.Column="1" Style="{StaticResource CloseButtonStyle}" 
                        Content="✕" Margin="0,0,15,0" Click="CloseButton_Click"/>
            </Grid>

            <!-- 主内容区域 -->
            <Grid Grid.Row="1" Margin="20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 说明文字 -->
                <TextBlock Grid.Row="0" Text="点击下方的获奖标签来筛选相应的电影："
                           FontSize="14" Foreground="#B0B0B0" Margin="0,0,0,15"/>

                <!-- 加载提示 -->
                <Grid x:Name="LoadingGrid" Grid.Row="1" Visibility="Visible">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <ProgressBar Width="200" Height="4" IsIndeterminate="True"
                                     Foreground="#4A5AE8" Background="#2A2D47" BorderThickness="0"/>
                        <TextBlock Text="正在加载获奖信息..." FontSize="14" Foreground="White" 
                                   HorizontalAlignment="Center" Margin="0,10,0,0"/>
                    </StackPanel>
                </Grid>

                <!-- 奖项标签容器 -->
                <ScrollViewer x:Name="AwardsScrollViewer" Grid.Row="1" Visibility="Collapsed"
                              VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                    <WrapPanel x:Name="AwardsPanel" Orientation="Horizontal"/>
                </ScrollViewer>

                <!-- 无数据提示 -->
                <StackPanel x:Name="NoDataPanel" Grid.Row="1" Visibility="Collapsed"
                            HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBlock Text="😔" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="暂无获奖信息" FontSize="16" Foreground="#B0B0B0" HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>

            <!-- 底部按钮 -->
            <Grid Grid.Row="2" Background="#2A2D47">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" VerticalAlignment="Center" Margin="20,0">
                    <Button Content="清除筛选" Style="{StaticResource ModernButton}" 
                            Background="#666666" Click="ClearFilterButton_Click"/>
                    <Button Content="关闭" Style="{StaticResource ModernButton}" 
                            Background="#FF4444" Click="CloseButton_Click"/>
                </StackPanel>
            </Grid>
        </Grid>
    </Border>
</Window>
