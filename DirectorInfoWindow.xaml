<Window x:Class="WpfApp.DirectorInfoWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WpfApp"
        mc:Ignorable="d"
        Title="导演资料" Height="600" Width="800"
        Background="#121a30"
        WindowStartupLocation="CenterScreen"
        WindowStyle="None"
        AllowsTransparency="True"
        BorderThickness="0"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- 窗口控制按钮样式 -->
        <Style x:Key="WindowButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="40"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#4A5AE8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3A4AD8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 低调的复制按钮样式 -->
        <Style x:Key="SubtleCopyButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#666"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="4"/>
            <Setter Property="Width" Value="20"/>
            <Setter Property="Height" Value="20"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Opacity" Value="0.5"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="3"
                                Padding="{TemplateBinding Padding}">
                            <TextBlock Text="&#xE8C8;" FontFamily="Segoe MDL2 Assets"
                                       FontSize="10" Foreground="{TemplateBinding Foreground}"
                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#364375"/>
                                <Setter Property="Foreground" Value="White"/>
                                <Setter Property="Opacity" Value="1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 标签样式 -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#8890AD"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>

        <!-- 内容样式 -->
        <Style x:Key="ContentStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>
    </Window.Resources>

    <Border BorderThickness="1" BorderBrush="#1a2747" CornerRadius="8">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Border Grid.Row="0" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                <Border.Background>
                    <ImageBrush ImageSource="/img/head_bg.png" Stretch="UniformToFill"/>
                </Border.Background>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 标题文本 -->
                    <TextBlock x:Name="DirectorNameTitle" Text="导演资料" Foreground="White" FontSize="14"
                               VerticalAlignment="Center" Margin="15,0,0,0"/>

                    <!-- 窗口控制按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button x:Name="MinimizeButton" Style="{StaticResource WindowButtonStyle}"
                                Click="MinimizeButton_Click">
                            <TextBlock Text="&#xE921;" FontFamily="Segoe MDL2 Assets"
                                       FontSize="12" Foreground="White"/>
                        </Button>
                        <Button x:Name="MaximizeButton" Style="{StaticResource WindowButtonStyle}"
                                Click="MaximizeButton_Click">
                            <TextBlock Text="&#xE922;" FontFamily="Segoe MDL2 Assets"
                                       FontSize="12" Foreground="White"/>
                        </Button>
                        <Button x:Name="CloseButton" Style="{StaticResource WindowButtonStyle}"
                                Click="CloseButton_Click">
                            <TextBlock Text="&#xE8BB;" FontFamily="Segoe MDL2 Assets"
                                       FontSize="12" Foreground="White"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 内容区域 -->
            <Grid Grid.Row="1" Margin="20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧导演照片 -->
                <Border Grid.Column="0" Width="200" Height="300" Margin="0,0,20,0" VerticalAlignment="Top"
                        CornerRadius="10" BorderBrush="#004D4D" BorderThickness="0.5">
                    <Border.Effect>
                        <DropShadowEffect ShadowDepth="3" BlurRadius="5" Opacity="0.3" Color="Black"/>
                    </Border.Effect>
                    <Image x:Name="DirectorImage" Stretch="UniformToFill"/>
                </Border>

                <!-- 右侧信息区域 -->
                <ScrollViewer Grid.Column="1" VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <TextBlock Text="基本信息" Style="{StaticResource LabelStyle}" FontSize="18" Margin="0,0,0,15"/>

                        <Grid Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="100"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Grid.Column="0" Text="姓名:" Style="{StaticResource LabelStyle}"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" x:Name="NameText" Text="-" Style="{StaticResource ContentStyle}"/>

                            <TextBlock Grid.Row="1" Grid.Column="0" Text="出生日期:" Style="{StaticResource LabelStyle}" Margin="0,8,0,0"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" x:Name="BirthdayText" Text="-" Style="{StaticResource ContentStyle}" Margin="0,8,0,0"/>

                            <TextBlock Grid.Row="2" Grid.Column="0" Text="出生地:" Style="{StaticResource LabelStyle}" Margin="0,8,0,0"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" x:Name="PlaceOfBirthText" Text="-" Style="{StaticResource ContentStyle}" Margin="0,8,0,0"/>

                            <TextBlock Grid.Row="3" Grid.Column="0" Text="知名度:" Style="{StaticResource LabelStyle}" Margin="0,8,0,0"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" x:Name="PopularityText" Text="-" Style="{StaticResource ContentStyle}" Margin="0,8,0,0"/>

                            <TextBlock Grid.Row="4" Grid.Column="0" Text="TMDB ID:" Style="{StaticResource LabelStyle}" Margin="0,8,0,0"/>
                            <TextBlock Grid.Row="4" Grid.Column="1" x:Name="IdText" Text="-" Style="{StaticResource ContentStyle}" Margin="0,8,0,0"/>
                        </Grid>

                        <!-- 个人简介 -->
                        <TextBlock Text="个人简介" Style="{StaticResource LabelStyle}" FontSize="18" Margin="0,10,0,10"/>
                        <TextBlock x:Name="BiographyText" Text="-" Style="{StaticResource ContentStyle}"
                                   TextWrapping="Wrap" MaxWidth="500" Margin="0,0,0,15"/>

                        <!-- 代表作品 -->
                        <TextBlock Text="代表作品" Style="{StaticResource LabelStyle}" FontSize="18" Margin="0,10,0,10"/>
                        <ItemsControl x:Name="KnownForList">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Margin="0,0,0,8" BorderBrush="#004D4D" BorderThickness="0,0,0,0.5"
                                            Padding="0,0,0,8" Background="Transparent">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="0" Text="{Binding}" Style="{StaticResource ContentStyle}"
                                                       TextWrapping="Wrap" Margin="0,0,10,0"/>
                                            <Button Grid.Column="1" Style="{StaticResource SubtleCopyButtonStyle}"
                                                    Click="CopyWork_Click" Tag="{Binding}" VerticalAlignment="Top"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </ScrollViewer>

                <!-- 加载指示器 -->
                <Grid Grid.ColumnSpan="2" x:Name="LoadingGrid" Background="#80000000" Visibility="Collapsed">
                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                        <TextBlock Text="正在加载导演信息..." Foreground="White" FontSize="16"
                                   HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <ProgressBar Width="200" Height="5" IsIndeterminate="True"
                                     Foreground="#5D6EFF" Background="Transparent"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Grid>
    </Border>
</Window>