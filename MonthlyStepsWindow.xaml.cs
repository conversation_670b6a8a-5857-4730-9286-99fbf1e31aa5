using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Shapes;
using Microsoft.Win32;
using WpfAdmin.Models;
using WpfAdmin.Services;

namespace WpfAdmin
{
    public partial class MonthlyStepsWindow : Window
    {
        private readonly HealthService _healthService;
        private DateTime _currentMonth;
        private List<HealthData> _monthlyData;

        public MonthlyStepsWindow()
        {
            InitializeComponent();
            _healthService = new HealthService();
            _currentMonth = DateTime.Now;
            _monthlyData = new List<HealthData>();

            Loaded += MonthlyStepsWindow_Loaded;
        }

        private async void MonthlyStepsWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadMonthlyData();
        }

        private async Task LoadMonthlyData()
        {
            try
            {
                // 更新标题
                MonthYearText.Text = _currentMonth.ToString("yyyy年M月", CultureInfo.GetCultureInfo("zh-CN"));

                // 获取月度数据
                _monthlyData = await _healthService.GetMonthlyDataAsync(_currentMonth.Year, _currentMonth.Month);

                // 生成日历
                GenerateCalendar();

                // 更新统计信息
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void GenerateCalendar()
        {
            CalendarGrid.Children.Clear();

            // 获取当月第一天和最后一天
            var firstDay = new DateTime(_currentMonth.Year, _currentMonth.Month, 1);
            var lastDay = firstDay.AddMonths(1).AddDays(-1);

            // 获取第一天是星期几（0=星期日）
            int firstDayOfWeek = (int)firstDay.DayOfWeek;

            // 添加上个月的日期（填充空白）
            var prevMonth = firstDay.AddMonths(-1);
            var prevMonthLastDay = prevMonth.AddMonths(1).AddDays(-1).Day;

            for (int i = firstDayOfWeek - 1; i >= 0; i--)
            {
                var date = new DateTime(prevMonth.Year, prevMonth.Month, prevMonthLastDay - i);
                var cell = CreateCalendarCell(date, false, true);
                CalendarGrid.Children.Add(cell);
            }

            // 添加当月的日期
            for (int day = 1; day <= lastDay.Day; day++)
            {
                var date = new DateTime(_currentMonth.Year, _currentMonth.Month, day);
                var isToday = date.Date == DateTime.Today;
                var cell = CreateCalendarCell(date, true, false, isToday);
                CalendarGrid.Children.Add(cell);
            }

            // 添加下个月的日期（填充剩余空白）
            var nextMonth = firstDay.AddMonths(1);
            int remainingCells = 42 - CalendarGrid.Children.Count; // 6行 × 7列 = 42个单元格

            for (int day = 1; day <= remainingCells; day++)
            {
                var date = new DateTime(nextMonth.Year, nextMonth.Month, day);
                var cell = CreateCalendarCell(date, false, true);
                CalendarGrid.Children.Add(cell);
            }
        }

        private Border CreateCalendarCell(DateTime date, bool isCurrentMonth, bool isOtherMonth, bool isToday = false)
        {
            var border = new Border();

            // 设置样式
            if (isToday)
            {
                border.Style = (Style)FindResource("TodayCellStyle");
            }
            else if (isOtherMonth)
            {
                border.Style = (Style)FindResource("OtherMonthCellStyle");
            }
            else
            {
                border.Style = (Style)FindResource("CalendarCellStyle");
            }

            var stackPanel = new StackPanel
            {
                Margin = new Thickness(8),
                VerticalAlignment = VerticalAlignment.Top
            };

            // 日期文本
            var dayText = new TextBlock
            {
                Text = date.Day.ToString(),
                FontSize = 16,
                FontWeight = isToday ? FontWeights.Bold : FontWeights.Normal,
                Foreground = isOtherMonth ? Brushes.Gray : (isToday ? new SolidColorBrush(Color.FromRgb(74, 144, 226)) : Brushes.Black),
                HorizontalAlignment = HorizontalAlignment.Left
            };
            stackPanel.Children.Add(dayText);

            // 如果是当前月份，显示步数信息
            if (isCurrentMonth)
            {
                var healthData = _monthlyData.FirstOrDefault(h => h.Date.Date == date.Date);
                if (healthData != null)
                {
                    // 步数文本
                    var stepsText = new TextBlock
                    {
                        Text = $"{healthData.Steps:N0}步",
                        FontSize = 11,
                        Foreground = Brushes.DarkBlue,
                        Margin = new Thickness(0, 2, 0, 0),
                        TextWrapping = TextWrapping.Wrap
                    };
                    stackPanel.Children.Add(stepsText);

                    // 步数颜色条
                    var colorBar = new Rectangle
                    {
                        Height = 4,
                        Margin = new Thickness(0, 2, 0, 0),
                        RadiusX = 2,
                        RadiusY = 2,
                        Fill = GetStepsColorBrush(healthData.Steps)
                    };
                    stackPanel.Children.Add(colorBar);

                    // 如果有血压异常，显示警告图标
                    if (healthData.IsBloodPressureAbnormal)
                    {
                        var warningIcon = new TextBlock
                        {
                            Text = "⚠",
                            FontSize = 12,
                            Foreground = Brushes.Red,
                            HorizontalAlignment = HorizontalAlignment.Right,
                            Margin = new Thickness(0, -20, 0, 0)
                        };
                        stackPanel.Children.Add(warningIcon);
                    }
                }
                else if (date.Date <= DateTime.Today)
                {
                    // 没有数据的过去日期
                    var noDataText = new TextBlock
                    {
                        Text = "无数据",
                        FontSize = 10,
                        Foreground = Brushes.Gray,
                        Margin = new Thickness(0, 2, 0, 0)
                    };
                    stackPanel.Children.Add(noDataText);
                }
            }

            border.Child = stackPanel;

            // 添加点击事件
            if (isCurrentMonth)
            {
                border.MouseLeftButtonUp += (s, e) => OnDateCellClick(date);
            }

            return border;
        }

        private SolidColorBrush GetStepsColorBrush(int steps)
        {
            if (steps < 6000)
                return new SolidColorBrush(Color.FromRgb(255, 107, 107)); // 红色
            else if (steps < 8000)
                return new SolidColorBrush(Color.FromRgb(255, 183, 77)); // 橙色
            else if (steps < 10000)
                return new SolidColorBrush(Color.FromRgb(79, 195, 247)); // 蓝色
            else
                return new SolidColorBrush(Color.FromRgb(102, 187, 106)); // 绿色
        }

        private void OnDateCellClick(DateTime date)
        {
            var healthData = _monthlyData.FirstOrDefault(h => h.Date.Date == date.Date);

            string message;
            if (healthData != null)
            {
                message = $"日期：{date:yyyy年M月d日}\n" +
                         $"步数：{healthData.Steps:N0}步\n" +
                         $"血压：{healthData.SystolicPressure}/{healthData.DiastolicPressure} mmHg\n" +
                         $"备注：{(string.IsNullOrEmpty(healthData.Notes) ? "无" : healthData.Notes)}";

                if (healthData.IsBloodPressureAbnormal)
                {
                    message += "\n\n⚠ 血压异常，请注意！";
                }
            }
            else
            {
                message = $"日期：{date:yyyy年M月d日}\n\n该日期暂无健康数据记录";
            }

            MessageBox.Show(message, "日期详情", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void UpdateStatistics()
        {
            if (_monthlyData == null || !_monthlyData.Any())
            {
                Under6000Text.Text = "0天";
                Under8000Text.Text = "0天";
                Under10000Text.Text = "0天";
                Over10000Text.Text = "0天";
                TotalStepsText.Text = "0步";

                // 重置进度条
                Under6000Bar.Width = 0;
                Under8000Bar.Width = 0;
                Under10000Bar.Width = 0;
                Over10000Bar.Width = 0;
                return;
            }

            // 统计各步数范围的天数
            int under6000 = _monthlyData.Count(h => h.Steps < 6000);
            int between6000And8000 = _monthlyData.Count(h => h.Steps >= 6000 && h.Steps < 8000);
            int between8000And10000 = _monthlyData.Count(h => h.Steps >= 8000 && h.Steps < 10000);
            int over10000 = _monthlyData.Count(h => h.Steps >= 10000);
            int totalDays = _monthlyData.Count;
            long totalSteps = _monthlyData.Sum(h => (long)h.Steps);

            // 更新文本
            Under6000Text.Text = $"{under6000}天";
            Under8000Text.Text = $"{between6000And8000}天";
            Under10000Text.Text = $"{between8000And10000}天";
            Over10000Text.Text = $"{over10000}天";
            TotalStepsText.Text = $"{totalSteps:N0}步";

            // 更新进度条（最大宽度200像素）
            double maxWidth = 200;
            if (totalDays > 0)
            {
                Under6000Bar.Width = (under6000 * maxWidth) / totalDays;
                Under8000Bar.Width = (between6000And8000 * maxWidth) / totalDays;
                Under10000Bar.Width = (between8000And10000 * maxWidth) / totalDays;
                Over10000Bar.Width = (over10000 * maxWidth) / totalDays;
            }
        }

        private async void PrevMonthButton_Click(object sender, RoutedEventArgs e)
        {
            _currentMonth = _currentMonth.AddMonths(-1);
            await LoadMonthlyData();
        }

        private async void NextMonthButton_Click(object sender, RoutedEventArgs e)
        {
            _currentMonth = _currentMonth.AddMonths(1);
            await LoadMonthlyData();
        }

        private async void DownloadReportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "CSV文件 (*.csv)|*.csv|所有文件 (*.*)|*.*",
                    FileName = $"月度步数统计_{_currentMonth:yyyy年M月}.csv"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    await ExportMonthlyReport(saveFileDialog.FileName);
                    MessageBox.Show("报表导出成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task ExportMonthlyReport(string filePath)
        {
            var lines = new List<string>
            {
                "日期,步数,收缩压,舒张压,血压状态,备注"
            };

            var sortedData = _monthlyData.OrderBy(h => h.Date).ToList();

            foreach (var data in sortedData)
            {
                var bloodPressureStatus = data.IsBloodPressureAbnormal ? "异常" : "正常";
                var line = $"{data.Date:yyyy-MM-dd},{data.Steps},{data.SystolicPressure},{data.DiastolicPressure},{bloodPressureStatus},\"{data.Notes}\"";
                lines.Add(line);
            }

            // 添加统计信息
            lines.Add("");
            lines.Add("统计信息");
            lines.Add($"统计月份,{_currentMonth:yyyy年M月}");
            lines.Add($"记录天数,{_monthlyData.Count}");
            lines.Add($"总步数,{_monthlyData.Sum(h => (long)h.Steps)}");
            lines.Add($"平均步数,{(_monthlyData.Any() ? _monthlyData.Average(h => h.Steps):0):F0}");
            lines.Add($"最高步数,{(_monthlyData.Any() ? _monthlyData.Max(h => h.Steps) : 0)}");
            lines.Add($"最低步数,{(_monthlyData.Any() ? _monthlyData.Min(h => h.Steps) : 0)}");

            await File.WriteAllLinesAsync(filePath, lines, System.Text.Encoding.UTF8);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
}
