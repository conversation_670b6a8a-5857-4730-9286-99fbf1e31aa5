﻿#pragma checksum "..\..\..\MovieDetailWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7BFBA191E3730C38285CF3B29121AAC0A14A4FB6"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace WpfApp {
    
    
    /// <summary>
    /// MovieDetailWindow
    /// </summary>
    public partial class MovieDetailWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 290 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RatingText;
        
        #line default
        #line hidden
        
        
        #line 296 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieTitle;
        
        #line default
        #line hidden
        
        
        #line 300 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border AwardTag;
        
        #line default
        #line hidden
        
        
        #line 301 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AwardTagText;
        
        #line default
        #line hidden
        
        
        #line 327 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image PosterImage;
        
        #line default
        #line hidden
        
        
        #line 336 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DateText;
        
        #line default
        #line hidden
        
        
        #line 344 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DirectorText;
        
        #line default
        #line hidden
        
        
        #line 349 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CountryText;
        
        #line default
        #line hidden
        
        
        #line 361 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock GenreText;
        
        #line default
        #line hidden
        
        
        #line 368 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AwardsDetail;
        
        #line default
        #line hidden
        
        
        #line 381 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AwardsText;
        
        #line default
        #line hidden
        
        
        #line 392 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MagnetButton;
        
        #line default
        #line hidden
        
        
        #line 394 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SubtitleButton;
        
        #line default
        #line hidden
        
        
        #line 396 "..\..\..\MovieDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DoubanButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WpfAdmin;component/moviedetailwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MovieDetailWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 253 "..\..\..\MovieDetailWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Background_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.RatingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.MovieTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.AwardTag = ((System.Windows.Controls.Border)(target));
            return;
            case 5:
            this.AwardTagText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            
            #line 308 "..\..\..\MovieDetailWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.PosterImage = ((System.Windows.Controls.Image)(target));
            return;
            case 8:
            this.DateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.DirectorText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.CountryText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.GenreText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.AwardsDetail = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 13:
            this.AwardsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.MagnetButton = ((System.Windows.Controls.Button)(target));
            
            #line 393 "..\..\..\MovieDetailWindow.xaml"
            this.MagnetButton.Click += new System.Windows.RoutedEventHandler(this.MagnetButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.SubtitleButton = ((System.Windows.Controls.Button)(target));
            
            #line 395 "..\..\..\MovieDetailWindow.xaml"
            this.SubtitleButton.Click += new System.Windows.RoutedEventHandler(this.SubtitleButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.DoubanButton = ((System.Windows.Controls.Button)(target));
            
            #line 397 "..\..\..\MovieDetailWindow.xaml"
            this.DoubanButton.Click += new System.Windows.RoutedEventHandler(this.DoubanButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

