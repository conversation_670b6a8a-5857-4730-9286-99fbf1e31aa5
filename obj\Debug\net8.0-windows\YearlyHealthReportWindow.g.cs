﻿#pragma checksum "..\..\..\YearlyHealthReportWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "DBB2FE6DF0E5539D4A4EAC6912408FA149451F48"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace WpfAdmin {
    
    
    /// <summary>
    /// YearlyHealthReportWindow
    /// </summary>
    public partial class YearlyHealthReportWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 21 "..\..\..\YearlyHealthReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TitleText;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\YearlyHealthReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock YearlyTotalStepsText;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\YearlyHealthReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock YearlyDailyAverageText;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\YearlyHealthReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock YearlyAvgSystolicText;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\YearlyHealthReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock YearlyAvgDiastolicText;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\YearlyHealthReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock YearlyBloodPressureStatusText;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\YearlyHealthReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock YearlyRecordDaysText;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\YearlyHealthReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock YearlyRecordRateText;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\YearlyHealthReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock YearlyAbnormalDaysText;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\YearlyHealthReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock YearlyAbnormalRateText;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\YearlyHealthReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WrapPanel MonthButtonsPanel;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\YearlyHealthReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MonthlyDetailGrid;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\YearlyHealthReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MonthlyStepsChartTitle;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\YearlyHealthReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel MonthlyStepsDetailPanel;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\YearlyHealthReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MonthlyBloodPressureChartTitle;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\YearlyHealthReportWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel MonthlyBloodPressureDetailPanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WpfAdmin;component/yearlyhealthreportwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\YearlyHealthReportWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            
            #line 22 "..\..\..\YearlyHealthReportWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.YearlyTotalStepsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.YearlyDailyAverageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.YearlyAvgSystolicText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.YearlyAvgDiastolicText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.YearlyBloodPressureStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.YearlyRecordDaysText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.YearlyRecordRateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.YearlyAbnormalDaysText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.YearlyAbnormalRateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.MonthButtonsPanel = ((System.Windows.Controls.WrapPanel)(target));
            return;
            case 13:
            this.MonthlyDetailGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 14:
            this.MonthlyStepsChartTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.MonthlyStepsDetailPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 16:
            this.MonthlyBloodPressureChartTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.MonthlyBloodPressureDetailPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

