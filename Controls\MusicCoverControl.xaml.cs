using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Imaging;

namespace WpfApp.Controls
{
    public partial class MusicCoverControl : UserControl
    {
        public static readonly DependencyProperty CoverUrlProperty =
            DependencyProperty.Register("CoverUrl", typeof(string), typeof(MusicCoverControl),
                new PropertyMetadata(null, OnCoverUrlChanged));

        public static readonly DependencyProperty ImgLinkProperty =
            DependencyProperty.Register("ImgLink", typeof(string), typeof(MusicCoverControl),
                new PropertyMetadata(null, OnImgLinkChanged));

        public string CoverUrl
        {
            get { return (string)GetValue(CoverUrlProperty); }
            set { SetValue(CoverUrlProperty, value); }
        }

        public string ImgLink
        {
            get { return (string)GetValue(ImgLinkProperty); }
            set { SetValue(ImgLinkProperty, value); }
        }

        public MusicCoverControl()
        {
            InitializeComponent();
        }

        private static void OnCoverUrlChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is MusicCoverControl control)
            {
                control.UpdateCoverImage();
            }
        }

        private static void OnImgLinkChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is MusicCoverControl control)
            {
                control.UpdateCoverImage();
            }
        }

        private void UpdateCoverImage()
        {
            try
            {
                // 优先使用 ImgLink
                string imageUrl = !string.IsNullOrEmpty(ImgLink) ? ImgLink : CoverUrl;

                if (string.IsNullOrEmpty(imageUrl))
                {
                    // 设置默认图片
                    CoverImage.Source = new BitmapImage(new Uri("pack://application:,,,/Images/default_poster.jpg", UriKind.Absolute));
                    return;
                }

                // 尝试加载图片
                BitmapImage bitmap = new BitmapImage();
                bitmap.BeginInit();

                // 检查是否是本地文件路径
                if (imageUrl.StartsWith("file:///") || File.Exists(imageUrl))
                {
                    bitmap.UriSource = new Uri(imageUrl, UriKind.RelativeOrAbsolute);
                }
                else
                {
                    bitmap.UriSource = new Uri(imageUrl, UriKind.RelativeOrAbsolute);
                }

                bitmap.CacheOption = BitmapCacheOption.OnLoad;
                bitmap.EndInit();
                CoverImage.Source = bitmap;
            }
            catch (Exception ex)
            {
                // 加载失败时使用默认图片
                System.Diagnostics.Debug.WriteLine($"加载图片失败: {ex.Message}");
                CoverImage.Source = new BitmapImage(new Uri("pack://application:,,,/Images/default_poster.jpg", UriKind.Absolute));
            }
        }
    }
}
