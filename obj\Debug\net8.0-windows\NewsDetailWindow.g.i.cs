﻿#pragma checksum "..\..\..\NewsDetailWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1D52920D5A4DC78243C01BE8234F589D5FF437BB"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using Microsoft.Web.WebView2.Wpf;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace WpfApp {
    
    
    /// <summary>
    /// NewsDetailWindow
    /// </summary>
    public partial class NewsDetailWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 114 "..\..\..\NewsDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WindowTitle;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\NewsDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FullScreenButton;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\NewsDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FullScreenIcon;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\NewsDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\NewsDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NewsTitle;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\NewsDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NewsSource;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\NewsDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NewsTime;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\NewsDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TextModeButton;
        
        #line default
        #line hidden
        
        
        #line 220 "..\..\..\NewsDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button WebModeButton;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\NewsDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer TextModeViewer;
        
        #line default
        #line hidden
        
        
        #line 253 "..\..\..\NewsDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NewsContent;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\NewsDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid LoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 270 "..\..\..\NewsDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border TipPanel;
        
        #line default
        #line hidden
        
        
        #line 283 "..\..\..\NewsDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid WebModeViewer;
        
        #line default
        #line hidden
        
        
        #line 284 "..\..\..\NewsDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Microsoft.Web.WebView2.Wpf.WebView2 NewsWebView;
        
        #line default
        #line hidden
        
        
        #line 287 "..\..\..\NewsDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid WebLoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 310 "..\..\..\NewsDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 337 "..\..\..\NewsDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OpenOriginalButton;
        
        #line default
        #line hidden
        
        
        #line 364 "..\..\..\NewsDetailWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseContentButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WpfAdmin;V1.0.0.0;component/newsdetailwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\NewsDetailWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 104 "..\..\..\NewsDetailWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.TitleBar_MouseDown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.WindowTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.FullScreenButton = ((System.Windows.Controls.Button)(target));
            
            #line 120 "..\..\..\NewsDetailWindow.xaml"
            this.FullScreenButton.Click += new System.Windows.RoutedEventHandler(this.FullScreenButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.FullScreenIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 138 "..\..\..\NewsDetailWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.NewsTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.NewsSource = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.NewsTime = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TextModeButton = ((System.Windows.Controls.Button)(target));
            
            #line 196 "..\..\..\NewsDetailWindow.xaml"
            this.TextModeButton.Click += new System.Windows.RoutedEventHandler(this.TextModeButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.WebModeButton = ((System.Windows.Controls.Button)(target));
            
            #line 221 "..\..\..\NewsDetailWindow.xaml"
            this.WebModeButton.Click += new System.Windows.RoutedEventHandler(this.WebModeButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.TextModeViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 12:
            this.NewsContent = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.LoadingPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 14:
            this.TipPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 15:
            this.WebModeViewer = ((System.Windows.Controls.Grid)(target));
            return;
            case 16:
            this.NewsWebView = ((Microsoft.Web.WebView2.Wpf.WebView2)(target));
            return;
            case 17:
            this.WebLoadingPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 18:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 312 "..\..\..\NewsDetailWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.OpenOriginalButton = ((System.Windows.Controls.Button)(target));
            
            #line 339 "..\..\..\NewsDetailWindow.xaml"
            this.OpenOriginalButton.Click += new System.Windows.RoutedEventHandler(this.OpenOriginalButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.CloseContentButton = ((System.Windows.Controls.Button)(target));
            
            #line 366 "..\..\..\NewsDetailWindow.xaml"
            this.CloseContentButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

