using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media.Imaging;

namespace WpfApp
{
    public class ImageUrlConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            string imageUrl = value as string;

            if (string.IsNullOrEmpty(imageUrl))
            {
                // 返回默认图片
                return new BitmapImage(new Uri("pack://application:,,,/Images/default_poster.jpg", UriKind.Absolute));
            }

            try
            {
                // 尝试加载图片
                if (imageUrl.StartsWith("http://") || imageUrl.StartsWith("https://"))
                {
                    // 网络图片
                    BitmapImage bitmap = new BitmapImage();
                    bitmap.BeginInit();
                    bitmap.UriSource = new Uri(imageUrl);
                    bitmap.CacheOption = BitmapCacheOption.OnLoad;
                    bitmap.EndInit();
                    return bitmap;
                }
                else if (imageUrl.StartsWith("pack://"))
                {
                    // 应用程序资源图片
                    return new BitmapImage(new Uri(imageUrl));
                }
                else
                {
                    // 尝试作为本地文件路径
                    return new BitmapImage(new Uri(imageUrl, UriKind.RelativeOrAbsolute));
                }
            }
            catch
            {
                // 如果加载失败，返回默认图片
                return new BitmapImage(new Uri("pack://application:,,,/Images/default_poster.jpg", UriKind.Absolute));
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
