﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Diagnostics;

namespace WpfApp
{
    public partial class MovieDetailWindow : Window
    {
        private dynamic currentMovie;

        public MovieDetailWindow(dynamic movie)
        {
            InitializeComponent();
            currentMovie = movie;

            // 设置窗口标题
            this.Title = $"电影详情 - {movie.Title}";

            // 填充电影信息
            MovieTitle.Text = movie.Title ?? "未知电影";

            // 设置海报图片
            try
            {
                if (!string.IsNullOrEmpty(movie.PosterUrl))
                {
                    PosterImage.Source = new BitmapImage(new Uri(movie.PosterUrl));
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"海报加载失败: {ex.Message}");
            }

            // 设置评分
            if (movie.Rating != null)
            {
                RatingText.Text = movie.Rating.ToString();
            }
            else
            {
                RatingText.Text = "8.0"; // 默认评分
            }

            // 设置日期 - 可以从movie对象获取，或使用当前日期
            DateText.Text = movie.ReleaseDate ?? DateTime.Now.ToString("yyyy-MM-dd");

            // 设置导演和国家信息
            DirectorText.Text = movie.Director ?? "未知导演";
            CountryText.Text = movie.Country ?? "未知";

            // 设置电影简介
            if (!string.IsNullOrEmpty(movie.Intro))
            {
                GenreText.Text = movie.Intro;
            }

            // 设置获奖信息
            SetAwardsInfo(movie.Awards);
        }

        /// <summary>
        /// 背景点击关闭窗口
        /// </summary>
        private void Background_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            // 确保点击的是背景而不是内容区域
            if (e.Source == sender)
            {
                this.Close();
            }
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// 磁力链接按钮点击事件
        /// </summary>
        private void MagnetButton_Click(object sender, RoutedEventArgs e)
        {
            string magnetLink = currentMovie?.Magnet;
            if (!string.IsNullOrEmpty(magnetLink) && magnetLink != "暂无磁力链接")
            {
                CopyTextToClipboard(magnetLink);
            }
            else
            {
                ShowMessage("暂无磁力链接", "提示");
            }
        }

        /// <summary>
        /// 字幕链接按钮点击事件
        /// </summary>
        private void SubtitleButton_Click(object sender, RoutedEventArgs e)
        {
            string subtitleLink = currentMovie?.Sublink;
            if (!string.IsNullOrEmpty(subtitleLink) && subtitleLink != "暂无字幕链接")
            {
                CopyTextToClipboard(subtitleLink);
            }
            else
            {
                ShowMessage("暂无字幕链接", "提示");
            }
        }

        /// <summary>
        /// 豆瓣链接按钮点击事件
        /// </summary>
        private void DoubanButton_Click(object sender, RoutedEventArgs e)
        {
            string doubanLink = currentMovie?.Douban;
            if (!string.IsNullOrEmpty(doubanLink) && doubanLink != "暂无豆瓣链接")
            {
                CopyTextToClipboard(doubanLink);
            }
            else
            {
                ShowMessage("暂无豆瓣链接", "提示");
            }
        }

        /// <summary>
        /// 设置电影获奖信息
        /// </summary>
        /// <param name="awards">获奖信息字符串</param>
        private void SetAwardsInfo(string awards)
        {
            try
            {
                if (!string.IsNullOrEmpty(awards) && awards.Trim() != "")
                {
                    // 显示获奖标签
                    AwardTag.Visibility = Visibility.Visible;
                    
                    // 设置获奖标签文本（可以根据具体奖项自定义）
                    if (awards.Contains("柏林") || awards.Contains("Berlin"))
                    {
                        AwardTagText.Text = "柏林国际电影节获奖作品";
                    }
                    else if (awards.Contains("戛纳") || awards.Contains("Cannes"))
                    {
                        AwardTagText.Text = "戛纳国际电影节获奖作品";
                    }
                    else if (awards.Contains("威尼斯") || awards.Contains("Venice"))
                    {
                        AwardTagText.Text = "威尼斯国际电影节获奖作品";
                    }
                    else
                    {
                        AwardTagText.Text = "获奖作品";
                    }

                    // 显示详细获奖信息
                    AwardsDetail.Visibility = Visibility.Visible;
                    string formattedAwards = FormatAwardsText(awards);
                    AwardsText.Text = formattedAwards;
                }
                else
                {
                    // 隐藏获奖相关UI
                    AwardTag.Visibility = Visibility.Collapsed;
                    AwardsDetail.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"设置获奖信息失败: {ex.Message}");
                AwardTag.Visibility = Visibility.Collapsed;
                AwardsDetail.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 格式化获奖信息文本
        /// </summary>
        /// <param name="awards">原始获奖信息</param>
        /// <returns>格式化后的获奖信息</returns>
        private string FormatAwardsText(string awards)
        {
            if (string.IsNullOrEmpty(awards))
                return "暂无获奖信息";

            try
            {
                // 将分号、分行符等替换为换行
                string formatted = awards
                    .Replace(";", "\n")
                    .Replace("；", "\n")
                    .Replace("|", "\n")
                    .Replace("｜", "\n")
                    .Replace("\r\n", "\n")
                    .Replace("\r", "\n");

                // 分割成行并清理空行
                var lines = formatted.Split('\n', StringSplitOptions.RemoveEmptyEntries)
                    .Select(line => line.Trim())
                    .Where(line => !string.IsNullOrEmpty(line))
                    .ToArray();

                if (lines.Length == 0)
                    return "暂无获奖信息";

                // 为每个奖项添加项目符号
                var formattedLines = lines.Select((line, index) =>
                {
                    // 如果已经有项目符号或特殊字符，就不再添加
                    if (line.StartsWith("•") || line.StartsWith("●") || line.StartsWith("-") ||
                        line.StartsWith("*") || line.StartsWith("–") || line.StartsWith("—"))
                    {
                        return line;
                    }
                    return $"• {line}";
                });

                return string.Join("\n", formattedLines);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"格式化获奖信息失败: {ex.Message}");
                return awards; // 返回原始文本
            }
        }

        /// <summary>
        /// 复制文本到剪贴板
        /// </summary>
        /// <param name="text">要复制的文本</param>
        private void CopyTextToClipboard(string text)
        {
            if (!string.IsNullOrEmpty(text))
            {
                Task.Run(() =>
                {
                    const int maxRetries = 10;
                    int retryCount = 0;
                    bool success = false;

                    while (!success && retryCount < maxRetries)
                    {
                        try
                        {
                            this.Dispatcher.Invoke(() =>
                            {
                                try
                                {
                                    Clipboard.SetText(text);
                                    success = true;
                                }
                                catch
                                {
                                    try
                                    {
                                        var dataObject = new DataObject(DataFormats.Text, text);
                                        Clipboard.SetDataObject(dataObject, true);
                                        success = true;
                                    }
                                    catch
                                    {
                                        // 重试
                                    }
                                }
                            });

                            if (success)
                            {
                                this.Dispatcher.Invoke(() =>
                                {
                                    ShowMessage("链接已复制到剪贴板", "复制成功");
                                });
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"复制到剪贴板失败 (尝试 {retryCount + 1}/{maxRetries}): {ex.Message}");
                        }

                        retryCount++;
                        if (!success && retryCount < maxRetries)
                        {
                            System.Threading.Thread.Sleep(100);
                        }
                    }

                    if (!success)
                    {
                        this.Dispatcher.Invoke(() =>
                        {
                            ShowMessage("复制到剪贴板失败，请手动复制", "错误");
                        });
                    }
                });
            }
        }

        /// <summary>
        /// 显示消息提示
        /// </summary>
        /// <param name="message">消息内容</param>
        /// <param name="title">标题</param>
        private void ShowMessage(string message, string title)
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, 
                title == "错误" ? MessageBoxImage.Error : MessageBoxImage.Information);
        }

        // 保留旧的方法以确保兼容性
        private void MagnetText_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            MagnetButton_Click(sender, null);
        }

        private void DoubanText_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            DoubanButton_Click(sender, null);
        }

        private void SublinkText_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            SubtitleButton_Click(sender, null);
        }

        private void CopyMagnet_Click(object sender, RoutedEventArgs e)
        {
            if (currentMovie?.Magnet != null)
            {
                CopyTextToClipboard(currentMovie.Magnet);
            }
        }

        private void CopyDouban_Click(object sender, RoutedEventArgs e)
        {
            if (currentMovie?.Douban != null)
            {
                CopyTextToClipboard(currentMovie.Douban);
            }
        }

        private void CopySublink_Click(object sender, RoutedEventArgs e)
        {
            if (currentMovie?.Sublink != null)
            {
                CopyTextToClipboard(currentMovie.Sublink);
            }
        }
    }
}
