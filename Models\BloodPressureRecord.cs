using System;

namespace WpfAdmin.Models
{
    /// <summary>
    /// 血压记录数据模型
    /// </summary>
    public class BloodPressureRecord
    {
        /// <summary>
        /// 记录日期
        /// </summary>
        public DateTime Date { get; set; }

        /// <summary>
        /// 记录时间（具体到分钟）
        /// </summary>
        public DateTime RecordTime { get; set; }

        /// <summary>
        /// 收缩压（高压）
        /// </summary>
        public int SystolicPressure { get; set; }

        /// <summary>
        /// 舒张压（低压）
        /// </summary>
        public int DiastolicPressure { get; set; }

        /// <summary>
        /// 心率（可选）
        /// </summary>
        public int? HeartRate { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// 血压是否异常
        /// </summary>
        public bool IsAbnormal => SystolicPressure > 130 || DiastolicPressure > 90;

        /// <summary>
        /// 血压等级描述
        /// </summary>
        public string BloodPressureLevel
        {
            get
            {
                if (SystolicPressure < 90 || DiastolicPressure < 60)
                    return "偏低";
                else if (SystolicPressure <= 120 && DiastolicPressure <= 80)
                    return "正常";
                else if (SystolicPressure <= 130 && DiastolicPressure <= 85)
                    return "正常偏高";
                else if (SystolicPressure <= 140 && DiastolicPressure <= 90)
                    return "轻度高血压";
                else if (SystolicPressure <= 160 && DiastolicPressure <= 100)
                    return "中度高血压";
                else
                    return "重度高血压";
            }
        }
    }
}
