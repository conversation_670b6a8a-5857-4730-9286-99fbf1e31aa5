<Window x:Class="WpfApp.WeatherWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WpfApp"
        mc:Ignorable="d"
        Title="和风天气" Height="600" Width="800"
        Background="#121a30"
        WindowStartupLocation="CenterScreen"
        WindowStyle="None"
        AllowsTransparency="True"
        BorderThickness="0"
        ResizeMode="CanResize"
        SizeChanged="Window_SizeChanged">
    <Window.Resources>
        <Style x:Key="WeatherIconStyle" TargetType="Image">
            <Setter Property="Width" Value="64"/>
            <Setter Property="Height" Value="64"/>
            <Setter Property="Margin" Value="5"/>
        </Style>
        <Style x:Key="ForecastIconStyle" TargetType="Image">
            <Setter Property="Width" Value="32"/>
            <Setter Property="Height" Value="32"/>
            <Setter Property="Margin" Value="2"/>
        </Style>
        <Style x:Key="WeatherTextStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Margin" Value="5,2"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        <Style x:Key="WeatherTitleStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="24"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="10,5"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
        </Style>
        <Style x:Key="TemperatureTextStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="48"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
        </Style>
        <Style x:Key="ForecastTextStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="TextAlignment" Value="Center"/>
        </Style>
        
        <!-- 天气卡片渐变样式 -->
        <LinearGradientBrush x:Key="WeatherCardGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#233863" Offset="0.0"/>
            <GradientStop Color="#1a2747" Offset="1.0"/>
        </LinearGradientBrush>
        
        <Style x:Key="ForecastCardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource WeatherCardGradient}"/>
            <Setter Property="CornerRadius" Value="10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Padding" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="315" ShadowDepth="3" BlurRadius="5" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 最大化模式下的预报卡片样式 -->
        <Style x:Key="MaximizedForecastCardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource WeatherCardGradient}"/>
            <Setter Property="CornerRadius" Value="15"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="315" ShadowDepth="5" BlurRadius="8" Opacity="0.4"/>
                </Setter.Value>
            </Setter>
        </Style>
        <Style x:Key="WindowButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="30"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Margin" Value="2,0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="0">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#3D8BFF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#2D7BEF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style x:Key="FlatButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#3D8BFF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,5"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#2D7BEF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1D6BDF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <!-- 自定义滚动条样式 -->
        <Style x:Key="CustomScrollBarThumbStyle" TargetType="{x:Type Thumb}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Thumb}">
                        <Grid>
                            <Border Background="#3D8BFF" CornerRadius="3"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="CustomScrollBarStyle" TargetType="{x:Type ScrollBar}">
            <Setter Property="Width" Value="8"/>
            <Setter Property="Background" Value="#1a2747"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ScrollBar}">
                        <Grid>
                            <Border Background="{TemplateBinding Background}" CornerRadius="4"/>
                            <Track x:Name="PART_Track" IsDirectionReversed="True">
                                <Track.Thumb>
                                    <Thumb Style="{StaticResource CustomScrollBarThumbStyle}"/>
                                </Track.Thumb>
                            </Track>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="CustomScrollViewerStyle" TargetType="{x:Type ScrollViewer}">
            <Setter Property="HorizontalScrollBarVisibility" Value="Auto"/>
            <Setter Property="VerticalScrollBarVisibility" Value="Disabled"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ScrollViewer}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <ScrollContentPresenter Grid.Column="0" Grid.Row="0"/>
                            
                            <ScrollBar x:Name="PART_HorizontalScrollBar"
                                      Grid.Column="0" Grid.Row="1"
                                      Orientation="Horizontal"
                                      Value="{TemplateBinding HorizontalOffset}"
                                      Maximum="{TemplateBinding ScrollableWidth}"
                                      ViewportSize="{TemplateBinding ViewportWidth}"
                                      Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                                      Style="{StaticResource CustomScrollBarStyle}"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
    <Border BorderThickness="1" BorderBrush="#1a2747" CornerRadius="8">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- 标题栏 -->
            <Grid Grid.Row="0" Background="#0a1020" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Text="和风天气" Foreground="White" FontSize="16" FontWeight="Bold" 
                           VerticalAlignment="Center" Margin="15,0,0,0" Grid.Column="0"/>
                
                <StackPanel Orientation="Horizontal" Grid.Column="2" HorizontalAlignment="Right">
                    <Button x:Name="MinimizeButton" Style="{StaticResource WindowButtonStyle}" 
                            Content="—" FontSize="14" Click="MinimizeButton_Click"/>
                    <Button x:Name="MaximizeButton" Style="{StaticResource WindowButtonStyle}" 
                            Content="□" FontSize="14" Click="MaximizeButton_Click"/>
                    <Button x:Name="CloseButton" Style="{StaticResource WindowButtonStyle}" 
                            Content="✕" FontSize="14" Click="CloseButton_Click"/>
                </StackPanel>
            </Grid>
            
            <!-- 搜索栏 -->
            <Grid Grid.Row="1" Margin="10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <ComboBox x:Name="CityComboBox" Height="36" Margin="5" VerticalContentAlignment="Center" 
                         Text="西安" Grid.Column="0" Foreground="White" Padding="10,0"
                         BorderThickness="0" Background="#1a2747" FontSize="14" 
                         IsEditable="True">
                    <ComboBox.Resources>
                        <Style TargetType="{x:Type Border}">
                            <Setter Property="CornerRadius" Value="4"/>
                        </Style>
                    </ComboBox.Resources>
                    <ComboBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding}" Foreground="White"/>
                        </DataTemplate>
                    </ComboBox.ItemTemplate>
                    <ComboBox.Style>
                        <Style TargetType="ComboBox">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="ComboBox">
                                        <Grid>
                                            <ToggleButton x:Name="ToggleButton"
                                                        BorderBrush="{TemplateBinding BorderBrush}"
                                                        Background="{TemplateBinding Background}"
                                                        IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                                                <ToggleButton.Template>
                                                    <ControlTemplate TargetType="ToggleButton">
                                                        <Border x:Name="Border" 
                                                                Background="{TemplateBinding Background}"
                                                                BorderBrush="{TemplateBinding BorderBrush}"
                                                                BorderThickness="0"
                                                                CornerRadius="4">
                                                            <Grid>
                                                                <Grid.ColumnDefinitions>
                                                                    <ColumnDefinition Width="*"/>
                                                                    <ColumnDefinition Width="20"/>
                                                                </Grid.ColumnDefinitions>
                                                                <ContentPresenter Grid.Column="0"
                                                                                HorizontalAlignment="Left"
                                                                                Margin="3,0,0,0"/>
                                                                <Path x:Name="Arrow"
                                                                      Grid.Column="1"
                                                                      Fill="White"
                                                                      HorizontalAlignment="Center"
                                                                      VerticalAlignment="Center"
                                                                      Data="M0,0 L4,4 L8,0 Z"/>
                                                            </Grid>
                                                        </Border>
                                                    </ControlTemplate>
                                                </ToggleButton.Template>
                                            </ToggleButton>
                                            <ContentPresenter x:Name="ContentSite"
                                                            Content="{TemplateBinding SelectionBoxItem}"
                                                            ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                            ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                                            VerticalAlignment="Center"
                                                            HorizontalAlignment="Left"
                                                            Margin="10,0,0,0"/>
                                            <TextBox x:Name="PART_EditableTextBox"
                                                     Visibility="Hidden"
                                                     IsReadOnly="{TemplateBinding IsReadOnly}"
                                                     Margin="10,0,20,0"
                                                     Background="Transparent"
                                                     Foreground="White"
                                                     CaretBrush="White"
                                                     BorderThickness="0"
                                                     VerticalAlignment="Center">
                                                <TextBox.Template>
                                                    <ControlTemplate TargetType="TextBox">
                                                        <Border Background="Transparent"
                                                                BorderThickness="0">
                                                            <ScrollViewer x:Name="PART_ContentHost"/>
                                                        </Border>
                                                    </ControlTemplate>
                                                </TextBox.Template>
                                            </TextBox>
                                            <Popup x:Name="Popup"
                                                   Placement="Bottom"
                                                   IsOpen="{TemplateBinding IsDropDownOpen}"
                                                   AllowsTransparency="True"
                                                   Focusable="False"
                                                   PopupAnimation="Slide">
                                                <Grid x:Name="DropDown"
                                                      SnapsToDevicePixels="True"
                                                      MinWidth="{TemplateBinding ActualWidth}"
                                                      MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                                    <Border x:Name="DropDownBorder"
                                                            Background="#1a2747"
                                                            BorderThickness="0"
                                                            CornerRadius="4">
                                                        <ScrollViewer SnapsToDevicePixels="True">
                                                            <StackPanel IsItemsHost="True"/>
                                                        </ScrollViewer>
                                                    </Border>
                                                </Grid>
                                            </Popup>
                                        </Grid>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsEditable" Value="True">
                                                <Setter TargetName="PART_EditableTextBox" Property="Visibility" Value="Visible"/>
                                                <Setter TargetName="ContentSite" Property="Visibility" Value="Hidden"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </ComboBox.Style>
                    <ComboBoxItem Content="西安"/>
                    <ComboBoxItem Content="庆阳"/>
                    <ComboBoxItem Content="北京"/>
                    <ComboBoxItem Content="昆明"/>
                    <ComboBoxItem Content="根河"/>
                    <ComboBoxItem Content="腾冲"/>
                </ComboBox>
                
                <Button x:Name="SearchButton" Content="搜索" Height="36" Margin="5" 
                        Click="SearchButton_Click" Grid.Column="1" Style="{StaticResource FlatButtonStyle}"/>
            </Grid>
            
            <!-- 当前天气信息 -->
            <Border Grid.Row="2" CornerRadius="15" Margin="10">
                <Border.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#2a4173" Offset="0.0"/>
                        <GradientStop Color="#1a2747" Offset="1.0"/>
                    </LinearGradientBrush>
                </Border.Background>
                <Border.Effect>
                    <DropShadowEffect Color="Black" Direction="315" ShadowDepth="4" BlurRadius="8" Opacity="0.3"/>
                </Border.Effect>
                <Grid>
                    <!-- 添加光泽效果 -->
                    <Border Height="60" VerticalAlignment="Top" CornerRadius="15,15,0,0"
                            Opacity="0.15" Background="White">
                        <Border.OpacityMask>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                <GradientStop Color="White" Offset="0.0"/>
                                <GradientStop Color="Transparent" Offset="1.0"/>
                            </LinearGradientBrush>
                        </Border.OpacityMask>
                    </Border>
                    
                    <Grid Margin="15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                    
                        <!-- 天气图标 -->
                        <Image x:Name="WeatherIcon" Grid.Column="0" Style="{StaticResource WeatherIconStyle}"/>
                    
                        <!-- 天气信息 -->
                        <StackPanel Grid.Column="1" Margin="10,0">
                            <TextBlock x:Name="CityText" Text="北京" Style="{StaticResource WeatherTitleStyle}" HorizontalAlignment="Left"/>
                            <TextBlock x:Name="WeatherText" Text="晴" Style="{StaticResource WeatherTextStyle}"/>
                            <TextBlock x:Name="WindText" Text="东北风 3-4级" Style="{StaticResource WeatherTextStyle}"/>
                            <TextBlock x:Name="HumidityText" Text="湿度: 40%" Style="{StaticResource WeatherTextStyle}"/>
                            <TextBlock x:Name="UpdateTimeText" Text="更新时间: 14:30" Style="{StaticResource WeatherTextStyle}" FontSize="12" Foreground="#aaaaaa"/>
                        </StackPanel>
                    
                        <!-- 温度 -->
                        <StackPanel Grid.Column="2" VerticalAlignment="Center">
                            <TextBlock x:Name="TemperatureText" Text="25°C" Style="{StaticResource TemperatureTextStyle}"/>
                        </StackPanel>
                    </Grid>
                </Grid>
            </Border>
            
            <!-- 未来天气预报 -->
            <Grid Grid.Row="3" Margin="10">
                <!-- 标准模式的横向滚动预报 -->
                <ScrollViewer x:Name="StandardForecastScrollViewer" Style="{StaticResource CustomScrollViewerStyle}"
                              Visibility="Visible">
                    <StackPanel x:Name="ForecastPanel" Orientation="Horizontal" HorizontalAlignment="Center">
                        <!-- 预报卡片将在代码中动态添加 -->
                    </StackPanel>
                </ScrollViewer>

                <!-- 最大化模式的网格预报 -->
                <Grid x:Name="MaximizedForecastGrid" Visibility="Collapsed">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <!-- 7天预报卡片将在代码中动态添加到各列 -->
                </Grid>
            </Grid>
            
            <!-- 底部状态栏 -->
            <Border Grid.Row="4" Background="#0a1020" Height="40">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Text="数据来源: 和风天气" Foreground="#aaaaaa" VerticalAlignment="Center" Margin="10,0"/>
                    
                    <Button x:Name="DownloadIconsButton" Grid.Column="1" Content="下载天气图标" 
                            Margin="10,5" Style="{StaticResource FlatButtonStyle}"/>
                </Grid>
            </Border>
            
            <!-- 加载指示器 -->
            <Grid x:Name="LoadingPanel" Grid.RowSpan="5" Background="#80000000" Visibility="Collapsed">
                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                    <TextBlock x:Name="LoadingText" Text="正在加载天气数据..." Foreground="White" FontSize="18" Margin="0,0,0,10"/>
                    <ProgressBar x:Name="LoadingProgressBar" Width="200" Height="10" IsIndeterminate="True"/>
                </StackPanel>
            </Grid>
        </Grid>
    </Border>
</Window> 