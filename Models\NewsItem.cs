using System;

namespace WpfApp.Models
{
    /// <summary>
    /// 新闻项目数据模型
    /// </summary>
    public class NewsItem
    {
        /// <summary>
        /// 新闻标题
        /// </summary>
        public string Title { get; set; }

        /// <summary>
        /// 新闻链接
        /// </summary>
        public string Url { get; set; }

        /// <summary>
        /// 新闻摘要
        /// </summary>
        public string Summary { get; set; }

        /// <summary>
        /// 发布时间
        /// </summary>
        public DateTime PublishTime { get; set; }

        /// <summary>
        /// 新闻来源
        /// </summary>
        public string Source { get; set; } = "凤凰网";

        /// <summary>
        /// 新闻分类
        /// </summary>
        public string Category { get; set; } = "国际新闻";

        /// <summary>
        /// 新闻内容（详细内容）
        /// </summary>
        public string Content { get; set; }

        /// <summary>
        /// 新闻图片URL
        /// </summary>
        public string ImageUrl { get; set; }

        /// <summary>
        /// 是否已读
        /// </summary>
        public bool IsRead { get; set; } = false;

        /// <summary>
        /// 新闻ID（用于去重）
        /// </summary>
        public string NewsId { get; set; }
    }
}
