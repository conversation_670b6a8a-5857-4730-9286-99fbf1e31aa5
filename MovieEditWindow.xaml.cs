﻿﻿using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Imaging;
using Supabase.Postgrest.Models;
using Supabase.Postgrest;

namespace WpfApp
{
    public partial class MovieEditWindow : Window
    {
        private readonly Supabase.Client _supabaseClient;
        private readonly MovieItem _movieItem;
        private bool _isDataChanged = false;

        public MovieEditWindow(MovieItem movieItem, Supabase.Client supabaseClient)
        {
            InitializeComponent();

            _supabaseClient = supabaseClient;
            _movieItem = movieItem;

            // 设置窗口标题
            this.Title = $"编辑电影信息 - {movieItem.Title}";
            MovieTitle.Text = $"编辑电影信息 - {movieItem.Title}";

            // 填充电影信息
            LoadMovieData();

            // 添加文本框内容变更事件处理
            IntroTextBox.TextChanged += TextBox_TextChanged;
            MagnetTextBox.TextChanged += TextBox_TextChanged;
            DoubanTextBox.TextChanged += TextBox_TextChanged;
            SublinkTextBox.TextChanged += TextBox_TextChanged;
            AwardsTextBox.TextChanged += TextBox_TextChanged;
        }

        private void LoadMovieData()
        {
            // 设置海报图片
            try
            {
                if (!string.IsNullOrEmpty(_movieItem.PosterUrl))
                {
                    PosterImage.Source = new BitmapImage(new Uri(_movieItem.PosterUrl));
                }
            }
            catch (Exception ex)
            {
                // 图片加载失败时的处理
                MessageBox.Show($"海报加载失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }

            // 设置其他信息
            RatingText.Text = _movieItem.Rating?.ToString() ?? "暂无评分";
            DirectorTextBox.Text = _movieItem.Director ?? "未知";
            CountryTextBox.Text = _movieItem.Country ?? "未知";
            IntroTextBox.Text = _movieItem.Intro ?? "";
            MagnetTextBox.Text = _movieItem.Magnet ?? "";
            DoubanTextBox.Text = _movieItem.Douban ?? "";
            SublinkTextBox.Text = _movieItem.Sublink ?? "";
            AwardsTextBox.Text = _movieItem.Awards ?? "";
        }

        private void TextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            _isDataChanged = true;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isDataChanged)
            {
                var result = MessageBox.Show("您有未保存的更改，确定要放弃吗？", "确认", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.No)
                {
                    return;
                }
            }
            this.DialogResult = false;
            this.Close();
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 禁用保存按钮，防止重复点击
                var saveButton = sender as Button;
                if (saveButton != null)
                {
                    saveButton.IsEnabled = false;
                }

                // 更新电影信息
                _movieItem.Intro = IntroTextBox.Text;
                _movieItem.Magnet = MagnetTextBox.Text;
                _movieItem.Douban = DoubanTextBox.Text;
                _movieItem.Sublink = SublinkTextBox.Text;

                // 保存到数据库
                // 调试输出
                Console.WriteLine($"开始更新电影，ID: {_movieItem.Id}, 标题: {_movieItem.Title}");
                Console.WriteLine($"当前电影字段值 - 简介: {_movieItem.Intro}, 磁力链接: {_movieItem.Magnet}, 豆瓣链接: {_movieItem.Douban}, 字幕链接: {_movieItem.Sublink}");
                Console.WriteLine($"新电影字段值 - 简介: {IntroTextBox.Text}, 磁力链接: {MagnetTextBox.Text}, 豆瓣链接: {DoubanTextBox.Text}, 字幕链接: {SublinkTextBox.Text}");

                try
                {
                    // 直接使用当前电影的ID进行更新
                    // 这样即使有多部相同标题的电影，也能确保更新正确的记录
                    int movieId = _movieItem.Id;

                    // 检查ID是否有效
                    if (movieId <= 0)
                    {
                        // 如果ID无效，尝试通过标题查询
                        Console.WriteLine($"电影ID无效: {movieId}，尝试通过标题查询");
                        var movieQuery = await _supabaseClient
                            .From<MovieItem>()
                            .Where(x => x.Title == _movieItem.Title)
                            .Get();

                        if (movieQuery.Models.Count == 0)
                        {
                            Console.WriteLine($"无法找到电影《{_movieItem.Title}》的记录");
                            throw new Exception($"无法找到电影《{_movieItem.Title}》的记录");
                        }
                        else if (movieQuery.Models.Count > 1)
                        {
                            // 如果找到多个相同标题的电影，显示警告
                            Console.WriteLine($"找到多个相同标题的电影，使用第一个记录");
                            MessageBox.Show($"注意: 数据库中存在{movieQuery.Models.Count}部相同标题的电影，将更新第一部。", "警告", MessageBoxButton.OK, MessageBoxImage.Warning);
                        }

                        // 使用查询到的第一个电影的ID
                        movieId = movieQuery.Models[0].Id;
                        Console.WriteLine($"查询到电影ID: {movieId}");
                    }

                    // 执行更新
                    Console.WriteLine($"开始执行更新操作，电影ID: {movieId}");
                    var response = await _supabaseClient
                        .From<MovieItem>()
                        .Where(x => x.Id == movieId)
                        .Set(x => x.Intro, IntroTextBox.Text)
                        .Set(x => x.Magnet, MagnetTextBox.Text)
                        .Set(x => x.Douban, DoubanTextBox.Text)
                        .Set(x => x.Sublink, SublinkTextBox.Text)
                        .Set(x => x.Awards, AwardsTextBox.Text)
                        .Update();

                    Console.WriteLine($"更新操作完成，响应状态: {response?.ResponseMessage?.StatusCode}");

                    // 更新成功通知用户
                    MessageBox.Show("电影信息更新成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    DialogResult = true;
                    this.Close();
                }
                catch (Exception updateEx)
                {
                    throw new Exception($"更新操作失败: {updateEx.Message}", updateEx);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存过程中发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                var saveButton = sender as Button;
                if (saveButton != null)
                {
                    saveButton.IsEnabled = true;
                }
            }
        }
    }
}
