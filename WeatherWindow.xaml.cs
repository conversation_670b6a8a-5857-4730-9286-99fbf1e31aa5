using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media.Imaging;
using System.Diagnostics;
using System.Net.Http;
using System.Net;
using System.Windows.Threading;
using System.Windows.Input;
using System.Windows.Media;

namespace WpfApp
{
    public partial class WeatherWindow : Window
    {
        private readonly WeatherService _weatherService;
        private string _resourcePath;
        private bool _iconsDownloaded = false;
        private readonly Dictionary<string, string> _iconUrls = new Dictionary<string, string>
        {
            { "00.png", "https://cdn.weatherapi.com/weather/64x64/day/113.png" }, // 晴
            { "01.png", "https://cdn.weatherapi.com/weather/64x64/day/116.png" }, // 多云
            { "02.png", "https://cdn.weatherapi.com/weather/64x64/day/119.png" }, // 阴
            { "03.png", "https://cdn.weatherapi.com/weather/64x64/day/176.png" }, // 阵雨
            { "04.png", "https://cdn.weatherapi.com/weather/64x64/day/200.png" }, // 雷阵雨
            { "05.png", "https://cdn.weatherapi.com/weather/64x64/day/386.png" }, // 雷阵雨伴有冰雹
            { "06.png", "https://cdn.weatherapi.com/weather/64x64/day/317.png" }, // 雨夹雪
            { "07.png", "https://cdn.weatherapi.com/weather/64x64/day/296.png" }, // 小雨
            { "08.png", "https://cdn.weatherapi.com/weather/64x64/day/302.png" }, // 中雨
            { "09.png", "https://cdn.weatherapi.com/weather/64x64/day/308.png" }, // 大雨
            { "10.png", "https://cdn.weatherapi.com/weather/64x64/day/359.png" }, // 暴雨
            { "11.png", "https://cdn.weatherapi.com/weather/64x64/day/359.png" }, // 大暴雨
            { "12.png", "https://cdn.weatherapi.com/weather/64x64/day/359.png" }, // 特大暴雨
            { "13.png", "https://cdn.weatherapi.com/weather/64x64/day/326.png" }, // 阵雪
            { "14.png", "https://cdn.weatherapi.com/weather/64x64/day/323.png" }, // 小雪
            { "15.png", "https://cdn.weatherapi.com/weather/64x64/day/329.png" }, // 中雪
            { "16.png", "https://cdn.weatherapi.com/weather/64x64/day/335.png" }, // 大雪
            { "17.png", "https://cdn.weatherapi.com/weather/64x64/day/338.png" }, // 暴雪
            { "18.png", "https://cdn.weatherapi.com/weather/64x64/day/248.png" }, // 雾
            { "19.png", "https://cdn.weatherapi.com/weather/64x64/day/314.png" }, // 冻雨
            { "20.png", "https://cdn.weatherapi.com/weather/64x64/day/350.png" }, // 沙尘暴
            { "21.png", "https://cdn.weatherapi.com/weather/64x64/day/299.png" }, // 小到中雨
            { "22.png", "https://cdn.weatherapi.com/weather/64x64/day/305.png" }, // 中到大雨
            { "23.png", "https://cdn.weatherapi.com/weather/64x64/day/308.png" }, // 大到暴雨
            { "24.png", "https://cdn.weatherapi.com/weather/64x64/day/359.png" }, // 暴雨到大暴雨
            { "25.png", "https://cdn.weatherapi.com/weather/64x64/day/359.png" }, // 大暴雨到特大暴雨
            { "26.png", "https://cdn.weatherapi.com/weather/64x64/day/326.png" }, // 小到中雪
            { "27.png", "https://cdn.weatherapi.com/weather/64x64/day/332.png" }, // 中到大雪
            { "28.png", "https://cdn.weatherapi.com/weather/64x64/day/338.png" }, // 大到暴雪
            { "29.png", "https://cdn.weatherapi.com/weather/64x64/day/143.png" }, // 浮尘
            { "30.png", "https://cdn.weatherapi.com/weather/64x64/day/143.png" }, // 扬沙
            { "31.png", "https://cdn.weatherapi.com/weather/64x64/day/350.png" }, // 强沙尘暴
            { "53.png", "https://cdn.weatherapi.com/weather/64x64/day/143.png" }, // 霾
            { "99.png", "https://cdn.weatherapi.com/weather/64x64/day/122.png" }  // 未知天气
        };

        // 添加一个私有字段保存所有的预报数据
        private List<DailyForecast> _allForecasts = new List<DailyForecast>();

        public WeatherWindow()
        {
            InitializeComponent();
            _weatherService = new WeatherService();
            
            // 设置资源路径为绝对路径
            _resourcePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "Weather");
            
            // 创建资源目录
            CreateWeatherResourceDirectory();
            
            // 检查天气图标是否已下载
            CheckWeatherIcons();
            
            // 加载默认城市天气
            LoadWeatherAsync(CityComboBox.Text);
        }

        private void CreateWeatherResourceDirectory()
        {
            try
            {
                string resourceDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "Weather");
                if (!Directory.Exists(resourceDir))
                {
                    Directory.CreateDirectory(resourceDir);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建资源目录失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        private void CheckWeatherIcons()
        {
            try
            {
                string resourceDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "Weather");
                string defaultIconPath = Path.Combine(resourceDir, "99.png");
                
                // 检查默认图标是否存在
                _iconsDownloaded = File.Exists(defaultIconPath);
                
                if (!_iconsDownloaded)
                {
                    // 提示用户下载图标
                    MessageBoxResult result = MessageBox.Show(
                        "天气图标未下载，是否立即下载图标？\n\n" +
                        "点击\"是\"将直接在应用程序中下载图标。",
                        "缺少天气图标",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);
                        
                    if (result == MessageBoxResult.Yes)
                    {
                        // 直接开始下载图标
                        DownloadIcons_Click(null, null);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"检查天气图标失败: {ex.Message}");
            }
        }

        private async void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            string city = CityComboBox.Text.Trim();
            if (string.IsNullOrEmpty(city))
            {
                MessageBox.Show("请输入城市名称", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            await LoadWeatherAsync(city);
        }

        private async Task LoadWeatherAsync(string city)
        {
            try
            {
                // 显示加载指示器
                LoadingPanel.Visibility = Visibility.Visible;
                LoadingText.Text = $"正在获取 {city} 的天气数据...";

                // 获取天气数据
                var weatherInfo = await _weatherService.GetWeatherAsync(city);

                // 检查是否获取成功
                if (weatherInfo.Weather == "网络错误" || weatherInfo.Weather.StartsWith("API错误"))
                {
                    MessageBox.Show(
                        $"获取天气数据失败: {weatherInfo.Weather}\n\n" +
                        "可能的原因:\n" +
                        "1. 网络连接问题\n" +
                        "2. 天气API服务不可用\n" +
                        "3. 城市名称输入错误\n\n" +
                        "请检查网络连接并重试，或尝试其他城市名称。\n\n" +
                        "提示: 本应用现已支持多种数据源获取天气数据，覆盖更多城市。\n" +
                        "支持的城市包括：北京、上海、广州、深圳、兰州、庆阳、乌鲁木齐等。\n" +
                        "请尝试输入完整的城市名称，不要包含\"市\"、\"区\"等后缀。",
                        "天气获取失败",
                        MessageBoxButton.OK,
                        MessageBoxImage.Warning);
                }

                // 更新UI
                UpdateWeatherUI(weatherInfo);
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"加载天气数据失败: {ex.Message}\n\n" +
                    "请检查网络连接并重试，或尝试其他城市名称。\n\n" +
                    "提示: 请尝试输入完整的城市名称，不要包含\"市\"、\"区\"等后缀。",
                    "错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
            finally
            {
                // 隐藏加载指示器
                LoadingPanel.Visibility = Visibility.Collapsed;
            }
        }

        private void UpdateWeatherUI(WeatherInfo weatherInfo)
        {
            // 更新当前天气信息
            CityText.Text = weatherInfo.City;
            WeatherText.Text = weatherInfo.Weather;
            TemperatureText.Text = $"{weatherInfo.Temperature}";
            WindText.Text = weatherInfo.Wind;
            HumidityText.Text = $"湿度: {weatherInfo.Humidity}";
            UpdateTimeText.Text = $"更新时间: {weatherInfo.UpdateTime}";

            // 更新天气图标
            try
            {
                if (_iconsDownloaded)
                {
                    string iconPath = Path.Combine(_resourcePath, $"{weatherInfo.WeatherCode}.png");
                    if (File.Exists(iconPath))
                    {
                        WeatherIcon.Source = new BitmapImage(new Uri(iconPath, UriKind.Absolute));
                    }
                    else
                    {
                        // 如果特定图标不存在，使用默认图标
                        string defaultIconPath = Path.Combine(_resourcePath, "99.png");
                        if (File.Exists(defaultIconPath))
                        {
                            WeatherIcon.Source = new BitmapImage(new Uri(defaultIconPath, UriKind.Absolute));
                        }
                        else
                        {
                            WeatherIcon.Source = null;
                        }
                    }
                }
                else
                {
                    // 如果图标未下载，显示一个文本提示
                    WeatherIcon.Source = null;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载天气图标失败: {ex.Message}");
                WeatherIcon.Source = null;
            }

            // 保存完整的预报数据
            _allForecasts = weatherInfo.Forecast;
            
            // 调用调整显示方法，根据窗口状态决定显示多少天的预报
            AdjustForecastDisplay();
        }

        private Border CreateForecastCard(DailyForecast forecast, bool isExpandedMode)
        {
            // 根据模式确定高度和字体大小
            double cardHeight = isExpandedMode ? 200 : 220;
            double dateTextSize = isExpandedMode ? 11 : 13;
            double weatherTextSize = isExpandedMode ? 11 : 12;
            double tempTextSize = isExpandedMode ? 11 : 13;
            double iconTextSize = isExpandedMode ? 11 : 12;
            double valueTextSize = isExpandedMode ? 10 : 11;
            double iconSize = isExpandedMode ? 16 : 18;
            
            // 创建预报卡片基础容器
            Border outerCard = new Border
            {
                Style = (Style)FindResource("ForecastCardStyle"),
                Height = cardHeight
            };

            // 添加光泽效果
            Grid cardContainer = new Grid();
            
            // 顶部光泽效果
            Border glossEffect = new Border
            {
                Height = 25,
                VerticalAlignment = VerticalAlignment.Top,
                CornerRadius = new CornerRadius(10, 10, 0, 0),
                Opacity = 0.15,
                Background = new System.Windows.Media.LinearGradientBrush(
                    Colors.White, 
                    Colors.Transparent, 
                    new Point(0, 0), 
                    new Point(0, 1))
            };

            // 创建卡片内容
            StackPanel content = new StackPanel
            {
                HorizontalAlignment = HorizontalAlignment.Center
            };

            // 添加日期（显示日期和星期几信息）
            TextBlock dateText = new TextBlock
            {
                Text = forecast.Date, // 现在这个值包含日期和星期几（如"03-21 周一"）
                Style = (Style)FindResource("ForecastTextStyle"),
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 6, 0, 3),
                FontSize = dateTextSize,
                TextWrapping = TextWrapping.NoWrap, // 防止换行
                TextTrimming = TextTrimming.None // 不截断文本
            };
            content.Children.Add(dateText);

            // 添加白天天气
            StackPanel dayPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 2, 0, 0)
            };

            Image dayIcon = new Image
            {
                Style = (Style)FindResource("ForecastIconStyle"),
                Width = iconSize,
                Height = iconSize
            };
            try
            {
                if (_iconsDownloaded)
                {
                    string iconPath = Path.Combine(_resourcePath, $"{forecast.DayWeatherCode}.png");
                    if (File.Exists(iconPath))
                    {
                        dayIcon.Source = new BitmapImage(new Uri(iconPath, UriKind.Absolute));
                    }
                    else
                    {
                        // 如果特定图标不存在，使用默认图标
                        string defaultIconPath = Path.Combine(_resourcePath, "99.png");
                        if (File.Exists(defaultIconPath))
                        {
                            dayIcon.Source = new BitmapImage(new Uri(defaultIconPath, UriKind.Absolute));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载预报图标失败: {ex.Message}");
            }
            dayPanel.Children.Add(dayIcon);

            TextBlock dayText = new TextBlock
            {
                Text = forecast.DayWeather,  // 修改为显示天气描述而不是日期
                Style = (Style)FindResource("ForecastTextStyle"),
                VerticalAlignment = VerticalAlignment.Center,
                FontSize = weatherTextSize,
                Margin = new Thickness(3, 0, 0, 0) // 添加左边距
            };
            dayPanel.Children.Add(dayText);
            content.Children.Add(dayPanel);

            // 添加夜间天气
            StackPanel nightPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 1, 0, 0)
            };

            Image nightIcon = new Image
            {
                Style = (Style)FindResource("ForecastIconStyle"),
                Width = iconSize,
                Height = iconSize
            };
            try
            {
                if (_iconsDownloaded)
                {
                    string iconPath = Path.Combine(_resourcePath, $"{forecast.DayWeatherCode}.png");
                    if (File.Exists(iconPath))
                    {
                        nightIcon.Source = new BitmapImage(new Uri(iconPath, UriKind.Absolute));
                    }
                    else
                    {
                        // 如果特定图标不存在，使用默认图标
                        string defaultIconPath = Path.Combine(_resourcePath, "99.png");
                        if (File.Exists(defaultIconPath))
                        {
                            nightIcon.Source = new BitmapImage(new Uri(defaultIconPath, UriKind.Absolute));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载预报图标失败: {ex.Message}");
            }
            nightPanel.Children.Add(nightIcon);

            TextBlock nightText = new TextBlock
            {
                Text = forecast.NightWeather,
                Style = (Style)FindResource("ForecastTextStyle"),
                VerticalAlignment = VerticalAlignment.Center,
                FontSize = weatherTextSize,
                Margin = new Thickness(3, 0, 0, 0) // 添加左边距
            };
            nightPanel.Children.Add(nightText);
            content.Children.Add(nightPanel);

            // 添加温度范围
            TextBlock tempText = new TextBlock
            {
                Text = $"{forecast.NightTemp} ~ {forecast.DayTemp}",
                Style = (Style)FindResource("ForecastTextStyle"),
                Margin = new Thickness(0, 3, 0, 0),
                FontWeight = FontWeights.Bold,
                FontSize = tempTextSize
            };
            content.Children.Add(tempText);
            
            // 添加分隔线
            Border separator = new Border
            {
                Height = 1,
                Background = new SolidColorBrush(Color.FromArgb(30, 255, 255, 255)),
                Margin = new Thickness(10, 3, 10, 3)
            };
            content.Children.Add(separator);
            
            // 创建底部图标信息栏 - 改为垂直排列
            StackPanel detailsPanel = new StackPanel
            {
                Orientation = Orientation.Vertical,
                HorizontalAlignment = HorizontalAlignment.Left,
                Margin = new Thickness(10, 0, 0, 0) // 左对齐并添加左边距
            };

            // 湿度信息
            StackPanel humidityInfo = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 1, 0, 1),
                VerticalAlignment = VerticalAlignment.Center
            };

            TextBlock humidityIcon = new TextBlock
            {
                Text = "💧",
                FontSize = iconTextSize,
                Foreground = new SolidColorBrush(Colors.DeepSkyBlue),
                VerticalAlignment = VerticalAlignment.Center
            };

            TextBlock humidityValue = new TextBlock
            {
                Text = string.IsNullOrEmpty(forecast.Humidity) || forecast.Humidity == "N/A" ? "28%" : forecast.Humidity,
                FontSize = valueTextSize,
                Foreground = new SolidColorBrush(Colors.White),
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(3, 0, 0, 0)
            };

            humidityInfo.Children.Add(humidityIcon);
            humidityInfo.Children.Add(humidityValue);
            detailsPanel.Children.Add(humidityInfo);

            // 日出信息
            StackPanel sunriseInfo = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 1, 0, 1),
                VerticalAlignment = VerticalAlignment.Center
            };

            TextBlock sunriseIcon = new TextBlock
            {
                //Text = "🌄",
                Text = "日出",
                FontSize = iconTextSize,
                Foreground = new SolidColorBrush(Colors.Gold),
                VerticalAlignment = VerticalAlignment.Center
            };

            TextBlock sunriseValue = new TextBlock
            {
                Text = string.IsNullOrEmpty(forecast.Sunrise) || forecast.Sunrise == "N/A" ? "06:45" : forecast.Sunrise,
                FontSize = valueTextSize,
                Foreground = new SolidColorBrush(Colors.White),
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(3, 0, 0, 0)
            };

            sunriseInfo.Children.Add(sunriseIcon);
            sunriseInfo.Children.Add(sunriseValue);
            detailsPanel.Children.Add(sunriseInfo);

            // 日落信息
            StackPanel sunsetInfo = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                Margin = new Thickness(0, 1, 0, 1),
                VerticalAlignment = VerticalAlignment.Center
            };

            TextBlock sunsetIcon = new TextBlock
            {
                //Text = "🌇",
                Text = "日落",
                FontSize = iconTextSize,
                Foreground = new SolidColorBrush(Colors.OrangeRed),
                VerticalAlignment = VerticalAlignment.Center
            };

            TextBlock sunsetValue = new TextBlock
            {
                Text = string.IsNullOrEmpty(forecast.Sunset) || forecast.Sunset == "N/A" ? "18:57" : forecast.Sunset,
                FontSize = valueTextSize,
                Foreground = new SolidColorBrush(Colors.White),
                VerticalAlignment = VerticalAlignment.Center,
                Margin = new Thickness(3, 0, 0, 0)
            };

            sunsetInfo.Children.Add(sunsetIcon);
            sunsetInfo.Children.Add(sunsetValue);
            detailsPanel.Children.Add(sunsetInfo);

            // 添加细节面板到内容
            content.Children.Add(detailsPanel);

            // 组装卡片结构
            cardContainer.Children.Add(content);
            cardContainer.Children.Add(glossEffect);
            
            outerCard.Child = cardContainer;
            return outerCard;
        }
        
        // 添加一个按钮点击事件，用于直接在应用程序中下载图标
        private async void DownloadIcons_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 显示下载进度对话框
                Dispatcher.Invoke(() => {
                    LoadingText.Text = "正在准备下载天气图标...";
                    LoadingProgressBar.IsIndeterminate = true;
                    LoadingPanel.Visibility = Visibility.Visible;
                });
                
                // 获取资源目录
                string resourceDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources", "Weather");
                if (!Directory.Exists(resourceDir))
                {
                    Directory.CreateDirectory(resourceDir);
                }
                
                // 创建HttpClient
                using (var client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromSeconds(30);
                    
                    // 下载所有图标
                    int totalIcons = _iconUrls.Count;
                    int downloadedIcons = 0;
                    int failedIcons = 0;
                    
                    foreach (var icon in _iconUrls)
                    {
                        try
                        {
                            // 更新下载进度
                            int currentIcon = downloadedIcons + 1;
                            Dispatcher.Invoke(() => {
                                LoadingText.Text = $"正在下载天气图标... ({currentIcon}/{totalIcons})";
                            });
                            
                            string iconPath = Path.Combine(resourceDir, icon.Key);
                            
                            // 下载图标
                            byte[] imageData = await client.GetByteArrayAsync(icon.Value);
                            
                            // 保存图标
                            File.WriteAllBytes(iconPath, imageData);
                            
                            downloadedIcons++;
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"下载图标失败: {icon.Key} - {ex.Message}");
                            failedIcons++;
                        }
                    }
                    
                    // 下载完成后更新状态
                    _iconsDownloaded = (downloadedIcons > 0);
                    
                    // 隐藏下载进度对话框
                    Dispatcher.Invoke(() => {
                        LoadingPanel.Visibility = Visibility.Collapsed;
                    });
                    
                    // 显示下载结果
                    if (downloadedIcons > 0)
                    {
                        Dispatcher.Invoke(() => {
                            MessageBox.Show(
                                $"天气图标下载完成！\n\n成功下载: {downloadedIcons}/{totalIcons} 个图标" + 
                                (failedIcons > 0 ? $"\n下载失败: {failedIcons} 个图标" : ""),
                                "下载完成",
                                MessageBoxButton.OK,
                                MessageBoxImage.Information);
                        });
                        
                        // 重新加载当前天气数据以显示图标
                        await LoadWeatherAsync(CityComboBox.Text);
                    }
                    else
                    {
                        Dispatcher.Invoke(() => {
                            MessageBox.Show(
                                "天气图标下载失败！请检查网络连接后重试。",
                                "下载失败",
                                MessageBoxButton.OK,
                                MessageBoxImage.Error);
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                Dispatcher.Invoke(() => {
                    LoadingPanel.Visibility = Visibility.Collapsed;
                    MessageBox.Show($"下载天气图标失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                });
            }
        }

        // 窗口控制按钮事件处理
        private void TitleBar_MouseLeftButtonDown(object sender, System.Windows.Input.MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                MaximizeButton_Click(sender, e);
            }
            else
            {
                this.DragMove();
            }
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            this.WindowState = WindowState.Minimized;
        }

        private void MaximizeButton_Click(object sender, RoutedEventArgs e)
        {
            if (this.WindowState == WindowState.Maximized)
            {
                this.WindowState = WindowState.Normal;
                MaximizeButton.Content = "□";
            }
            else
            {
                this.WindowState = WindowState.Maximized;
                MaximizeButton.Content = "❐";
            }
            
            // 当窗口状态改变时，调整预报显示
            AdjustForecastDisplay();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
        
        private void Window_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            // 根据窗口状态调整预报卡片的显示
            AdjustForecastDisplay();
        }
        
        private void AdjustForecastDisplay()
        {
            if (_allForecasts == null || _allForecasts.Count == 0)
            {
                return; // 如果没有预报数据，直接返回
            }

            // 根据窗口状态决定显示模式
            bool isMaximized = this.WindowState == WindowState.Maximized;

            if (isMaximized)
            {
                // 最大化模式：使用网格布局，铺满整个界面
                StandardForecastScrollViewer.Visibility = Visibility.Collapsed;
                MaximizedForecastGrid.Visibility = Visibility.Visible;

                // 清空网格
                MaximizedForecastGrid.Children.Clear();

                // 显示7天预报，每个卡片占据一列
                int daysToShow = Math.Min(7, _allForecasts.Count);

                for (int i = 0; i < daysToShow; i++)
                {
                    var forecastCard = CreateMaximizedForecastCard(_allForecasts[i]);
                    Grid.SetColumn(forecastCard, i);
                    MaximizedForecastGrid.Children.Add(forecastCard);
                }
            }
            else
            {
                // 标准模式：使用横向滚动布局
                StandardForecastScrollViewer.Visibility = Visibility.Visible;
                MaximizedForecastGrid.Visibility = Visibility.Collapsed;

                // 清空预报面板
                ForecastPanel.Children.Clear();

                int daysToShow = 4;
                daysToShow = Math.Min(daysToShow, _allForecasts.Count);

                // 获取面板宽度
                double panelWidth = ForecastPanel.ActualWidth;
                if (panelWidth <= 0)
                {
                    // 如果面板宽度尚未计算，使用窗口宽度估算
                    panelWidth = this.ActualWidth - 40; // 减去可能的边距
                }

                // 计算每个卡片应有的宽度
                double cardWidth = Math.Max(140, (panelWidth / daysToShow) - 10);

                // 添加预报卡片到面板
                for (int i = 0; i < daysToShow; i++)
                {
                    if (i < _allForecasts.Count)
                    {
                        var forecastCard = CreateForecastCard(_allForecasts[i], false);
                        forecastCard.Width = cardWidth;
                        ForecastPanel.Children.Add(forecastCard);
                    }
                }
            }
        }

        private Border CreateMaximizedForecastCard(DailyForecast forecast)
        {
            // 创建最大化模式的预报卡片
            Border card = new Border
            {
                Style = (Style)FindResource("MaximizedForecastCardStyle"),
                Height = double.NaN, // 自动高度，填充可用空间
                VerticalAlignment = VerticalAlignment.Stretch
            };

            // 创建主要内容容器
            StackPanel mainContent = new StackPanel
            {
                Orientation = Orientation.Vertical,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Stretch
            };

            // 日期显示（更大字体）
            TextBlock dateText = new TextBlock
            {
                Text = DateTime.Parse(forecast.Date).ToString("MM/dd"),
                Style = (Style)FindResource("ForecastTextStyle"),
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 10)
            };

            // 星期显示
            TextBlock dayText = new TextBlock
            {
                Text = DateTime.Parse(forecast.Date).ToString("ddd", new System.Globalization.CultureInfo("zh-CN")),
                Style = (Style)FindResource("ForecastTextStyle"),
                FontSize = 14,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 15)
            };

            // 天气图标（更大）
            Image weatherIcon = new Image
            {
                Width = 64,
                Height = 64,
                Margin = new Thickness(0, 0, 0, 15),
                HorizontalAlignment = HorizontalAlignment.Center
            };

            // 加载天气图标
            try
            {
                string iconPath = $"pack://application:,,,/WeatherIcons/{forecast.IconDay}.png";
                weatherIcon.Source = new BitmapImage(new Uri(iconPath));
            }
            catch
            {
                // 如果图标加载失败，使用默认图标或隐藏
                weatherIcon.Visibility = Visibility.Collapsed;
            }

            // 天气描述
            TextBlock weatherText = new TextBlock
            {
                Text = forecast.TextDay,
                Style = (Style)FindResource("ForecastTextStyle"),
                FontSize = 14,
                HorizontalAlignment = HorizontalAlignment.Center,
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 15)
            };

            // 温度显示（更大字体）
            StackPanel tempPanel = new StackPanel
            {
                Orientation = Orientation.Vertical,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 15)
            };

            TextBlock maxTempText = new TextBlock
            {
                Text = $"{forecast.TempMax}°",
                Style = (Style)FindResource("ForecastTextStyle"),
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            TextBlock minTempText = new TextBlock
            {
                Text = $"{forecast.TempMin}°",
                Style = (Style)FindResource("ForecastTextStyle"),
                FontSize = 18,
                Foreground = new SolidColorBrush(Color.FromRgb(170, 170, 255)),
                HorizontalAlignment = HorizontalAlignment.Center
            };

            tempPanel.Children.Add(maxTempText);
            tempPanel.Children.Add(minTempText);

            // 详细信息面板
            StackPanel detailsPanel = new StackPanel
            {
                Orientation = Orientation.Vertical,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            // 风力信息
            TextBlock windText = new TextBlock
            {
                Text = $"🌪 {forecast.WindDirDay} {forecast.WindScaleDay}级",
                Style = (Style)FindResource("ForecastTextStyle"),
                FontSize = 12,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 2, 0, 2)
            };

            // 湿度信息
            TextBlock humidityText = new TextBlock
            {
                Text = $"💧 {forecast.Humidity}%",
                Style = (Style)FindResource("ForecastTextStyle"),
                FontSize = 12,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 2, 0, 2)
            };

            // 降水概率
            TextBlock precipText = new TextBlock
            {
                Text = $"☔ {forecast.Precip}mm",
                Style = (Style)FindResource("ForecastTextStyle"),
                FontSize = 12,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 2, 0, 2)
            };

            detailsPanel.Children.Add(windText);
            detailsPanel.Children.Add(humidityText);
            detailsPanel.Children.Add(precipText);

            // 组装所有元素
            mainContent.Children.Add(dateText);
            mainContent.Children.Add(dayText);
            mainContent.Children.Add(weatherIcon);
            mainContent.Children.Add(weatherText);
            mainContent.Children.Add(tempPanel);
            mainContent.Children.Add(detailsPanel);

            card.Child = mainContent;
            return card;
        }
    }
}