using System;
using Supabase;
using Supabase.Postgrest.Attributes;
using Supabase.Postgrest.Models;

namespace WpfApp
{
    [Table("musiclist")]
    public class MusicItem : BaseModel
    {
        public MusicItem() { }

        [PrimaryKey("id", true)]
        [Column("id")]
        public int Id { get; set; }

        [Column("title")]
        public string Title { get; set; }

        [<PERSON>um<PERSON>("artist")]
        public string Artist { get; set; }

        [Column("album")]
        public string Album { get; set; }

        [Column("year")]
        public string Year { get; set; }

        [Column("genre")]
        public string Genre { get; set; }

        [Column("cover_url")]
        public string CoverUrl { get; set; }

        [Column("imglink")]
        public string ImgLink { get; set; }

        [Column("description")]
        public string Description { get; set; }

        [Column("magetlink")]
        public string MagnetLink { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        [Column("updated_at")]
        public DateTime UpdatedAt { get; set; }
    }
}
