<Window x:Class="WpfApp.DirectorDetailWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WpfApp"
        mc:Ignorable="d"
        Title="导演详情" Height="650" Width="900"
        Background="#121a30"
        WindowStartupLocation="CenterScreen"
        WindowStyle="None"
        AllowsTransparency="True"
        BorderThickness="0"
        ResizeMode="CanResize">
    
    <Window.Resources>
        <!-- 样式资源 -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#8890AD"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,15,0,5"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>
        
        <Style x:Key="ContentStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
            <Setter Property="LineHeight" Value="22"/>
        </Style>
        
        <Style x:Key="MovieCardStyle" TargetType="Border">
            <Setter Property="Width" Value="160"/>
            <Setter Property="Height" Value="240"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderBrush" Value="#004D4D"/>
            <Setter Property="BorderThickness" Value="0.5"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect ShadowDepth="3" BlurRadius="5" Opacity="0.3" Color="Black"/>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="WindowButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="40"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#4A5AE8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3A4AD8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 复制按钮样式 -->
        <Style x:Key="CopyButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#8890AD"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="5,2"/>
            <Setter Property="FontSize" Value="11"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="3"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#364375"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="FlatButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#3D8BFF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,5"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#2D7BEF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1D6BDF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
    <Border BorderThickness="1" BorderBrush="#1a2747" CornerRadius="8">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- 标题栏 -->
            <Border Grid.Row="0" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                <Border.Background>
                    <ImageBrush ImageSource="/img/head_bg.png" Stretch="UniformToFill"/>
                </Border.Background>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 标题文本 -->
                    <TextBlock x:Name="TitleText" Text="导演详情" Foreground="White" FontSize="14"
                               VerticalAlignment="Center" Margin="15,0,0,0"/>

                    <!-- 窗口控制按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button x:Name="MinimizeButton" Style="{StaticResource WindowButtonStyle}"
                                Click="MinimizeButton_Click">
                            <TextBlock Text="&#xE921;" FontFamily="Segoe MDL2 Assets"
                                       FontSize="12" Foreground="White"/>
                        </Button>
                        <Button x:Name="MaximizeButton" Style="{StaticResource WindowButtonStyle}"
                                Click="MaximizeButton_Click">
                            <TextBlock Text="&#xE922;" FontFamily="Segoe MDL2 Assets"
                                       FontSize="12" Foreground="White"/>
                        </Button>
                        <Button x:Name="CloseButton" Style="{StaticResource WindowButtonStyle}"
                                Click="CloseButton_Click">
                            <TextBlock Text="&#xE8BB;" FontFamily="Segoe MDL2 Assets"
                                       FontSize="12" Foreground="White"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>
            
            <!-- 内容区域 -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧导演照片和基本信息区域 -->
                <Grid Grid.Column="0" Width="280" Margin="20,20,0,20">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 导演照片 -->
                    <Border Grid.Row="0" Width="260" Height="360" CornerRadius="10">
                        <Border.Effect>
                            <DropShadowEffect ShadowDepth="3" BlurRadius="5" Opacity="0.3" Color="Black"/>
                        </Border.Effect>
                        <Image x:Name="ProfileImage" Stretch="UniformToFill"/>
                    </Border>
                    
                    <!-- 导演基本信息 -->
                    <StackPanel Grid.Row="1" Margin="0,20,0,0">
                        <!-- 出生日期 -->
                        <TextBlock Text="出生日期：" Style="{StaticResource LabelStyle}" Margin="0,10,0,5"/>
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock x:Name="BirthdateText" Text="1970-01-01" Style="{StaticResource ContentStyle}" Margin="0"/>
                            <Button Grid.Column="1" Content="复制" Style="{StaticResource CopyButtonStyle}"
                                    Click="CopyBirthdate_Click" Tag="{Binding ElementName=BirthdateText, Path=Text}"/>
                        </Grid>

                        <!-- 出生地 -->
                        <TextBlock Text="出生地：" Style="{StaticResource LabelStyle}"/>
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock x:Name="PlaceOfBirthText" Text="美国，加利福尼亚" Style="{StaticResource ContentStyle}" Margin="0"/>
                            <Button Grid.Column="1" Content="复制" Style="{StaticResource CopyButtonStyle}"
                                    Click="CopyPlaceOfBirth_Click" Tag="{Binding ElementName=PlaceOfBirthText, Path=Text}"/>
                        </Grid>

                        <!-- IMDb链接 -->
                        <TextBlock Text="IMDb链接：" Style="{StaticResource LabelStyle}"/>
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock>
                                <Hyperlink x:Name="ImdbLink" Click="ImdbLink_Click" Foreground="#4D95FF">
                                    <TextBlock x:Name="ImdbText" Text="查看IMDb主页" Foreground="#4D95FF"/>
                                </Hyperlink>
                            </TextBlock>
                            <Button Grid.Column="1" Content="复制链接" Style="{StaticResource CopyButtonStyle}"
                                    Click="CopyImdbLink_Click"/>
                        </Grid>
                    </StackPanel>
                </Grid>

                <!-- 右侧导演详情信息区域 -->
                <ScrollViewer Grid.Column="1" VerticalScrollBarVisibility="Auto" Margin="20">
                    <StackPanel>
                        <!-- 导演姓名 -->
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <StackPanel>
                                <TextBlock x:Name="DirectorNameText" Text="导演姓名" FontSize="28" Foreground="White"
                                           FontWeight="Bold" Margin="0"/>
                                <TextBlock x:Name="DirectorEnglishNameText" Text="Director Name" FontSize="16"
                                           Foreground="#8890AD" Margin="0,5,0,0"/>
                            </StackPanel>
                            <StackPanel Grid.Column="1" VerticalAlignment="Top" Margin="10,5,0,0">
                                <Button Content="复制中文名" Style="{StaticResource CopyButtonStyle}"
                                        Click="CopyDirectorName_Click" Margin="0,0,0,5"/>
                                <Button Content="复制英文名" Style="{StaticResource CopyButtonStyle}"
                                        Click="CopyDirectorEnglishName_Click"/>
                            </StackPanel>
                        </Grid>

                        <!-- 导演简介 -->
                        <TextBlock Text="简介：" Style="{StaticResource LabelStyle}"/>
                        <Grid Margin="0,0,0,10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock x:Name="BiographyText" Text="导演简介内容..." Style="{StaticResource ContentStyle}"
                                       TextWrapping="Wrap" MaxWidth="520" Margin="0"/>
                            <Button Grid.Column="1" Content="复制简介" Style="{StaticResource CopyButtonStyle}"
                                    Click="CopyBiography_Click" VerticalAlignment="Top" Margin="10,0,0,0"/>
                        </Grid>
                        
                        <!-- 导演作品 -->
                        <TextBlock Text="代表作品：" Style="{StaticResource LabelStyle}" Margin="0,30,0,10"/>
                        
                        <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Disabled" 
                                      Margin="0,10,0,20" MaxHeight="260">
                            <ItemsControl x:Name="MoviesItemsControl">
                                <ItemsControl.ItemsPanel>
                                    <ItemsPanelTemplate>
                                        <StackPanel Orientation="Horizontal"/>
                                    </ItemsPanelTemplate>
                                </ItemsControl.ItemsPanel>
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Style="{StaticResource MovieCardStyle}">
                                            <Grid>
                                                <Grid.RowDefinitions>
                                                    <RowDefinition Height="*"/>
                                                    <RowDefinition Height="Auto"/>
                                                </Grid.RowDefinitions>
                                                
                                                <!-- 电影海报 -->
                                                <Border Grid.Row="0" Margin="5,5,5,0" CornerRadius="5">
                                                    <Border.Background>
                                                        <ImageBrush ImageSource="{Binding PosterPath}" Stretch="UniformToFill"/>
                                                    </Border.Background>
                                                    
                                                    <!-- 电影评分 -->
                                                    <Border HorizontalAlignment="Right" VerticalAlignment="Top" 
                                                            Background="#FFDD33" CornerRadius="0,5,0,5" 
                                                            Padding="5,2">
                                                        <TextBlock Text="{Binding Rating}" FontWeight="Bold" FontSize="11"
                                                                   Foreground="#333333"/>
                                                    </Border>
                                                </Border>
                                                
                                                <!-- 电影信息 -->
                                                <StackPanel Grid.Row="1" Margin="5,5,5,5">
                                                    <TextBlock Text="{Binding Title}" FontWeight="SemiBold" 
                                                               Foreground="White" FontSize="12" 
                                                               TextTrimming="CharacterEllipsis"/>
                                                    <TextBlock Text="{Binding ReleaseYear}" Foreground="#8890AD" 
                                                               FontSize="10" Margin="0,3,0,0"/>
                                                </StackPanel>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </StackPanel>
                </ScrollViewer>
            </Grid>
            
            <!-- 加载进度指示器 -->
            <Grid x:Name="LoadingPanel" Grid.RowSpan="2" Background="#80000000" Visibility="Collapsed">
                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                    <TextBlock x:Name="LoadingText" Text="正在加载导演信息..." Foreground="White" FontSize="18" Margin="0,0,0,10"/>
                    <ProgressBar Width="200" Height="5" IsIndeterminate="True"/>
                </StackPanel>
            </Grid>
        </Grid>
    </Border>
</Window> 