using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Supabase.Postgrest.Models;

namespace WpfAdmin.Models
{
    [Table("health_data")]
    public class HealthData : BaseModel
    {
        [Column("date")]
        public DateTime Date { get; set; }

        [Column("steps")]
        public int Steps { get; set; }

        [Column("heart_rate")]
        public int HeartRate { get; set; }

        [Column("systolic_pressure")]
        public int SystolicPressure { get; set; }

        [Column("diastolic_pressure")]
        public int DiastolicPressure { get; set; }

        [Column("weight")]
        public double Weight { get; set; }

        [Column("notes")]
        public string Notes { get; set; } = string.Empty;

        [Column("user_id")]
        public string UserId { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }

        public bool IsBloodPressureAbnormal => SystolicPressure > 130 || DiastolicPressure > 90;
    }
} 