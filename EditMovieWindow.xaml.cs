using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media.Animation;
using System.Windows.Media;
using System.Diagnostics;
using System.Threading.Tasks;
using Supabase;
using System.Collections.Generic;
using WpfApp;

namespace WpfAdmin
{
    /// <summary>
    /// EditMovieWindow.xaml 的交互逻辑
    /// </summary>
    public partial class EditMovieWindow : Window
    {
        private readonly Supabase.Client _supabaseClient;
        private readonly MovieItem _movieItem;
        private event EventHandler MovieEdited;

        public EditMovieWindow(Supabase.Client supabaseClient, MovieItem movieItem)
        {
            InitializeComponent();
            _supabaseClient = supabaseClient;
            _movieItem = movieItem;

            // 加载现有数据
            TitleTextBox.Text = movieItem.Title;
            DirectorTextBox.Text = movieItem.Director;
            CountryTextBox.Text = movieItem.Country;
            IntroTextBox.Text = movieItem.Intro;
            RatingTextBox.Text = movieItem.Rating;
            MagnetTextBox.Text = movieItem.Magnet;
            SubtitleTextBox.Text = movieItem.Sublink;
            DoubanTextBox.Text = movieItem.Douban;
            PosterUrlTextBox.Text = movieItem.PosterUrl;
            AwardsTextBox.Text = movieItem.Awards;
            ReleaseDatePicker.SelectedDate = DateTime.TryParse(movieItem.ReleaseDate, out var date) ? date : DateTime.Now;
        }

        private void Border_Mousedown(object sender, MouseButtonEventArgs e)
        {
            if (e.ChangedButton == MouseButton.Left)
            {
                this.DragMove();
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 启用保存中状态
                SaveButton.IsEnabled = false;
                SaveProgressBar.Visibility = Visibility.Visible;

                // 更新电影数据
                _movieItem.Title = TitleTextBox.Text;
                _movieItem.Director = DirectorTextBox.Text;
                _movieItem.Country = CountryTextBox.Text;
                _movieItem.Intro = IntroTextBox.Text;
                _movieItem.Rating = RatingTextBox.Text;
                _movieItem.Magnet = MagnetTextBox.Text;
                _movieItem.Sublink = SubtitleTextBox.Text;
                _movieItem.Douban = DoubanTextBox.Text;
                _movieItem.PosterUrl = PosterUrlTextBox.Text;
                _movieItem.Awards = AwardsTextBox.Text;
                _movieItem.ReleaseDate = ReleaseDatePicker.SelectedDate?.ToString("yyyy-MM-dd") ?? DateTime.Now.ToString("yyyy-MM-dd");

                // 保存到数据库
                await _supabaseClient.From<MovieItem>().Update(_movieItem);

                // 触发事件
                MovieEdited?.Invoke(this, EventArgs.Empty);

                // 显示成功信息
                MessageBox.Show("电影信息已更新！", "保存成功", MessageBoxButton.OK, MessageBoxImage.Information);
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存电影信息时出错: {ex.Message}", "保存失败", MessageBoxButton.OK, MessageBoxImage.Error);
                Debug.WriteLine($"保存电影信息时出错: {ex.Message}");
            }
            finally
            {
                // 恢复按钮状态
                SaveButton.IsEnabled = true;
                SaveProgressBar.Visibility = Visibility.Collapsed;
            }
        }
    }
}