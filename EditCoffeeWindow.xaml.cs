﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using Supabase.Interfaces;
using Supabase;
using Supabase.Gotrue;
using Supabase.Postgrest;

namespace WpfApp
{
    public partial class EditCoffeeWindow : Window
    {
        private readonly Supabase.Client _supabaseClient;
        private readonly int _coffeeId; // 使用私有字段存储ID
        
        /// <summary>
        /// 编辑咖啡窗口构造函数
        /// </summary>
        /// <param name="supabaseClient">Supabase客户端</param>
        /// <param name="coffeeItem">要编辑的咖啡项</param>
        public EditCoffeeWindow(Supabase.Client supabaseClient, CoffeeItem coffeeItem)
        {
            InitializeComponent();

            // 确保ID被正确保存
            _coffeeId = coffeeItem.Id;
            
            // 调试输出
            Console.WriteLine($"初始化编辑窗口 - 咖啡ID: {_coffeeId}, 名称: {coffeeItem.Name}");
            
            try 
            {
                // 设置表单字段
                txtName.Text = coffeeItem.Name ?? "咖啡详情";
                txtType.Text = coffeeItem.Type;
                txtPrice.Text = coffeeItem.Price.ToString();
                txtWeight.Text = coffeeItem.Weight.ToString();
                txtCountry.Text = coffeeItem.Country;
                
                // 处理日期
                if (DateTime.TryParse(coffeeItem.Date, out DateTime date))
                {
                    dpDate.SelectedDate = date;
                }
                else
                {
                    dpDate.SelectedDate = DateTime.Now;
                }

                // 设置状态下拉框
                SetStatusSelection(coffeeItem.Status);
                
                // 设置备注和链接
                txtInfo.Text = coffeeItem.Memo;
                txtLink.Text = coffeeItem.Link;

                _supabaseClient = supabaseClient;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化窗口时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        // 辅助方法：设置状态选择
        private void SetStatusSelection(string status)
        {
            // 先尝试按Tag匹配
            foreach (ComboBoxItem item in cmbStatus.Items)
            {
                if (item.Tag != null && item.Tag.ToString() == status)
                {
                    cmbStatus.SelectedItem = item;
                    Console.WriteLine($"通过Tag匹配到状态: {status}");
                    return;
                }
            }
            
            // 如果按Tag没找到，尝试按索引匹配
            int index = -1;
            switch (status)
            {
                case "road": index = 0; break;
                case "new": index = 1; break;
                case "ing": index = 2; break;
                case "over": index = 3; break;
            }

            if (index >= 0 && index < cmbStatus.Items.Count)
            {
                cmbStatus.SelectedIndex = index;
                Console.WriteLine($"通过Index匹配到状态: {status} -> {index}");
            }
            else
            {
                Console.WriteLine($"无法匹配状态: {status}，使用默认值");
                cmbStatus.SelectedIndex = 0; // 默认选第一项
            }
        }

        private async void btnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 调试输出
                Console.WriteLine($"保存咖啡 ID: {_coffeeId}");
                
                // 获取选中的ComboBoxItem
                ComboBoxItem selectedItem = cmbStatus.SelectedItem as ComboBoxItem;
                if (selectedItem == null)
                {
                    MessageBox.Show("请选择状态！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                // 确保使用Tag作为状态值
                string statusValue = selectedItem.Tag.ToString();
                
                var updatedCoffee = new CoffeeItemDb
                {
                    Id = _coffeeId, 
                    Name = txtName.Text,
                    Type = txtType.Text,
                    Price = Convert.ToInt32(txtPrice.Text),
                    Weight = Convert.ToInt32(txtWeight.Text),
                    Country = txtCountry.Text,
                    Date = dpDate.SelectedDate?.ToString("yyyy-MM-dd") ?? DateTime.Now.ToString("yyyy-MM-dd"),
                    Status = statusValue,
                    Memo = txtInfo.Text,
                    Link = txtLink.Text
                };

                // 调试输出
                Console.WriteLine($"更新咖啡，ID: {_coffeeId}, Status: {statusValue}");

                try
                {
                    // 执行更新操作
                    await _supabaseClient
                        .From<CoffeeItemDb>()
                        .Where(c => c.Id == _coffeeId)
                        .Update(updatedCoffee);
                    
                    // 更新成功通知用户
                    MessageBox.Show("咖啡信息更新成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    DialogResult = true;
                }
                catch (Exception updateEx)
                {
                    throw new Exception($"更新操作失败: {updateEx.Message}", updateEx);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"更新失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void btnDel_Click(object sender, RoutedEventArgs e)
        {
            // 显示确认对话框
            MessageBoxResult result = MessageBox.Show(
                $"确定要删除咖啡 \"{txtName.Text}\" 吗？",
                "删除确认",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            // 如果用户点击"是"，则删除记录
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    // 调试输出
                    Console.WriteLine($"删除咖啡 ID: {_coffeeId}");
                    
                    try
                    {
                        // 执行删除操作
                        await _supabaseClient
                            .From<CoffeeItemDb>()
                            .Where(c => c.Id == _coffeeId) // 使用正确的ID
                            .Delete();
                        
                        // 删除成功后通知用户并关闭窗口
                        MessageBox.Show("咖啡记录已成功删除！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                        DialogResult = true; // 设置对话框结果为true，表示成功
                        Close();
                    }
                    catch (Exception deleteEx)
                    {
                        throw new Exception($"删除操作失败: {deleteEx.Message}", deleteEx);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"删除失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void btnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
        }

        // 窗口控制按钮事件处理
        private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                // 双击最大化/还原窗口
                if (WindowState == WindowState.Maximized)
                    WindowState = WindowState.Normal;
                else
                    WindowState = WindowState.Maximized;
            }
            else
            {
                // 单击拖动窗口
                DragMove();
            }
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        private void MaximizeButton_Click(object sender, RoutedEventArgs e)
        {
            if (WindowState == WindowState.Maximized)
                WindowState = WindowState.Normal;
            else
                WindowState = WindowState.Maximized;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }
    }
    // 咖啡数据模型类
    public class CoffeeItemView
    {
        public string Name { get; set; }
        public int Price { get; set; }
        public int Weight { get; set; }
        public string Status { get; set; }
        public string Type { get; set; }
        public string Link { get; set; }
        public string Country { get; set; }
        public DateTime Date { get; set; }
        public string Memo { get; set; }
    }
}
