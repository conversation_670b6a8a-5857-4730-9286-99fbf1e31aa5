﻿#pragma checksum "..\..\..\MonthlyStepsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C8BF4B2CA911AB430DE8C9C92928CACFD7A3D4CC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace WpfAdmin {
    
    
    /// <summary>
    /// MonthlyStepsWindow
    /// </summary>
    public partial class MonthlyStepsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 69 "..\..\..\MonthlyStepsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrevMonthButton;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\MonthlyStepsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MonthYearText;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\MonthlyStepsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NextMonthButton;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\MonthlyStepsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DownloadReportButton;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\MonthlyStepsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.UniformGrid CalendarGrid;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\MonthlyStepsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle Under6000Bar;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\MonthlyStepsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Under6000Text;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\MonthlyStepsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle Under8000Bar;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\MonthlyStepsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Under8000Text;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\MonthlyStepsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle Under10000Bar;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\MonthlyStepsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Under10000Text;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\MonthlyStepsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Rectangle Over10000Bar;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\MonthlyStepsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Over10000Text;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\MonthlyStepsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalStepsText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WpfAdmin;component/monthlystepswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MonthlyStepsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PrevMonthButton = ((System.Windows.Controls.Button)(target));
            
            #line 70 "..\..\..\MonthlyStepsWindow.xaml"
            this.PrevMonthButton.Click += new System.Windows.RoutedEventHandler(this.PrevMonthButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MonthYearText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.NextMonthButton = ((System.Windows.Controls.Button)(target));
            
            #line 81 "..\..\..\MonthlyStepsWindow.xaml"
            this.NextMonthButton.Click += new System.Windows.RoutedEventHandler(this.NextMonthButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DownloadReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 87 "..\..\..\MonthlyStepsWindow.xaml"
            this.DownloadReportButton.Click += new System.Windows.RoutedEventHandler(this.DownloadReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CalendarGrid = ((System.Windows.Controls.Primitives.UniformGrid)(target));
            return;
            case 6:
            this.Under6000Bar = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 7:
            this.Under6000Text = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.Under8000Bar = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 9:
            this.Under8000Text = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.Under10000Bar = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 11:
            this.Under10000Text = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.Over10000Bar = ((System.Windows.Shapes.Rectangle)(target));
            return;
            case 13:
            this.Over10000Text = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.TotalStepsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            
            #line 192 "..\..\..\MonthlyStepsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

