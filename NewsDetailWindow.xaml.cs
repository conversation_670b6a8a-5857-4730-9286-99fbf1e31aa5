using System;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using WpfApp.Models;
using WpfApp.Services;
using Microsoft.Web.WebView2.Core;

namespace WpfApp
{
    /// <summary>
    /// 新闻详情窗口
    /// </summary>
    public partial class NewsDetailWindow : Window
    {
        private readonly NewsItem _newsItem;
        private readonly NewsService _newsService;
        private bool _isWebMode = false;
        private bool _isFullScreen = false;
        private WindowState _previousWindowState;
        private WindowStyle _previousWindowStyle;
        private double _previousWidth;
        private double _previousHeight;
        private double _previousLeft;
        private double _previousTop;

        public NewsDetailWindow(NewsItem newsItem)
        {
            InitializeComponent();
            _newsItem = newsItem;
            _newsService = new NewsService();

            InitializeWindow();
            InitializeWebView();
            LoadNewsContent();

            // 添加键盘事件处理
            this.KeyDown += NewsDetailWindow_KeyDown;
        }

        /// <summary>
        /// 初始化窗口
        /// </summary>
        private void InitializeWindow()
        {
            // 设置新闻基本信息
            NewsTitle.Text = _newsItem.Title;
            NewsSource.Text = _newsItem.Source;
            NewsTime.Text = _newsItem.PublishTime.ToString("yyyy-MM-dd HH:mm");

            // 如果有摘要，先显示摘要
            if (!string.IsNullOrEmpty(_newsItem.Summary))
            {
                NewsContent.Text = _newsItem.Summary;
            }
        }

        /// <summary>
        /// 加载新闻详细内容
        /// </summary>
        private async void LoadNewsContent()
        {
            try
            {
                LoadingPanel.Visibility = Visibility.Visible;
                TipPanel.Visibility = Visibility.Collapsed;

                // 获取新闻详细内容
                var content = await _newsService.GetNewsContentAsync(_newsItem.Url);

                // 更新UI
                if (!string.IsNullOrEmpty(content) && !content.Contains("无法获取") && !content.Contains("获取失败"))
                {
                    // 改善文本分段显示
                    var formattedContent = FormatNewsContent(content);
                    NewsContent.Text = formattedContent;

                    // 如果内容较短，显示提示
                    if (content.Length < 200)
                    {
                        TipPanel.Visibility = Visibility.Visible;
                    }
                }
                else
                {
                    NewsContent.Text = $"📰 {_newsItem.Title}\n\n" +
                                      $"📅 发布时间：{_newsItem.PublishTime}\n" +
                                      $"📍 来源：{_newsItem.Source}\n\n" +
                                      $"💡 摘要：\n{FormatParagraph(_newsItem.Summary ?? "暂无摘要")}\n\n" +
                                      $"⚠️ 无法获取完整内容，请切换到网页模式或点击下方【在浏览器中打开】按钮查看原文。";
                    TipPanel.Visibility = Visibility.Visible;
                }

                // 标记为已读
                _newsItem.IsRead = true;
            }
            catch (Exception ex)
            {
                NewsContent.Text = $"📰 {_newsItem.Title}\n\n" +
                                  $"❌ 加载新闻内容失败：{ex.Message}\n\n" +
                                  $"💡 建议：请切换到网页模式或点击下方【在浏览器中打开】按钮在浏览器中查看完整内容。";
                TipPanel.Visibility = Visibility.Visible;
            }
            finally
            {
                LoadingPanel.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 格式化新闻内容，改善分段显示
        /// </summary>
        private string FormatNewsContent(string content)
        {
            if (string.IsNullOrEmpty(content))
                return content;

            // 清理多余的空白字符
            content = System.Text.RegularExpressions.Regex.Replace(content, @"\s+", " ");

            // 按句号分段，但保留一些常见的缩写
            var sentences = content.Split(new char[] { '。', '！', '？' }, StringSplitOptions.RemoveEmptyEntries);

            var formattedParagraphs = new System.Collections.Generic.List<string>();
            var currentParagraph = "";

            foreach (var sentence in sentences)
            {
                var trimmedSentence = sentence.Trim();
                if (string.IsNullOrEmpty(trimmedSentence))
                    continue;

                currentParagraph += trimmedSentence;

                // 如果句子以这些标点结尾，添加回标点
                if (content.Contains(trimmedSentence + "。"))
                    currentParagraph += "。";
                else if (content.Contains(trimmedSentence + "！"))
                    currentParagraph += "！";
                else if (content.Contains(trimmedSentence + "？"))
                    currentParagraph += "？";

                // 每2-3个句子形成一个段落
                if (currentParagraph.Length > 100 || formattedParagraphs.Count == 0)
                {
                    formattedParagraphs.Add(currentParagraph);
                    currentParagraph = "";
                }
                else
                {
                    currentParagraph += " ";
                }
            }

            // 添加最后一个段落
            if (!string.IsNullOrEmpty(currentParagraph))
            {
                formattedParagraphs.Add(currentParagraph);
            }

            return string.Join("\n\n", formattedParagraphs);
        }

        /// <summary>
        /// 格式化段落
        /// </summary>
        private string FormatParagraph(string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            return System.Text.RegularExpressions.Regex.Replace(text, @"\s+", " ").Trim();
        }

        /// <summary>
        /// 标题栏鼠标按下事件 - 用于拖动窗口
        /// </summary>
        private void TitleBar_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed && !_isFullScreen)
            {
                DragMove();
            }
        }

        /// <summary>
        /// 键盘按键事件处理
        /// </summary>
        private void NewsDetailWindow_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.Key)
            {
                case Key.F11:
                    // F11键切换全屏
                    ToggleFullScreen();
                    e.Handled = true;
                    break;
                case Key.Escape:
                    // ESC键退出全屏或关闭窗口
                    if (_isFullScreen)
                    {
                        ExitFullScreen();
                    }
                    else
                    {
                        Close();
                    }
                    e.Handled = true;
                    break;
                case Key.F5:
                    // F5键刷新内容
                    RefreshButton_Click(sender, new RoutedEventArgs());
                    e.Handled = true;
                    break;
            }
        }

        /// <summary>
        /// 全屏按钮点击事件
        /// </summary>
        private void FullScreenButton_Click(object sender, RoutedEventArgs e)
        {
            ToggleFullScreen();
        }

        /// <summary>
        /// 切换全屏模式
        /// </summary>
        private void ToggleFullScreen()
        {
            if (_isFullScreen)
            {
                // 退出全屏
                ExitFullScreen();
            }
            else
            {
                // 进入全屏
                EnterFullScreen();
            }
        }

        /// <summary>
        /// 进入全屏模式
        /// </summary>
        private void EnterFullScreen()
        {
            // 保存当前窗口状态
            _previousWindowState = this.WindowState;
            _previousWindowStyle = this.WindowStyle;
            _previousWidth = this.Width;
            _previousHeight = this.Height;
            _previousLeft = this.Left;
            _previousTop = this.Top;

            // 设置全屏
            this.WindowState = WindowState.Normal;
            this.WindowStyle = WindowStyle.None;
            this.Left = 0;
            this.Top = 0;
            this.Width = SystemParameters.PrimaryScreenWidth;
            this.Height = SystemParameters.PrimaryScreenHeight;
            this.Topmost = true;

            _isFullScreen = true;

            // 更新按钮图标和提示
            FullScreenIcon.Text = "\uE73F"; // 退出全屏图标
            FullScreenButton.ToolTip = "退出全屏";
            WindowTitle.Text = "新闻详情 - 全屏模式";

            // 隐藏任务栏
            this.ResizeMode = ResizeMode.NoResize;
        }

        /// <summary>
        /// 退出全屏模式
        /// </summary>
        private void ExitFullScreen()
        {
            // 恢复窗口状态
            this.Topmost = false;
            this.WindowState = _previousWindowState;
            this.WindowStyle = _previousWindowStyle;
            this.Width = _previousWidth;
            this.Height = _previousHeight;
            this.Left = _previousLeft;
            this.Top = _previousTop;
            this.ResizeMode = ResizeMode.CanResize;

            _isFullScreen = false;

            // 更新按钮图标和提示
            FullScreenIcon.Text = "\uE740"; // 全屏图标
            FullScreenButton.ToolTip = "全屏显示";
            WindowTitle.Text = "新闻详情";
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        /// <summary>
        /// 初始化WebView2
        /// </summary>
        private async void InitializeWebView()
        {
            try
            {
                await NewsWebView.EnsureCoreWebView2Async(null);

                // 设置广告拦截
                await SetupAdBlocker();

                // 设置WebView2事件
                NewsWebView.NavigationStarting += (s, e) =>
                {
                    WebLoadingPanel.Visibility = Visibility.Visible;
                };

                NewsWebView.NavigationCompleted += async (s, e) =>
                {
                    WebLoadingPanel.Visibility = Visibility.Collapsed;

                    // 页面加载完成后，注入CSS和JavaScript来优化显示
                    await InjectContentOptimizationScript();
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"WebView2初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 设置广告拦截器
        /// </summary>
        private async Task SetupAdBlocker()
        {
            try
            {
                if (NewsWebView.CoreWebView2 == null) return;

                // 拦截广告相关的网络请求
                NewsWebView.CoreWebView2.WebResourceRequested += (sender, e) =>
                {
                    var uri = e.Request.Uri.ToLower();

                    // 广告域名和路径关键词
                    var adKeywords = new[]
                    {
                        "doubleclick", "googleadservices", "googlesyndication",
                        "googletagmanager", "google-analytics", "googletagservices",
                        "adsystem", "amazon-adsystem", "ads.yahoo", "advertising",
                        "adsense", "adnxs", "adsafeprotected", "moatads",
                        "scorecardresearch", "quantserve", "outbrain", "taboola",
                        "facebook.com/tr", "connect.facebook.net",
                        "baidu.com/cpro", "pos.baidu.com", "cbjs.baidu.com",
                        "tanx.com", "alimama.com", "mmstat.com",
                        "cnzz.com", "51.la", "umeng.com", "growingio.com"
                    };

                    foreach (var keyword in adKeywords)
                    {
                        if (uri.Contains(keyword))
                        {
                            e.Response = NewsWebView.CoreWebView2.Environment.CreateWebResourceResponse(
                                null, 200, "OK", "");
                            return;
                        }
                    }
                };

                // 添加资源请求过滤器
                NewsWebView.CoreWebView2.AddWebResourceRequestedFilter("*", Microsoft.Web.WebView2.Core.CoreWebView2WebResourceContext.All);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"设置广告拦截器失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 文本模式按钮点击事件
        /// </summary>
        private void TextModeButton_Click(object sender, RoutedEventArgs e)
        {
            SwitchToTextMode();
        }

        /// <summary>
        /// 网页模式按钮点击事件
        /// </summary>
        private void WebModeButton_Click(object sender, RoutedEventArgs e)
        {
            SwitchToWebMode();
        }

        /// <summary>
        /// 切换到文本模式
        /// </summary>
        private void SwitchToTextMode()
        {
            _isWebMode = false;
            TextModeViewer.Visibility = Visibility.Visible;
            WebModeViewer.Visibility = Visibility.Collapsed;

            // 更新按钮样式
            TextModeButton.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0x5D, 0x6E, 0xFF));
            WebModeButton.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0x36, 0x43, 0x75));
        }

        /// <summary>
        /// 切换到网页模式
        /// </summary>
        private void SwitchToWebMode()
        {
            _isWebMode = true;
            TextModeViewer.Visibility = Visibility.Collapsed;
            WebModeViewer.Visibility = Visibility.Visible;

            // 更新按钮样式
            TextModeButton.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0x36, 0x43, 0x75));
            WebModeButton.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(0x5D, 0x6E, 0xFF));

            // 加载网页内容
            LoadWebContent();
        }

        /// <summary>
        /// 加载网页内容到WebView2
        /// </summary>
        private async void LoadWebContent()
        {
            try
            {
                if (!string.IsNullOrEmpty(_newsItem.Url) && NewsWebView.CoreWebView2 != null)
                {
                    WebLoadingPanel.Visibility = Visibility.Visible;

                    // 设置用户代理，避免被某些网站阻止
                    NewsWebView.CoreWebView2.Settings.UserAgent =
                        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";

                    // 禁用一些可能影响阅读的功能
                    NewsWebView.CoreWebView2.Settings.AreDevToolsEnabled = false;
                    NewsWebView.CoreWebView2.Settings.AreDefaultContextMenusEnabled = false;
                    NewsWebView.CoreWebView2.Settings.IsSwipeNavigationEnabled = false;

                    NewsWebView.CoreWebView2.Navigate(_newsItem.Url);
                }
                else
                {
                    var errorHtml = @"
                    <html>
                    <head>
                        <style>
                            body {
                                background-color: #1E1E2E;
                                color: white;
                                font-family: 'Microsoft YaHei', Arial, sans-serif;
                                padding: 20px;
                                text-align: center;
                            }
                            .error-container {
                                max-width: 500px;
                                margin: 50px auto;
                                padding: 30px;
                                background-color: #364375;
                                border-radius: 10px;
                            }
                        </style>
                    </head>
                    <body>
                        <div class='error-container'>
                            <h3>⚠️ 无法加载网页</h3>
                            <p>原文链接不可用或WebView2未正确初始化</p>
                            <p>请尝试点击【在浏览器中打开】按钮</p>
                        </div>
                    </body>
                    </html>";

                    if (NewsWebView.CoreWebView2 != null)
                    {
                        NewsWebView.CoreWebView2.NavigateToString(errorHtml);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载网页内容失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 注入内容优化脚本
        /// </summary>
        private async Task InjectContentOptimizationScript()
        {
            try
            {
                if (NewsWebView.CoreWebView2 == null) return;

                // 延迟执行，确保页面完全加载
                var optimizationScript = @"
                    setTimeout(function() {
                        try {
                            console.log('开始内容优化...');

                            // 第一步：保护主要内容
                            var protectedContent = null;
                            var contentSelectors = [
                                'article', '[role=""main""]', 'main',
                                '.article', '.content', '.post-content', '.entry-content',
                                '.news-content', '.article-content', '.main-content',
                                '.story-body', '.article-body', '.post-body',
                                'div[class*=""content""]', 'div[class*=""article""]',
                                'div[class*=""story""]', 'div[class*=""news""]'
                            ];

                            // 查找主要内容区域
                            for (var i = 0; i < contentSelectors.length; i++) {
                                var elements = document.querySelectorAll(contentSelectors[i]);
                                for (var j = 0; j < elements.length; j++) {
                                    var el = elements[j];
                                    var textContent = el.textContent || el.innerText || '';
                                    // 如果元素包含足够多的文本内容，认为是主要内容
                                    if (textContent.length > 200) {
                                        protectedContent = el;
                                        console.log('找到主要内容区域:', el.className || el.tagName);
                                        break;
                                    }
                                }
                                if (protectedContent) break;
                            }

                            // 如果没有找到明确的内容区域，尝试通过文本长度判断
                            if (!protectedContent) {
                                var allDivs = document.querySelectorAll('div, section, article');
                                var maxTextLength = 0;
                                for (var i = 0; i < allDivs.length; i++) {
                                    var div = allDivs[i];
                                    var textLength = (div.textContent || div.innerText || '').length;
                                    if (textLength > maxTextLength && textLength > 500) {
                                        maxTextLength = textLength;
                                        protectedContent = div;
                                    }
                                }
                                if (protectedContent) {
                                    console.log('通过文本长度找到主要内容区域');
                                }
                            }

                            // 第二步：创建样式
                            var style = document.createElement('style');
                            style.id = 'news-reader-style';
                            style.textContent = `
                                /* 页面整体样式 */
                                body {
                                    background-color: #1E1E2E !important;
                                    color: #E0E0E0 !important;
                                    font-family: 'SimSun', '宋体', 'NSimSun', '新宋体', 'Microsoft YaHei', serif !important;
                                    font-size: 18px !important;
                                    line-height: 2.0 !important;
                                    margin: 0 !important;
                                    padding: 20px !important;
                                    overflow-x: hidden !important;
                                }

                                /* 隐藏广告和无关元素 - 更精确的选择器 */
                                .ad, .ads, .advertisement, .banner, .popup, .modal,
                                .sidebar:not(.main-sidebar), .side-bar, .right-sidebar, .left-sidebar,
                                .header-ad, .footer-ad, .top-ad, .bottom-ad,
                                .google-ad, .baidu-ad, .taobao-ad, .adsense,
                                .share-box, .social-share, .comment-box, .comments,
                                .related-news, .hot-news, .recommend, .recommendation,
                                .nav:not(.breadcrumb), .navigation, .menu:not(.content-menu),
                                .header:not(.article-header), .footer:not(.article-footer),
                                .breadcrumb, .pagination, .tags, .tag-list,
                                iframe[src*='ad'], iframe[src*='banner'], iframe[src*='doubleclick'],
                                div[class*='ad-'], div[id*='ad-'], div[class*='_ad'], div[id*='_ad'],
                                div[class*='banner'], div[id*='banner'],
                                div[class*='popup'], div[id*='popup'] {
                                    display: none !important;
                                    visibility: hidden !important;
                                    opacity: 0 !important;
                                    height: 0 !important;
                                    width: 0 !important;
                                    margin: 0 !important;
                                    padding: 0 !important;
                                }

                                /* 保护主要内容区域 */
                                .protected-content {
                                    display: block !important;
                                    visibility: visible !important;
                                    opacity: 1 !important;
                                    max-width: 900px !important;
                                    margin: 0 auto !important;
                                    padding: 40px !important;
                                    background-color: #2A2A3E !important;
                                    border-radius: 12px !important;
                                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
                                    color: #E0E0E0 !important;
                                    font-family: 'SimSun', '宋体', 'NSimSun', '新宋体', serif !important;
                                    font-size: 18px !important;
                                    line-height: 2.0 !important;
                                }

                                /* 标题样式 */
                                .protected-content h1, .protected-content h2, .protected-content h3,
                                .protected-content h4, .protected-content h5, .protected-content h6 {
                                    color: #FFFFFF !important;
                                    text-align: center !important;
                                    margin: 25px 0 !important;
                                    font-weight: bold !important;
                                    font-family: 'SimSun', '宋体', 'NSimSun', '新宋体', serif !important;
                                }

                                .protected-content h1 {
                                    font-size: 32px !important;
                                    border-bottom: 2px solid #5D6EFF !important;
                                    padding-bottom: 15px !important;
                                    margin-bottom: 30px !important;
                                }

                                .protected-content h2 {
                                    font-size: 26px !important;
                                    margin: 30px 0 20px 0 !important;
                                }

                                .protected-content h3 {
                                    font-size: 22px !important;
                                    margin: 25px 0 15px 0 !important;
                                }

                                /* 段落样式 */
                                .protected-content p {
                                    color: #E0E0E0 !important;
                                    font-family: 'SimSun', '宋体', 'NSimSun', '新宋体', serif !important;
                                    font-size: 20px !important;
                                    line-height: 2.2 !important;
                                    margin: 20px 0 !important;
                                    text-align: justify !important;
                                    text-indent: 2em !important;
                                    letter-spacing: 0.5px !important;
                                }

                                /* 文本内容样式 */
                                .protected-content div, .protected-content span {
                                    font-family: 'SimSun', '宋体', 'NSimSun', '新宋体', serif !important;
                                    font-size: 20px !important;
                                    line-height: 2.2 !important;
                                    color: #E0E0E0 !important;
                                    letter-spacing: 0.5px !important;
                                }

                                /* 图片样式 */
                                .protected-content img {
                                    max-width: 100% !important;
                                    height: auto !important;
                                    display: block !important;
                                    margin: 20px auto !important;
                                    border-radius: 8px !important;
                                    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2) !important;
                                }

                                /* 链接样式 */
                                .protected-content a {
                                    color: #5D6EFF !important;
                                    text-decoration: none !important;
                                }

                                .protected-content a:hover {
                                    color: #4A5AE8 !important;
                                    text-decoration: underline !important;
                                }

                                /* 列表样式 */
                                .protected-content ul, .protected-content ol {
                                    color: #E0E0E0 !important;
                                    font-family: 'SimSun', '宋体', 'NSimSun', '新宋体', serif !important;
                                    font-size: 20px !important;
                                    margin: 20px 0 !important;
                                    padding-left: 2em !important;
                                }

                                .protected-content li {
                                    margin: 12px 0 !important;
                                    line-height: 2.0 !important;
                                    font-family: 'SimSun', '宋体', 'NSimSun', '新宋体', serif !important;
                                    font-size: 20px !important;
                                    letter-spacing: 0.5px !important;
                                }
                            `;

                            // 移除旧样式，添加新样式
                            var oldStyle = document.getElementById('news-reader-style');
                            if (oldStyle) oldStyle.remove();
                            document.head.appendChild(style);

                            // 第三步：重构页面内容
                            if (protectedContent) {
                                console.log('开始重构页面内容...');

                                // 克隆内容
                                var contentClone = protectedContent.cloneNode(true);
                                contentClone.className = 'protected-content';

                                // 清理克隆内容中的广告元素
                                var adsInContent = contentClone.querySelectorAll('.ad, .ads, .advertisement, .banner, .popup, .modal, .share-box, .social-share, .comment-box, .comments, .related-news, .hot-news, .recommend, .recommendation');
                                for (var i = 0; i < adsInContent.length; i++) {
                                    adsInContent[i].remove();
                                }

                                // 清空body并添加优化后的内容
                                document.body.innerHTML = '';
                                document.body.appendChild(contentClone);

                                console.log('页面内容重构完成');
                            } else {
                                console.log('未找到主要内容区域，仅应用样式优化');

                                // 如果没有找到主要内容，至少隐藏明显的广告元素
                                var adElements = document.querySelectorAll('.ad, .ads, .advertisement, .banner, .popup, .modal, .sidebar, .header, .footer, .nav, .navigation, .menu');
                                for (var i = 0; i < adElements.length; i++) {
                                    var el = adElements[i];
                                    var textContent = el.textContent || el.innerText || '';
                                    // 只隐藏文本内容较少的元素，避免误删正文
                                    if (textContent.length < 100) {
                                        el.style.display = 'none';
                                    }
                                }
                            }

                            console.log('内容优化完成');

                        } catch (e) {
                            console.error('内容优化出错:', e);
                        }
                    }, 2000); // 延迟2秒执行，确保页面完全加载
                ";

                await NewsWebView.CoreWebView2.ExecuteScriptAsync(optimizationScript);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"注入优化脚本失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 刷新内容按钮点击事件
        /// </summary>
        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isWebMode)
            {
                LoadWebContent();
            }
            else
            {
                LoadNewsContent();
            }
        }

        /// <summary>
        /// 查看原文按钮点击事件
        /// </summary>
        private void OpenOriginalButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!string.IsNullOrEmpty(_newsItem.Url))
                {
                    // 使用默认浏览器打开原文链接
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = _newsItem.Url,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("无法获取原文链接", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开原文链接失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 窗口关闭时释放资源
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            _newsService?.Dispose();
            base.OnClosed(e);
        }
    }
}
