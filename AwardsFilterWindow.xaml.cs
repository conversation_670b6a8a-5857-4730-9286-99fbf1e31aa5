using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Supabase;

namespace WpfApp
{
    public partial class AwardsFilterWindow : Window
    {
        private readonly Supabase.Client _supabaseClient;
        private readonly Action<string> _onAwardSelected;
        private List<string> _allAwards;

        public AwardsFilterWindow(Supabase.Client supabaseClient, Action<string> onAwardSelected)
        {
            InitializeComponent();
            _supabaseClient = supabaseClient;
            _onAwardSelected = onAwardSelected;

            // 窗口加载完成后加载数据
            Loaded += AwardsFilterWindow_Loaded;
        }

        private async void AwardsFilterWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadAwardsData();
        }

        /// <summary>
        /// 从数据库加载所有获奖信息并分类显示
        /// </summary>
        private async Task LoadAwardsData()
        {
            try
            {
                // 显示加载状态
                LoadingGrid.Visibility = Visibility.Visible;
                AwardsScrollViewer.Visibility = Visibility.Collapsed;
                NoDataPanel.Visibility = Visibility.Collapsed;

                // 从数据库获取所有电影的获奖信息
                var response = await _supabaseClient
                    .From<MovieItem>()
                    .Select(x => new object[] { x.Awards })
                    .Filter("ccprize", Supabase.Postgrest.Constants.Operator.Not, "null")
                    .Get();

                var movies = response.Models;

                // 提取并处理所有获奖信息
                var allAwardsSet = new HashSet<string>();

                foreach (var movie in movies)
                {
                    if (!string.IsNullOrWhiteSpace(movie.Awards))
                    {
                        // 处理获奖信息，支持多种分隔符
                        var awards = ProcessAwardsText(movie.Awards);
                        foreach (var award in awards)
                        {
                            if (!string.IsNullOrWhiteSpace(award))
                            {
                                allAwardsSet.Add(award.Trim());
                            }
                        }
                    }
                }

                _allAwards = allAwardsSet.OrderBy(a => a).ToList();

                // 隐藏加载状态
                LoadingGrid.Visibility = Visibility.Collapsed;

                if (_allAwards.Count > 0)
                {
                    // 显示获奖标签
                    CreateAwardTags();
                    AwardsScrollViewer.Visibility = Visibility.Visible;
                }
                else
                {
                    // 显示无数据提示
                    NoDataPanel.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                LoadingGrid.Visibility = Visibility.Collapsed;
                NoDataPanel.Visibility = Visibility.Visible;
                MessageBox.Show($"加载获奖信息失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 处理获奖文本，支持多种分隔符
        /// </summary>
        private List<string> ProcessAwardsText(string awardsText)
        {
            if (string.IsNullOrWhiteSpace(awardsText))
                return new List<string>();

            // 支持的分隔符：逗号、分号、换行符、竖线等
            var separators = new char[] { ',', ';', '\n', '\r', '|', '、' };

            var awards = awardsText.Split(separators, StringSplitOptions.RemoveEmptyEntries)
                                  .Select(a => a.Trim())
                                  .Where(a => !string.IsNullOrWhiteSpace(a))
                                  .ToList();

            return awards;
        }

        /// <summary>
        /// 创建获奖标签按钮
        /// </summary>
        private void CreateAwardTags()
        {
            AwardsPanel.Children.Clear();

            foreach (var award in _allAwards)
            {
                var button = new Button
                {
                    Content = award,
                    Style = (Style)FindResource("AwardTagStyle"),
                    Tag = award
                };

                button.Click += AwardTag_Click;
                AwardsPanel.Children.Add(button);
            }
        }

        /// <summary>
        /// 获奖标签点击事件
        /// </summary>
        private void AwardTag_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string selectedAward)
            {
                // 调用回调函数，传递选中的获奖信息
                _onAwardSelected?.Invoke(selectedAward);

                // 关闭窗口
                this.Close();
            }
        }

        /// <summary>
        /// 清除筛选按钮点击事件
        /// </summary>
        private void ClearFilterButton_Click(object sender, RoutedEventArgs e)
        {
            // 调用回调函数，传递空字符串表示清除筛选
            _onAwardSelected?.Invoke(string.Empty);

            // 关闭窗口
            this.Close();
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
