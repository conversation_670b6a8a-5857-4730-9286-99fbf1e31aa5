using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Supabase;

namespace WpfApp
{
    public partial class AwardsFilterWindow : Window
    {
        private readonly Supabase.Client _supabaseClient;
        private readonly Action<string> _onAwardSelected;
        private List<string> _allAwards;

        public AwardsFilterWindow(Supabase.Client supabaseClient, Action<string> onAwardSelected)
        {
            InitializeComponent();
            _supabaseClient = supabaseClient;
            _onAwardSelected = onAwardSelected;

            // 窗口加载完成后加载数据
            Loaded += AwardsFilterWindow_Loaded;
        }

        private async void AwardsFilterWindow_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadAwardsData();
        }

        /// <summary>
        /// 从数据库加载所有获奖信息并分类显示
        /// </summary>
        private async Task LoadAwardsData()
        {
            try
            {
                // 显示加载状态
                LoadingGrid.Visibility = Visibility.Visible;
                AwardsScrollViewer.Visibility = Visibility.Collapsed;
                NoDataPanel.Visibility = Visibility.Collapsed;

                // 从数据库获取所有电影的获奖信息
                var response = await _supabaseClient
                    .From<MovieItem>()
                    .Select(x => new object[] { x.Awards })
                    .Filter("ccprize", Supabase.Postgrest.Constants.Operator.Not, "null")
                    .Get();

                var movies = response.Models;

                // 提取并处理所有获奖信息
                var allAwardsSet = new HashSet<string>();

                foreach (var movie in movies)
                {
                    if (!string.IsNullOrWhiteSpace(movie.Awards))
                    {
                        // 处理获奖信息，支持多种分隔符
                        var awards = ProcessAwardsText(movie.Awards);
                        foreach (var award in awards)
                        {
                            if (!string.IsNullOrWhiteSpace(award))
                            {
                                allAwardsSet.Add(award.Trim());
                            }
                        }
                    }
                }

                // 调试：输出前20个原始获奖信息
                var rawAwards = allAwardsSet.ToList();
                Console.WriteLine("=== 数据库中的原始获奖信息（前20个）===");
                for (int i = 0; i < Math.Min(20, rawAwards.Count); i++)
                {
                    Console.WriteLine($"{i + 1}: {rawAwards[i]}");
                }
                Console.WriteLine($"总共找到 {rawAwards.Count} 个不同的获奖信息");

                // 智能分类获奖信息
                var classifiedAwards = ClassifyAwards(rawAwards);

                // 调试：输出分类结果
                Console.WriteLine("=== 分类结果 ===");
                foreach (var category in classifiedAwards)
                {
                    Console.WriteLine($"{category.Key}: {category.Value.Count} 个项目");
                    if (category.Value.Count > 0)
                    {
                        Console.WriteLine($"  示例: {string.Join(", ", category.Value.Take(3))}");
                    }
                }

                // 提取分类标签作为最终显示的标签
                _allAwards = classifiedAwards.Keys.OrderBy(k => k).ToList();

                // 隐藏加载状态
                LoadingGrid.Visibility = Visibility.Collapsed;

                if (_allAwards.Count > 0)
                {
                    // 显示获奖标签
                    CreateAwardTags();
                    AwardsScrollViewer.Visibility = Visibility.Visible;
                }
                else
                {
                    // 显示无数据提示
                    NoDataPanel.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                LoadingGrid.Visibility = Visibility.Collapsed;
                NoDataPanel.Visibility = Visibility.Visible;
                MessageBox.Show($"加载获奖信息失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 处理获奖文本，支持多种分隔符
        /// </summary>
        private List<string> ProcessAwardsText(string awardsText)
        {
            if (string.IsNullOrWhiteSpace(awardsText))
                return new List<string>();

            // 支持的分隔符：逗号、分号、换行符、竖线等
            var separators = new char[] { ',', ';', '\n', '\r', '|', '、' };

            var awards = awardsText.Split(separators, StringSplitOptions.RemoveEmptyEntries)
                                  .Select(a => a.Trim())
                                  .Where(a => !string.IsNullOrWhiteSpace(a))
                                  .ToList();

            return awards;
        }

        /// <summary>
        /// 智能分类获奖信息
        /// </summary>
        private Dictionary<string, List<string>> ClassifyAwards(List<string> allAwards)
        {
            var classifications = new Dictionary<string, List<string>>();

            // 定义分类规则和关键词
            var categoryRules = new Dictionary<string, List<string>>
            {
                ["CC标准收藏"] = new List<string> { "cc", "criterion", "标准收藏", "标准", "收藏" },
                ["视与听影评人百佳"] = new List<string> { "视与听", "sight", "sound", "影评人", "百佳", "十佳" },
                ["豆瓣电影TOP250"] = new List<string> { "豆瓣", "douban", "top250", "top", "250" },
                ["奥斯卡奖项"] = new List<string> { "奥斯卡", "oscar", "academy", "学院奖" },
                ["戛纳电影节"] = new List<string> { "戛纳", "cannes", "金棕榈", "palme", "戛纳电影节" },
                ["威尼斯电影节"] = new List<string> { "威尼斯", "venice", "金狮", "lion", "威尼斯电影节" },
                ["柏林电影节"] = new List<string> { "柏林", "berlin", "金熊", "bear", "柏林电影节" },
                ["金球奖"] = new List<string> { "金球", "golden", "globe" },
                ["英国电影学院奖"] = new List<string> { "bafta", "英国电影", "学院奖" },
                ["美国导演工会奖"] = new List<string> { "导演工会", "dga", "directors", "guild" },
                ["美国编剧工会奖"] = new List<string> { "编剧工会", "wga", "writers", "guild" },
                ["圣丹斯电影节"] = new List<string> { "圣丹斯", "sundance" },
                ["多伦多电影节"] = new List<string> { "多伦多", "toronto", "tiff" },
                ["纽约影评人协会奖"] = new List<string> { "纽约影评", "new york", "critics" },
                ["洛杉矶影评人协会奖"] = new List<string> { "洛杉矶影评", "los angeles", "critics" },
                ["国家影评人协会奖"] = new List<string> { "国家影评", "national", "critics" },
                ["AFI百年系列"] = new List<string> { "afi", "american film institute", "百年", "century" },
                ["AFI TOP250"] = new List<string> { "afi top250", "afi top 250", "afi250", "afi 250" },
                ["IMDb Top 250"] = new List<string> { "imdb", "top250", "internet movie database", "imdb top250", "imdb top 250" },
                ["TSPDT TOP1000"] = new List<string> { "tspdt", "top1000", "top 1000", "they shoot pictures", "tspdt1000" },
                ["佳片有约"] = new List<string> { "佳片有约", "佳片", "有约" },
                ["乱耳字幕组"] = new List<string> { "乱耳", "字幕组", "乱耳字幕", "乱耳字幕组" },
                ["时代周刊"] = new List<string> { "时代", "time", "magazine", "时代周刊" },
                ["电影手册"] = new List<string> { "电影手册", "cahiers", "cinema" },
                ["帝国杂志"] = new List<string> { "帝国", "empire", "magazine", "帝国杂志" },
                ["娱乐周刊"] = new List<string> { "娱乐周刊", "entertainment", "weekly" },
                ["英国电影协会"] = new List<string> { "bfi", "british film institute", "英国电影协会" },
                ["法国电影手册"] = new List<string> { "cahiers du cinema", "电影手册", "法国电影手册" },
                ["纽约时报"] = new List<string> { "纽约时报", "new york times", "nyt" },
                ["卫报"] = new List<string> { "卫报", "guardian", "the guardian" },
                ["BBC文化"] = new List<string> { "bbc", "bbc culture", "bbc文化" },
                ["罗杰·伊伯特"] = new List<string> { "roger ebert", "伊伯特", "ebert" },
                ["村声杂志"] = new List<string> { "village voice", "村声", "村声杂志" }
            };

            // 为每个分类初始化列表
            foreach (var category in categoryRules.Keys)
            {
                classifications[category] = new List<string>();
            }

            // 未分类项目
            classifications["其他获奖"] = new List<string>();

            // 对每个获奖信息进行分类
            foreach (var award in allAwards)
            {
                var awardLower = award.ToLower();
                bool classified = false;

                foreach (var category in categoryRules)
                {
                    var categoryName = category.Key;
                    var keywords = category.Value;

                    // 检查是否包含任何关键词
                    if (keywords.Any(keyword => awardLower.Contains(keyword.ToLower())))
                    {
                        classifications[categoryName].Add(award);
                        classified = true;
                        break;
                    }
                }

                // 如果没有匹配任何分类，放入"其他获奖"
                if (!classified)
                {
                    classifications["其他获奖"].Add(award);
                }
            }

            // 移除空的分类
            var result = classifications.Where(c => c.Value.Count > 0)
                                     .ToDictionary(c => c.Key, c => c.Value);

            return result;
        }

        /// <summary>
        /// 创建获奖标签按钮
        /// </summary>
        private void CreateAwardTags()
        {
            AwardsPanel.Children.Clear();

            foreach (var award in _allAwards)
            {
                // 获取分类图标
                var icon = GetCategoryIcon(award);

                // 创建按钮内容
                var stackPanel = new StackPanel
                {
                    Orientation = Orientation.Horizontal,
                    VerticalAlignment = VerticalAlignment.Center
                };

                // 添加图标
                if (!string.IsNullOrEmpty(icon))
                {
                    var iconText = new TextBlock
                    {
                        Text = icon,
                        FontSize = 14,
                        Margin = new Thickness(0, 0, 8, 0),
                        VerticalAlignment = VerticalAlignment.Center
                    };
                    stackPanel.Children.Add(iconText);
                }

                // 添加分类名称
                var nameText = new TextBlock
                {
                    Text = award,
                    FontSize = 12,
                    FontWeight = FontWeights.SemiBold,
                    VerticalAlignment = VerticalAlignment.Center
                };
                stackPanel.Children.Add(nameText);

                var button = new Button
                {
                    Content = stackPanel,
                    Style = (Style)FindResource("AwardTagStyle"),
                    Tag = award,
                    MinWidth = 120,
                    Padding = new Thickness(15, 8, 15, 8)
                };

                button.Click += AwardTag_Click;
                AwardsPanel.Children.Add(button);
            }
        }

        /// <summary>
        /// 获取分类对应的图标
        /// </summary>
        private string GetCategoryIcon(string category)
        {
            var categoryIcons = new Dictionary<string, string>
            {
                ["CC标准收藏"] = "📀",
                ["视与听影评人百佳"] = "🎬",
                ["豆瓣电影TOP250"] = "⭐",
                ["奥斯卡奖项"] = "🏆",
                ["戛纳电影节"] = "🌴",
                ["威尼斯电影节"] = "🦁",
                ["柏林电影节"] = "🐻",
                ["金球奖"] = "🌟",
                ["英国电影学院奖"] = "🇬🇧",
                ["美国导演工会奖"] = "🎭",
                ["美国编剧工会奖"] = "✍️",
                ["圣丹斯电影节"] = "❄️",
                ["多伦多电影节"] = "🍁",
                ["纽约影评人协会奖"] = "🗽",
                ["洛杉矶影评人协会奖"] = "🌴",
                ["国家影评人协会奖"] = "🇺🇸",
                ["AFI百年系列"] = "📽️",
                ["AFI TOP250"] = "🎞️",
                ["IMDb Top 250"] = "🎯",
                ["TSPDT TOP1000"] = "📊",
                ["佳片有约"] = "📺",
                ["乱耳字幕组"] = "💬",
                ["时代周刊"] = "📰",
                ["电影手册"] = "📖",
                ["帝国杂志"] = "👑",
                ["娱乐周刊"] = "🎪",
                ["英国电影协会"] = "🎭",
                ["法国电影手册"] = "🇫🇷",
                ["纽约时报"] = "📄",
                ["卫报"] = "📰",
                ["BBC文化"] = "📻",
                ["罗杰·伊伯特"] = "👨‍💼",
                ["村声杂志"] = "🗞️",
                ["其他获奖"] = "🏅"
            };

            return categoryIcons.ContainsKey(category) ? categoryIcons[category] : "🏅";
        }

        /// <summary>
        /// 获奖标签点击事件
        /// </summary>
        private void AwardTag_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is string selectedAward)
            {
                // 调用回调函数，传递选中的获奖信息
                _onAwardSelected?.Invoke(selectedAward);

                // 关闭窗口
                this.Close();
            }
        }

        /// <summary>
        /// 清除筛选按钮点击事件
        /// </summary>
        private void ClearFilterButton_Click(object sender, RoutedEventArgs e)
        {
            // 调用回调函数，传递空字符串表示清除筛选
            _onAwardSelected?.Invoke(string.Empty);

            // 关闭窗口
            this.Close();
        }

        /// <summary>
        /// 关闭按钮点击事件
        /// </summary>
        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
