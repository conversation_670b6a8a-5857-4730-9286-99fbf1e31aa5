﻿<Window x:Class="WpfApp.EditCoffeeWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="编辑咖啡信息" Height="900" Width="500"
        Background="#121a30"
        Foreground="White"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize" WindowStyle="None" AllowsTransparency="True"
        BorderThickness="0">
    <!-- 统一样式 -->
    <Window.Resources>
        <!-- 窗口控制按钮样式 -->
        <Style x:Key="WindowButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="40"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#4A5AE8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#3A4AD8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>

        <Style x:Key="TextBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Width" Value="400"/>
            <Setter Property="HorizontalAlignment" Value="Left"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#4A5AE8"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <Style x:Key="ComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Width" Value="400"/>
            <Setter Property="HorizontalAlignment" Value="Left"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#4A5AE8"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <Style x:Key="DatePickerStyle" TargetType="DatePicker">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Width" Value="400"/>
            <Setter Property="HorizontalAlignment" Value="Left"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#4A5AE8"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="Black"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Width" Value="100"/>
            <Setter Property="Margin" Value="10,0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 自定义滚动条拖拽块样式 -->
        <Style x:Key="CustomScrollBarThumbStyle" TargetType="{x:Type Thumb}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Thumb}">
                        <Grid>
                            <Border Background="#4A5568" CornerRadius="4" Opacity="0.8"/>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 自定义滚动条样式 -->
        <Style x:Key="CustomScrollBarStyle" TargetType="{x:Type ScrollBar}">
            <Setter Property="Width" Value="8"/>
            <Setter Property="Background" Value="#2D3748"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ScrollBar}">
                        <Grid>
                            <Border Background="{TemplateBinding Background}" CornerRadius="4" Opacity="0.6"/>
                            <Track x:Name="PART_Track" IsDirectionReversed="True">
                                <Track.Thumb>
                                    <Thumb Style="{StaticResource CustomScrollBarThumbStyle}"/>
                                </Track.Thumb>
                            </Track>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 自定义滚动视图样式 -->
        <Style x:Key="CustomScrollViewerStyle" TargetType="{x:Type ScrollViewer}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ScrollViewer}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <ScrollContentPresenter Grid.Column="0" Grid.Row="0" Margin="0,0,8,0"/>

                            <ScrollBar x:Name="PART_VerticalScrollBar"
                                      Grid.Column="1" Grid.Row="0"
                                      Orientation="Vertical"
                                      Value="{TemplateBinding VerticalOffset}"
                                      Maximum="{TemplateBinding ScrollableHeight}"
                                      ViewportSize="{TemplateBinding ViewportHeight}"
                                      Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                                      Style="{StaticResource CustomScrollBarStyle}"/>

                            <ScrollBar x:Name="PART_HorizontalScrollBar"
                                      Grid.Column="0" Grid.Row="1"
                                      Orientation="Horizontal"
                                      Value="{TemplateBinding HorizontalOffset}"
                                      Maximum="{TemplateBinding ScrollableWidth}"
                                      ViewportSize="{TemplateBinding ViewportWidth}"
                                      Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                                      Style="{StaticResource CustomScrollBarStyle}"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border BorderThickness="1" BorderBrush="#1a2747" CornerRadius="8">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Border Grid.Row="0" MouseLeftButtonDown="TitleBar_MouseLeftButtonDown">
                <Border.Background>
                    <ImageBrush ImageSource="/img/head_bg.png" Stretch="UniformToFill"/>
                </Border.Background>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 标题文本 -->
                    <TextBlock Text="编辑咖啡信息" Foreground="White" FontSize="14"
                               VerticalAlignment="Center" Margin="15,0,0,0"/>

                    <!-- 窗口控制按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button x:Name="MinimizeButton" Style="{StaticResource WindowButtonStyle}"
                                Click="MinimizeButton_Click">
                            <TextBlock Text="&#xE921;" FontFamily="Segoe MDL2 Assets"
                                       FontSize="12" Foreground="White"/>
                        </Button>
                        <Button x:Name="MaximizeButton" Style="{StaticResource WindowButtonStyle}"
                                Click="MaximizeButton_Click">
                            <TextBlock Text="&#xE922;" FontFamily="Segoe MDL2 Assets"
                                       FontSize="12" Foreground="White"/>
                        </Button>
                        <Button x:Name="CloseButton" Style="{StaticResource WindowButtonStyle}"
                                Click="CloseButton_Click">
                            <TextBlock Text="&#xE8BB;" FontFamily="Segoe MDL2 Assets"
                                       FontSize="12" Foreground="White"/>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 内容区域 -->
            <Border Grid.Row="1" Background="Transparent" Margin="20" CornerRadius="8" Padding="20">

                <ScrollViewer VerticalScrollBarVisibility="Auto"
                             Style="{StaticResource CustomScrollViewerStyle}">
                    <StackPanel>
                        <!-- 名称 -->
                        <TextBlock Text="名称" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="txtName" Style="{StaticResource TextBoxStyle}"/>

                        <!-- 类型 -->
                        <TextBlock Text="类型" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="txtType" Style="{StaticResource TextBoxStyle}"/>

                        <!-- 价格 -->
                        <TextBlock Text="价格" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="txtPrice" Style="{StaticResource TextBoxStyle}"/>

                        <!-- 重量 -->
                        <TextBlock Text="重量" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="txtWeight" Style="{StaticResource TextBoxStyle}"/>

                        <!-- 产地 -->
                        <TextBlock Text="产地" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="txtCountry" Style="{StaticResource TextBoxStyle}"/>

                        <!-- 日期 -->
                        <TextBlock Text="日期" Style="{StaticResource LabelStyle}"/>
                        <DatePicker x:Name="dpDate" Style="{StaticResource DatePickerStyle}"/>

                        <!-- 状态 -->
                        <TextBlock Text="状态" Style="{StaticResource LabelStyle}"/>
                        <ComboBox x:Name="cmbStatus" Style="{StaticResource ComboBoxStyle}" >
                            <ComboBoxItem Content="在路上" Tag="road"/>
                            <ComboBoxItem Content="已到" Tag="new"/>
                            <ComboBoxItem Content="进行中" Tag="ing"/>
                            <ComboBoxItem Content="喝完" Tag="over"/>
                        </ComboBox>

                        <!-- 备注 -->
                        <TextBlock Text="备注" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="txtInfo" Style="{StaticResource TextBoxStyle}" Height="80" TextWrapping="Wrap"/>

                        <!-- 链接 -->
                        <TextBlock Text="链接" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="txtLink" Style="{StaticResource TextBoxStyle}"/>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <!-- 按钮区域 -->
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="20,10,20,20">
                <Button x:Name="btnSave" Content="保存" Style="{StaticResource ButtonStyle}" Background="#4B91F1" Click="btnSave_Click"/>
                <Button x:Name="btnDel" Content="删除" Style="{StaticResource ButtonStyle}" Background="#E74C3C" Click="btnDel_Click"/>
                <Button x:Name="btnCancel" Content="取消" Style="{StaticResource ButtonStyle}" Background="#6C757D" Click="btnCancel_Click"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>