﻿<Window x:Class="WpfApp.MovieDetailWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="电影详情" Height="900" Width="1000"
        Background="Transparent"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        AllowsTransparency="True">
    <Window.Resources>
        <!-- 标签样式 -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#8890AD"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>

        <!-- 内容样式 -->
        <Style x:Key="ContentStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
        </Style>

        <!-- 日期样式 -->
        <Style x:Key="DateStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#8890AD"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
        </Style>

        <!-- 导演信息样式 -->
        <Style x:Key="DirectorStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,10,0,15"/>
        </Style>

        <!-- 评分标签样式 -->
        <Style x:Key="RatingStyle" TargetType="Border">
            <Setter Property="Background" Value="#2D3748"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="HorizontalAlignment" Value="Left"/>
            <Setter Property="VerticalAlignment" Value="Top"/>
        </Style>

        <!-- 获奖标签样式 -->
        <Style x:Key="AwardTagStyle" TargetType="Border">
            <Setter Property="Background" Value="#FF7A00"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="0,15,0,0"/>
            <Setter Property="HorizontalAlignment" Value="Left"/>
        </Style>

        <!-- 磁力链接按钮样式 -->
        <Style x:Key="MagnetButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#667EEA"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Height" Value="45"/>
            <Setter Property="Width" Value="140"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="22"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#5A67D8"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 字幕链接按钮样式 -->
        <Style x:Key="SubtitleButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2D9B69"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Height" Value="45"/>
            <Setter Property="Width" Value="140"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="22"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#28805C"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 豆瓣链接按钮样式 -->
        <Style x:Key="DoubanButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#48BB78"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Height" Value="45"/>
            <Setter Property="Width" Value="140"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="22"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#38A169"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 关闭按钮样式 -->
        <Style x:Key="CloseButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="#8890AD"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="30"/>
            <Setter Property="Height" Value="30"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="15">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#4A5568"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 自定义滚动条拖拽块样式 -->
        <Style x:Key="CustomScrollBarThumbStyle" TargetType="{x:Type Thumb}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Thumb}">
                        <Grid>
                            <Border Background="#4A5568" CornerRadius="4" Opacity="0.8"/>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 自定义滚动条样式 -->
        <Style x:Key="CustomScrollBarStyle" TargetType="{x:Type ScrollBar}">
            <Setter Property="Width" Value="8"/>
            <Setter Property="Background" Value="#2D3748"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ScrollBar}">
                        <Grid>
                            <Border Background="{TemplateBinding Background}" CornerRadius="4" Opacity="0.6"/>
                            <Track x:Name="PART_Track" IsDirectionReversed="True">
                                <Track.Thumb>
                                    <Thumb Style="{StaticResource CustomScrollBarThumbStyle}"/>
                                </Track.Thumb>
                            </Track>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 自定义滚动视图样式 -->
        <Style x:Key="CustomScrollViewerStyle" TargetType="{x:Type ScrollViewer}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ScrollViewer}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <ScrollContentPresenter Grid.Column="0" Grid.Row="0" Margin="0,0,8,0"/>

                            <ScrollBar x:Name="PART_VerticalScrollBar"
                                      Grid.Column="1" Grid.Row="0"
                                      Orientation="Vertical"
                                      Value="{TemplateBinding VerticalOffset}"
                                      Maximum="{TemplateBinding ScrollableHeight}"
                                      ViewportSize="{TemplateBinding ViewportHeight}"
                                      Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                                      Style="{StaticResource CustomScrollBarStyle}"/>

                            <ScrollBar x:Name="PART_HorizontalScrollBar"
                                      Grid.Column="0" Grid.Row="1"
                                      Orientation="Horizontal"
                                      Value="{TemplateBinding HorizontalOffset}"
                                      Maximum="{TemplateBinding ScrollableWidth}"
                                      ViewportSize="{TemplateBinding ViewportWidth}"
                                      Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                                      Style="{StaticResource CustomScrollBarStyle}"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <!-- 半透明背景 -->
    <Border Background="#80000000" MouseLeftButtonDown="Background_MouseLeftButtonDown">
        <!-- 主内容区域 -->
        <Border Width="600"
                Height="800"
                CornerRadius="20"
                HorizontalAlignment="Right"
                VerticalAlignment="Center"
                Margin="0,0,50,0">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="#1A202C" Offset="0"/>
                    <GradientStop Color="#2D3748" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <Border.Effect>
                <DropShadowEffect ShadowDepth="5" BlurRadius="20" Opacity="0.3" Color="Black"/>
            </Border.Effect>

            <Grid Margin="30">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 头部区域 -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0">
                        <!-- 评分标签 -->
                        <Border Style="{StaticResource RatingStyle}" Margin="0,0,0,15">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="⭐" FontSize="14" Foreground="#FFD700" VerticalAlignment="Center"/>
                                <TextBlock x:Name="RatingText" Text="8" FontSize="14" FontWeight="Bold"
                                          Foreground="White" VerticalAlignment="Center" Margin="5,0,0,0"/>
                            </StackPanel>
                        </Border>

                        <!-- 电影标题 -->
                        <TextBlock x:Name="MovieTitle" Text="电影标题" FontSize="28" FontWeight="Bold"
                                  Foreground="White" Margin="0,0,0,10" TextWrapping="Wrap"/>

                        <!-- 获奖标签 -->
                        <Border x:Name="AwardTag" Style="{StaticResource AwardTagStyle}" Visibility="Collapsed">
                            <TextBlock x:Name="AwardTagText" Text="第74届柏林国际电影节获奖名单"
                                      FontSize="13" FontWeight="SemiBold" Foreground="White"/>
                        </Border>
                    </StackPanel>

                    <!-- 关闭按钮 -->
                    <Button Grid.Column="1" Content="✕" Style="{StaticResource CloseButtonStyle}"
                           Click="CloseButton_Click" VerticalAlignment="Top"/>
                </Grid>

                <!-- 内容区域 -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="0,20,0,0"
                             Style="{StaticResource CustomScrollViewerStyle}">
                    <StackPanel>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="200"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- 左侧海报 -->
                            <Border Grid.Column="0" Width="180" Height="250" CornerRadius="12"
                                   Margin="0,0,20,0" Background="#2D3748">
                                <Border.Effect>
                                    <DropShadowEffect ShadowDepth="3" BlurRadius="8" Opacity="0.3" Color="Black"/>
                                </Border.Effect>
                                <Image x:Name="PosterImage" Stretch="UniformToFill"
                                      RenderOptions.BitmapScalingMode="HighQuality"/>
                            </Border>

                            <!-- 右侧信息 -->
                            <StackPanel Grid.Column="1">
                                <!-- 日期 -->
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                    <TextBlock Text="📅" FontSize="14" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                    <TextBlock x:Name="DateText" Text="2024-11-15" Style="{StaticResource DateStyle}"
                                              VerticalAlignment="Center" Margin="0"/>
                                </StackPanel>

                                <!-- 导演信息 -->
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                    <TextBlock Text="🎬" FontSize="14" VerticalAlignment="Top" Margin="0,2,8,0"/>
                                    <StackPanel>
                                        <TextBlock x:Name="DirectorText" Text="Pham Ngoc Lan"
                                                  Style="{StaticResource DirectorStyle}"
                                                  Margin="0,0,0,5"/>
                                        <Border Background="#4A5568" CornerRadius="8" Padding="8,3"
                                               HorizontalAlignment="Left">
                                            <TextBlock x:Name="CountryText" Text="法国" FontSize="12"
                                                      Foreground="White"/>
                                        </Border>
                                    </StackPanel>
                                </StackPanel>
                            </StackPanel>
                        </Grid>

                        <!-- 电影简介 -->
                        <StackPanel Margin="0,30,0,0">
                            <TextBlock Text="电影简介" FontSize="16" FontWeight="SemiBold"
                                      Foreground="White" Margin="0,0,0,15"/>
                            <TextBlock x:Name="GenreText"
                                      Text="越南新锐导演Pham Ngoc Lan导演的短片《异乡》《福地》都曾入围柏林国际电影节，而他的长片《懒猴从不哭泣》则入围了今年的第74届柏林国际电影节全景单元，并获最佳长片首作奖（GWFF BEST FIRST FEATURE）。片中女主角带着前夫的骨灰和他喜爱的小懒猴回到河内，而一对年轻人则试图去掉传统负担追逐着他们的婚礼。导演用黑白影像营造了一个通过气氛推动故事的叙事空间，让当下的画面和历史的记忆渐渐交织在一起，模糊身份的主观镜头带来更多的沉思时刻，主人公们对幸福充满困惑，但从未停止追寻。"
                                      Style="{StaticResource ContentStyle}"
                                      LineHeight="22"/>
                        </StackPanel>

                        <!-- 获奖详情 -->
                        <StackPanel x:Name="AwardsDetail" Margin="0,20,0,0" Visibility="Collapsed">
                            <TextBlock Text="获奖情况" FontSize="16" FontWeight="SemiBold"
                                      Foreground="White" Margin="0,0,0,15"/>
                            <Border Background="#4A5AE8" CornerRadius="12" Padding="20">
                                <Border.Effect>
                                    <DropShadowEffect ShadowDepth="2" BlurRadius="8" Opacity="0.3" Color="Black"/>
                                </Border.Effect>
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                                        <TextBlock Text="🏆" FontSize="16" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                        <TextBlock Text="电影奖项" FontSize="16" FontWeight="SemiBold"
                                                  Foreground="White" VerticalAlignment="Center"/>
                                    </StackPanel>
                                    <TextBlock x:Name="AwardsText" Text="" Style="{StaticResource ContentStyle}"
                                              TextWrapping="Wrap" LineHeight="20" Foreground="#E8F0FF" Margin="0"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </StackPanel>
                </ScrollViewer>

                <!-- 底部按钮区域 -->
                <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center"
                           Margin="0,20,0,0">
                    <Button x:Name="MagnetButton" Content="🧲 磁力链接" Style="{StaticResource MagnetButtonStyle}"
                           Margin="0,0,15,0" Click="MagnetButton_Click"/>
                    <Button x:Name="SubtitleButton" Content="↩ 字幕链接" Style="{StaticResource SubtitleButtonStyle}"
                           Margin="0,0,15,0" Click="SubtitleButton_Click"/>
                    <Button x:Name="DoubanButton" Content="🌿 豆瓣链接" Style="{StaticResource DoubanButtonStyle}"
                           Click="DoubanButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Border>
</Window>
