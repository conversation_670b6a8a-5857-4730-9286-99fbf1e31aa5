﻿using System.Collections.Generic;
using System.Security.Authentication;
using System.Security.Policy;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using LiveCharts;
using LiveCharts.Wpf;
using Supabase;
using Supabase.Postgrest;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System;
using System.Globalization;
using System.Collections.ObjectModel;
using System.Windows.Data;
using System.IO;
using Microsoft.Win32;
using Microsoft.WindowsAPICodePack.Dialogs;
using Microsoft.Web.WebView2.Core;
using Microsoft.Web.WebView2.Wpf;
using System.Xml.Linq;

using Supabase.Postgrest.Models;
using Supabase.Postgrest.Attributes;
using static Supabase.Postgrest.Constants;

using Microsoft.VisualBasic;
using LiveCharts.Defaults;
using System.Threading;
using WpfAdmin.Services;
using WpfAdmin.Models;
using WpfApp.Services;
using WpfApp.Models;

namespace WpfApp
{
    // 反向布尔值到可见性转换器
    public class InverseBoolToVisibilityConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool boolValue = (bool)value;
            return boolValue ? Visibility.Collapsed : Visibility.Visible;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            Visibility visibility = (Visibility)value;
            return visibility == Visibility.Collapsed;
        }
    }

    // 聊天消息背景颜色转换器
    public class BoolToBackgroundConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool isUser = (bool)value;
            return isUser ? new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2C3E50"))
                          : new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    // 聊天消息头像背景颜色转换器
    public class BoolToAvatarBgConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool isUser = (bool)value;
            return isUser ? new SolidColorBrush((Color)ColorConverter.ConvertFromString("#5D6EFF"))
                          : new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF5D6E"));
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    // 聊天消息头像图标转换器
    public class BoolToAvatarConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool isUser = (bool)value;
            return isUser ? "\uE77B" : "\uE756"; // User icon vs AI icon
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    // 聊天消息模型
    public class ChatMessage
    {
        public string Sender { get; set; }
        public string Content { get; set; }
        public string Timestamp { get; set; }
        public bool IsUser { get; set; }
    }

    // 用户自选股模型
    [Table("userstocks")]
    public class UserStock : BaseModel
    {
        [Column("code")]
        public string Code { get; set; }

        [Column("name")]
        public string Name { get; set; }

        [Column("user_id")]
        public string UserId { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }
    }
}

namespace WpfApp
{
    // 创建与数据库表对应的模型类
    // 使用特性标记表名和字段名

    [Table("movinfo")]
    public class MovieDirector : BaseModel
    {
        // 添加无参数构造函数
        public MovieDirector() { }

        [PrimaryKey("id")]
        [Column("id")]
        public int Id { get; set; }

        [Column("directorename")]
        public string DirectorEnglishName { get; set; }

        [Column("directorchname")]
        public string DirectorChineseName { get; set; }
    }

    [Table("movlist")]
    public class MovieItem : BaseModel
    {
        public MovieItem() { }


        [PrimaryKey("id", true)]
        [Column("id")]
        public int Id { get; set; }

        [Column("mimglink")]
        public string PosterUrl { get; set; }

        [Column("mname")]
        public string Title { get; set; }

        [Column("director")]
        public string Director { get; set; }

        [Column("countyname")]
        public string Country { get; set; }

        [Column("mintro")]
        public string Intro { get; set; }

        [Column("mstar")]
        public string Rating { get; set; }

        [Column("mmagnet")]
        public string Magnet { get; set; }

        [Column("msublink")]
        public string Sublink { get; set; }

        [Column("douban")]
        public string Douban { get; set; }

        [Column("mdate")]
        public string ReleaseDate { get; set; }

        [Column("ccprize")]
        public string Awards { get; set; }

        [Column("created_at")]
        public DateTime CreatedAt { get; set; }
    }

    [Table("coffeelist")]
    public class CoffeeItemDb : BaseModel
    {
        public CoffeeItemDb() { }

        // 如果数据库中id是数值类型
        [PrimaryKey("id")]
        [Column("id")]
        public int Id { get; set; } // 改为int类型，不要使用GUID


        [Column("name")]
        public string Name { get; set; }

        [Column("type")]
        public string Type { get; set; }

        [Column("price")]
        public int Price { get; set; }

        [Column("weight")]
        public int Weight { get; set; }

        [Column("country")]
        public string Country { get; set; }

        [Column("date")]
        public string Date { get; set; }

        [Column("status")]
        public string Status { get; set; }

        [Column("memo")]
        public string Memo { get; set; }

        [Column("link")]
        public string Link { get; set; }


    }

    // 用于查询和显示的模型
    [Table("coffeelist")]
    public class CoffeeItem : CoffeeItemDb
    {

        // 这里不需要定义数据库列，因为已经从基类继承
        // 只需添加计算属性
        public string StatusText => Status switch
        {
            "road" => "在路上",
            "new" => "已到",
            "ing" => "进行中",
            "over" => "喝完",
            _ => Status
        };
    }


    public partial class MainWindow : Window
    {
        private const string supabaseUrl = "https://vvwysksbbuulpqzgegty.supabase.co";
        private const string supabaseKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.022fsVNes6LtXdzxyLvuX1Fv3OYdLHq4shmmWdPG29o";
        private Supabase.Client _supabaseClient;
        private readonly StockService _stockService; // 添加股票服务
        private readonly ExportService _exportService; // 添加导出服务
        private readonly MusicService _musicService; // 添加音乐服务
        private readonly HealthService _healthService; // 添加健康服务
        private readonly BloodPressureService _bloodPressureService; // 添加血压服务
        private readonly NewsService _newsService; // 添加新闻服务
        private readonly HttpClient _httpClient;
        private ObservableCollection<ChatMessage> _chatMessages;
        private ObservableCollection<ChatMessage> _volcanoMessages;
        private ObservableCollection<CustomStock> _userStocks; // 存储用户自选股数据
        private const string DeepSeekApiKey = "***********************************";
        private const string DeepSeekApiUrl = "https://api.deepseek.com/v1/chat/completions";
        private readonly TMDBService _tmdbService; // 添加TMDB服务

        // 火山引擎API相关常量
        private const string VolcanoApiKey = "49aa4b62-8b11-49c1-8a5c-c76274ccc26a";
        private const string VolcanoApiUrl = "https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions";
        private const string VolcanoAppId = "bot-20250220221746-vtshb";
        private readonly HttpClient _volcanoClient;

        // 高德地图API相关常量
        // 注意：这是一个示例密钥，实际使用时需要申请有效的API密钥
        private const string AmapApiKey = "your_amap_api_key_here";
        private const string AmapSecurityKey = "your_amap_security_key_here";
        private bool _isMapInitialized = false;
        private string _currentKmlContent = string.Empty;

        // 图表数据属性
        public SeriesCollection MonthlyExpenseSeries { get; set; }
        public SeriesCollection CountryDistributionSeries { get; set; }
        public SeriesCollection CoffeeTypeSeries { get; set; }
        public SeriesCollection CoffeeStatusSeries { get; set; }
        public string[] MonthLabels { get; set; }
        public Func<double, string> YFormatter { get; set; }
        public Func<ChartPoint, string> PointLabel { get; set; }

        public MainWindow()
        {
            InitializeComponent();

            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(60);
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", DeepSeekApiKey);
            _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            _volcanoClient = new HttpClient();
            _volcanoClient.Timeout = TimeSpan.FromSeconds(60);
            _volcanoClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", VolcanoApiKey);
            _volcanoClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            // 初始化TMDB服务
            _tmdbService = new TMDBService();

            _supabaseClient = new Supabase.Client(supabaseUrl, supabaseKey);
            _stockService = new StockService(_supabaseClient);  // 传入 Supabase 客户端
            _exportService = new ExportService(_supabaseClient);  // 初始化导出服务
            _musicService = new MusicService(_supabaseClient);  // 初始化音乐服务
            _healthService = new HealthService();  // 初始化健康服务（使用本地CSV）
            _bloodPressureService = new BloodPressureService();  // 初始化血压服务（使用本地CSV）
            _newsService = new NewsService();  // 初始化新闻服务

            // 初始化 Supabase 客户端配置
            var options = new Supabase.SupabaseOptions
            {
                AutoConnectRealtime = true,
                AutoRefreshToken = true
            };

            // 初始化 Supabase 客户端
            _supabaseClient = new Supabase.Client(
                supabaseUrl,
                supabaseKey,
                options
            );

            // 初始化聊天消息集合
            _chatMessages = new ObservableCollection<ChatMessage>();
            _volcanoMessages = new ObservableCollection<ChatMessage>();
            // ChatMessagesControl.ItemsSource = _chatMessages; // 已移除
            VolcanoMessagesControl.ItemsSource = _volcanoMessages;

            // 添加欢迎消息
            _chatMessages.Add(new ChatMessage
            {
                Sender = "DeepSeek R1",
                Content = "你好！我是DeepSeek R1 AI助手，有什么我可以帮助你的吗？",
                Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                IsUser = false
            });

            // 添加火山引擎欢迎消息
            _volcanoMessages.Add(new ChatMessage
            {
                Sender = "火山方舟",
                Content = "你好！我是火山方舟AI助手，有什么我可以帮助你的吗？",
                Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                IsUser = false
            });

            // 默认显示仪表板
            DashboardContent.Visibility = Visibility.Visible;
            FilmContent.Visibility = Visibility.Collapsed;
            MessagesContent.Visibility = Visibility.Collapsed;
            CoffeeContent.Visibility = Visibility.Collapsed;
            AIToolContent.Visibility = Visibility.Collapsed;
            VolcanoContent.Visibility = Visibility.Collapsed;
            CoffeeAnalysisContent.Visibility = Visibility.Collapsed;

            // 设置数据上下文
            DataContext = this;

            // 模拟数据
            SeriesCollection = new SeriesCollection
            {
                new LineSeries
                {
                    Values = new ChartValues<double> { 50, 150, 400, 250, 350, 200, 100 },
                    Stroke = System.Windows.Media.Brushes.Purple,
                    Fill = null,
                    StrokeThickness = 2
                },
                new LineSeries
                {
                    Values = new ChartValues<double> { 100, 200, 450, 300, 400, 250, 150 },
                    Stroke = (System.Windows.Media.Brush)(new System.Windows.Media.BrushConverter().ConvertFrom("#b39ddb")),
                    Fill = null,
                    StrokeThickness = 2
                }
            };

            Labels = new[] { "-5", "0", "1", "2", "3", "4", "5" };
            YFormatter = value => value.ToString("N0");

            // 初始化健康数据
            LoadHealthData();

            // 初始化地图
            InitializeMap();

            // 预加载新闻数据（在后台加载，不阻塞UI）
            _ = Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(3000); // 延迟3秒，让其他重要数据先加载
                    await Dispatcher.InvokeAsync(async () =>
                    {
                        await PreloadNewsData();
                    });
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"预加载新闻失败: {ex.Message}");
                }
            });
        }


        private async void LoadMessageData()
        {
            try
            {

                // 明确指定从movinfo表获取数据

                var response = await _supabaseClient
                    .From<MovieDirector>()
                    .Select(x => new object[] { x.DirectorEnglishName, x.DirectorChineseName })
                    .Get();

                // 将数据绑定到datagrid
                DirectorsDataGrid.ItemsSource = response.Models;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 处理Messages按钮点击事件
        /// </summary>
        private async void MessagesButton_Click(object sender, RoutedEventArgs e)
        {
            // 切换到Messages视图
            SwitchView(MessagesContent);

            LoadMessageData();
        }


        /// <summary>
        /// 处理film按钮点击事件
        /// </summary>
        private async void FilmButton_Click(object sender, RoutedEventArgs e)
        {
            // 切换到Messages视图
            SwitchView(FilmContent);
        }

        /// <summary>
        /// 处理Dashboard按钮点击事件
        /// </summary>
        private void DashboardButton_Click(object sender, RoutedEventArgs e)
        {
            // 切换到Dashboard视图
            SwitchView(DashboardContent);
        }

        /// <summary>
        /// 处理健康管理按钮点击事件
        /// </summary>
        private void HealthButton_Click(object sender, RoutedEventArgs e)
        {
            // 切换到健康管理视图（即Dashboard视图）
            SwitchView(DashboardContent);
            // 刷新健康数据
            LoadHealthData();
        }

        /// <summary>
        /// 处理Coffee按钮点击事件
        /// </summary>
        private async void CoffeeButton_Click(object sender, RoutedEventArgs e)
        {
            // 切换到Coffee视图
            SwitchView(CoffeeContent);

            // 显示进度条
            CoffeeLoadingProgress.Visibility = Visibility.Visible;

            try
            {
                // 加载咖啡数据（已在LoadCoffeeData方法中设置为按时间降序排列）
                LoadCoffeeData();
            }
            finally
            {
                // 进度条会在LoadCoffeeData方法中隐藏
            }
        }

        /// <summary>
        /// 处理咖啡管理按钮点击事件
        /// </summary>
        private void CoffeeManagementButton_Click(object sender, RoutedEventArgs e)
        {
            // 和CoffeeButton_Click功能相同
            CoffeeButton_Click(sender, e);
        }

        /// <summary>
        /// 处理Listing按钮点击事件
        /// </summary>
        private void ListingButton_Click(object sender, RoutedEventArgs e)
        {
            // 切换到工具集视图
            SwitchView(AIToolContent);

            // 确保地图已初始化
            if (!_isMapInitialized)
            {
                InitializeMap();
            }
        }

        /// <summary>
        /// 切换视图的通用方法
        /// </summary>
        private void SwitchView(UIElement viewToShow)
        {
            // 隐藏所有视图
            DashboardContent.Visibility = Visibility.Collapsed;
            FilmContent.Visibility = Visibility.Collapsed;
            MessagesContent.Visibility = Visibility.Collapsed;
            CoffeeContent.Visibility = Visibility.Collapsed;
            MovieContent.Visibility = Visibility.Collapsed;
            StockContent.Visibility = Visibility.Collapsed;
            AIToolContent.Visibility = Visibility.Collapsed;
            VolcanoContent.Visibility = Visibility.Collapsed;
            CoffeeAnalysisContent.Visibility = Visibility.Collapsed;
            MusicContent.Visibility = Visibility.Collapsed; // 添加音乐资料视图的隐藏逻辑
            NewsContent.Visibility = Visibility.Collapsed; // 添加新闻推送视图的隐藏逻辑

            // 显示所需视图，并添加淡入动画
            viewToShow.Visibility = Visibility.Visible;

            // 创建淡入动画
            DoubleAnimation fadeInAnimation = new DoubleAnimation
            {
                From = 0.0,
                To = 1.0,
                Duration = new Duration(TimeSpan.FromSeconds(0.3))
            };

            // 应用动画
            viewToShow.BeginAnimation(UIElement.OpacityProperty, fadeInAnimation);
        }

        /// <summary>
        /// 处理音乐资料按钮点击事件
        /// </summary>
        private async void MusicDataButton_Click(object sender, RoutedEventArgs e)
        {
            // 切换到音乐视图
            SwitchView(MusicContent);

            // 加载音乐数据
            await LoadMusicData();
        }

        /// <summary>
        /// 加载音乐数据
        /// </summary>
        private async Task LoadMusicData()
        {
            try
            {
                // 显示加载进度条
                MusicLoadingProgressGrid.Visibility = Visibility.Visible;
                MusicItemsControl.Visibility = Visibility.Collapsed;
                NoMusicDataText.Visibility = Visibility.Collapsed;

                // 音乐服务已在构造函数中初始化

                // 获取所有音乐数据
                var musicList = await _musicService.GetAllMusicAsync();

                // 更新记录数量显示
                MusicCountText.Text = $"({musicList.Count}条记录)";

                if (musicList.Count > 0)
                {
                    // 显示音乐列表
                    MusicItemsControl.ItemsSource = musicList;
                    MusicItemsControl.Visibility = Visibility.Visible;
                    NoMusicDataText.Visibility = Visibility.Collapsed;

                    // 自动加载离线图片
                    await LoadOfflineAlbumImagesAsync(musicList);
                }
                else
                {
                    // 显示无数据提示
                    MusicItemsControl.Visibility = Visibility.Collapsed;
                    NoMusicDataText.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载音乐数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                NoMusicDataText.Visibility = Visibility.Visible;
                MusicItemsControl.Visibility = Visibility.Collapsed;
            }
            finally
            {
                // 隐藏加载进度条
                MusicLoadingProgressGrid.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 处理音乐搜索框按下回车键事件
        /// </summary>
        private async void MusicSearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                await SearchMusic();
            }
        }

        /// <summary>
        /// 处理音乐搜索按钮点击事件
        /// </summary>
        private async void MusicSearchButton_Click(object sender, RoutedEventArgs e)
        {
            await SearchMusic();
        }

        /// <summary>
        /// 搜索音乐
        /// </summary>
        private async Task SearchMusic()
        {
            string keyword = MusicSearchTextBox.Text?.Trim();
            if (string.IsNullOrEmpty(keyword))
            {
                // 如果搜索框为空，则加载所有音乐
                await LoadMusicData();
                return;
            }

            try
            {
                // 显示加载进度条
                MusicLoadingProgressGrid.Visibility = Visibility.Visible;
                MusicItemsControl.Visibility = Visibility.Collapsed;
                NoMusicDataText.Visibility = Visibility.Collapsed;

                // 音乐服务已在构造函数中初始化

                // 搜索音乐
                var musicList = await _musicService.SearchMusicAsync(keyword);

                // 更新记录数量显示
                MusicCountText.Text = $"(找到{musicList.Count}条记录)";

                if (musicList.Count > 0)
                {
                    // 显示音乐列表
                    MusicItemsControl.ItemsSource = musicList;
                    MusicItemsControl.Visibility = Visibility.Visible;
                    NoMusicDataText.Visibility = Visibility.Collapsed;
                }
                else
                {
                    // 显示无数据提示
                    MusicItemsControl.Visibility = Visibility.Collapsed;
                    NoMusicDataText.Visibility = Visibility.Visible;
                    NoMusicDataText.Text = $"未找到与\"{keyword}\"相关的音乐";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"搜索音乐失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 隐藏加载进度条
                MusicLoadingProgressGrid.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 处理添加音乐按钮点击事件
        /// </summary>
        private void AddMusicButton_Click(object sender, RoutedEventArgs e)
        {
            // 创建添加音乐窗口
            var addMusicWindow = new AddMusicWindow(_supabaseClient);
            addMusicWindow.Owner = this;

            // 如果成功添加了音乐，则刷新音乐列表
            if (addMusicWindow.ShowDialog() == true)
            {
                LoadMusicData();
            }
        }

        /// <summary>
        /// 处理编辑音乐按钮点击事件
        /// </summary>
        private void EditMusic_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            if (button?.Tag is MusicItem music)
            {
                // 创建编辑音乐窗口
                var editMusicWindow = new EditMusicWindow(_supabaseClient, music);
                editMusicWindow.Owner = this;

                // 如果成功编辑了音乐，则刷新音乐列表
                if (editMusicWindow.ShowDialog() == true)
                {
                    LoadMusicData();
                }
            }
        }

        /// <summary>
        /// 处理删除音乐按钮点击事件
        /// </summary>
        private async void DeleteMusic_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            if (button?.Tag is MusicItem music)
            {
                // 确认删除
                var result = MessageBox.Show($"确定要删除音乐《{music.Title}》吗？", "确认删除",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // 音乐服务已在构造函数中初始化

                        // 删除音乐
                        bool success = await _musicService.DeleteMusicAsync(music.Id);

                        if (success)
                        {
                            MessageBox.Show($"音乐《{music.Title}》已成功删除！", "成功",
                                MessageBoxButton.OK, MessageBoxImage.Information);

                            // 刷新音乐列表
                            await LoadMusicData();
                        }
                        else
                        {
                            MessageBox.Show("删除音乐失败，请稍后重试。", "错误",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"删除音乐失败: {ex.Message}", "错误",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        /// <summary>
        /// 处理图片下载按钮点击事件
        /// </summary>
        private async void DownloadImagesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 获取当前显示的音乐列表
                if (MusicItemsControl.ItemsSource is List<MusicItem> musicList && musicList.Count > 0)
                {
                    // 创建并显示下载进度窗口
                    var progressWindow = new DownloadProgressWindow(musicList);
                    progressWindow.Owner = this;
                    progressWindow.ShowDialog();
                }
                else
                {
                    MessageBox.Show("没有可下载的图片", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"下载图片失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 处理AI分析图片按钮点击事件
        /// </summary>
        private async void AnalyzeImagesButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 获取当前显示的音乐列表
                if (MusicItemsControl.ItemsSource is List<MusicItem> musicList && musicList.Count > 0)
                {
                    // 检查Images目录是否存在
                    string imagesDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Images");
                    if (!Directory.Exists(imagesDirectory))
                    {
                        MessageBox.Show("未找到Images目录，请先下载专辑图片", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                        return;
                    }

                    // 检查是否有图片文件
                    var imageFiles = Directory.GetFiles(imagesDirectory, "album_*.*")
                        .Where(f => {
                            string ext = Path.GetExtension(f).ToLower();
                            return ext == ".jpg" || ext == ".jpeg" || ext == ".png" || ext == ".gif";
                        })
                        .ToList();

                    if (imageFiles.Count == 0)
                    {
                        MessageBox.Show("未找到专辑图片文件，请先下载专辑图片", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                        return;
                    }

                    // 创建并显示分析进度窗口
                    var progressWindow = new ImageAnalysisProgressWindow(musicList);
                    progressWindow.Owner = this;
                    progressWindow.ShowDialog();

                    // 分析完成后刷新显示
                    MusicItemsControl.Items.Refresh();
                }
                else
                {
                    MessageBox.Show("没有可分析的专辑", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"分析图片失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 自动加载离线专辑图片
        /// </summary>
        /// <param name="musicList">音乐专辑列表</param>
        /// <returns>更新的专辑数量</returns>
        private async Task<int> LoadOfflineAlbumImagesAsync(List<MusicItem> musicList)
        {
            try
            {
                if (musicList == null || musicList.Count == 0)
                {
                    return 0;
                }

                // 创建分析服务
                var analysisService = new AlbumImageAnalysisService();

                // 首先尝试从索引文件加载匹配关系
                var matchResults = analysisService.LoadAlbumIndexFromFile();

                // 如果索引文件中没有数据，则尝试分析图片
                if (matchResults.Count == 0)
                {
                    // 检查Images目录是否存在
                    string imagesDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Images");
                    if (!Directory.Exists(imagesDirectory))
                    {
                        return 0;
                    }

                    // 检查是否有图片文件
                    var imageFiles = Directory.GetFiles(imagesDirectory, "album_*.*")
                        .Where(f => {
                            string ext = Path.GetExtension(f).ToLower();
                            return ext == ".jpg" || ext == ".jpeg" || ext == ".png" || ext == ".gif";
                        })
                        .ToList();

                    if (imageFiles.Count == 0)
                    {
                        return 0;
                    }

                    // 分析并匹配图片
                    matchResults = await analysisService.AnalyzeAndMatchAlbumsAsync(musicList);
                }

                // 更新专辑的本地图片路径
                int updatedCount = analysisService.UpdateAlbumLocalImages(musicList, matchResults);

                // 刷新显示
                if (updatedCount > 0)
                {
                    Dispatcher.Invoke(() => {
                        MusicItemsControl.Items.Refresh();
                    });
                }

                return updatedCount;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"自动加载离线图片失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 处理电影资料按钮点击事件
        /// </summary>
        private void MusicButton_Click(object sender, RoutedEventArgs e)
        {
            // 切换到电影视图
            SwitchView(MovieContent);

            // 加载电影数据
            LoadMovieData();
        }

        /// <summary>
        /// 导出电影数据到CSV文件
        /// </summary>
        private async void ExportMovies_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 创建文件夹选择对话框
                var folderDialog = new CommonOpenFileDialog
                {
                    Title = "选择导出文件夹",
                    IsFolderPicker = true,
                    InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                    AddToMostRecentlyUsedList = false,
                    AllowNonFileSystemItems = false,
                    DefaultDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                    EnsureFileExists = true,
                    EnsurePathExists = true,
                    EnsureReadOnly = false,
                    EnsureValidNames = true,
                    Multiselect = false,
                    ShowPlacesList = true
                };

                // 显示对话框
                if (folderDialog.ShowDialog() != CommonFileDialogResult.Ok)
                {
                    return;
                }

                string folderPath = folderDialog.FileName;

                // 创建并显示进度对话框
                var progressWindow = new ExportProgressWindow();
                progressWindow.Owner = this;
                progressWindow.Show();

                // 开始导出操作
                List<string> exportedFiles = await _exportService.ExportMoviesToCsvAsync(folderPath, (current, total) =>
                {
                    // 更新进度
                    Dispatcher.Invoke(() => progressWindow.UpdateProgress(current, total));
                });

                // 导出完成
                Dispatcher.Invoke(() =>
                {
                    progressWindow.SetCompleted();

                    if (exportedFiles.Count > 0)
                    {
                        MessageBox.Show($"导出完成\n\n共导出 {exportedFiles.Count} 个文件到文件夹: {folderPath}", "导出成功", MessageBoxButton.OK, MessageBoxImage.Information);

                        // 打开导出文件夹
                        System.Diagnostics.Process.Start("explorer.exe", folderPath);
                    }
                    else
                    {
                        MessageBox.Show("没有电影数据可供导出", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出电影数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 加载电影数据
        /// </summary>
        // 分页相关变量
        private int _currentPage = 1;
        private int _pageSize = 20;
        private int _totalMovies = 0;
        private int _totalPages = 0;
        private List<MovieItem> _allMovies = new List<MovieItem>();
        // 添加排序相关变量
        private string _currentSortField = "id"; // 默认按ID排序
        private Ordering _currentSortOrder = Ordering.Descending; // 默认降序排列
        private string _lastFilterType = ""; // 记录最后一次筛选类型
        private MovieSearchResult _currentMovie; // 当前查询的电影信息，用于保存至数据库

        private async void LoadMovieData()
        {
            try
            {
                // 显示加载进度条
                MovieLoadingProgressGrid.Visibility = Visibility.Visible;
                MovieItemsControl.Visibility = Visibility.Collapsed;

                // 使用SQL查询获取总记录数
                var countResult = await _supabaseClient
                    .Rpc("get_film_count", new Dictionary<string, object>());


                // 假设返回的是一个包含count字段的对象
                _totalMovies = int.Parse(countResult.Content);

                if (_totalMovies <= 0)
                {
                    // 如果无法获取总记录数，可以使用一个较大的估计值
                    _totalMovies = 3000; // 您提到实际有超过2000条记录
                }

                _totalPages = (_totalMovies + _pageSize - 1) / _pageSize;
                TotalMoviesText.Text = _totalMovies.ToString();

                // 加载第一页数据
                await LoadPageData(_currentPage);

                // 更新分页按钮状态
                UpdatePaginationButtons();

                // 隐藏进度条，显示内容
                MovieLoadingProgressGrid.Visibility = Visibility.Collapsed;
                MovieItemsControl.Visibility = Visibility.Visible;
            }
            catch (Exception ex)
            {
                // 发生错误时也隐藏进度条
                MovieLoadingProgressGrid.Visibility = Visibility.Collapsed;
                MovieItemsControl.Visibility = Visibility.Visible;

                MessageBox.Show($"加载电影数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 修改LoadPageData方法，添加排序功能
        private async Task LoadPageData(int page)
        {
            try
            {
                // 显示加载进度条
                MovieLoadingProgressGrid.Visibility = Visibility.Visible;
                MovieItemsControl.Visibility = Visibility.Collapsed;

                int startIndex = (page - 1) * _pageSize;

                var query = _supabaseClient
                    .From<MovieItem>()
                    .Select(x => new object[] { x.PosterUrl, x.Title, x.Director, x.Country, x.Intro, x.Rating, x.Douban, x.Sublink, x.Magnet, x.ReleaseDate, x.Awards });

                // 根据当前排序字段和顺序进行排序
                if (_currentSortField == "rating")
                {
                    query = query.Order(x => x.Rating, _currentSortOrder);
                }
                else if (_currentSortField == "mdate")
                {
                    query = query.Order(x => x.ReleaseDate, _currentSortOrder);
                }
                else if (_currentSortField == "id")
                {
                    query = query.Order(x => x.Id, _currentSortOrder);
                }
                else
                {
                    // 默认按ID排序
                    query = query.Order(x => x.Id, Ordering.Descending);
                }

                var response = await query.Range(startIndex, startIndex + _pageSize - 1).Get();

                // 更新ItemsControl的数据源
                MovieItemsControl.ItemsSource = response.Models;

                // 隐藏进度条，显示内容
                MovieLoadingProgressGrid.Visibility = Visibility.Collapsed;
                MovieItemsControl.Visibility = Visibility.Visible;
            }
            catch (Exception ex)
            {
                // 发生错误时也隐藏进度条
                MovieLoadingProgressGrid.Visibility = Visibility.Collapsed;
                MovieItemsControl.Visibility = Visibility.Visible;

                MessageBox.Show($"加载页面数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 修改分页按钮点击事件
        private async void PageButton_Click(object sender, RoutedEventArgs e)
        {
            Button button = sender as Button;
            if (button != null && button.Tag != null)
            {
                int page = Convert.ToInt32(button.Tag);
                if (page != _currentPage && page >= 1 && page <= _totalPages)
                {
                    _currentPage = page;
                    await LoadPageData(_currentPage);
                    UpdatePaginationButtons();
                }
            }
        }

        // 同样修改上一页、下一页和跳转页面的事件处理方法
        private async void PreviousPage_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage > 1)
            {
                _currentPage--;
                await LoadPageData(_currentPage);
                UpdatePaginationButtons();
            }
        }

        private async void NextPage_Click(object sender, RoutedEventArgs e)
        {
            if (_currentPage < _totalPages)
            {
                _currentPage++;
                await LoadPageData(_currentPage);
                UpdatePaginationButtons();
            }
        }

        private async void JumpToPage_Click(object sender, RoutedEventArgs e)
        {
            if (int.TryParse(PageJumpTextBox.Text, out int page) && page >= 1 && page <= _totalPages)
            {
                _currentPage = page;
                await LoadPageData(_currentPage);
                UpdatePaginationButtons();
            }
            else
            {
                MessageBox.Show($"请输入1到{_totalPages}之间的页码", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }



        // 更新分页按钮状态
        private void UpdatePaginationButtons()
        {
            // 计算要显示的页码范围
            int startPage = Math.Max(1, _currentPage - 2);
            int endPage = Math.Min(_totalPages, startPage + 4);

            // 调整起始页，确保显示5个页码按钮（如果有足够的页数）
            if (endPage - startPage < 4 && _totalPages > 4)
            {
                startPage = Math.Max(1, endPage - 4);
            }

            // 更新页码按钮
            PageButton1.Content = startPage.ToString();
            PageButton1.Tag = startPage;
            PageButton1.Background = (startPage == _currentPage) ? (Brush)new BrushConverter().ConvertFrom("#5D6EFF") : (Brush)new BrushConverter().ConvertFrom("#293153");
            PageButton1.Visibility = (startPage <= _totalPages) ? Visibility.Visible : Visibility.Collapsed;

            PageButton2.Content = (startPage + 1).ToString();
            PageButton2.Tag = startPage + 1;
            PageButton2.Background = (startPage + 1 == _currentPage) ? (Brush)new BrushConverter().ConvertFrom("#5D6EFF") : (Brush)new BrushConverter().ConvertFrom("#293153");
            PageButton2.Visibility = (startPage + 1 <= _totalPages) ? Visibility.Visible : Visibility.Collapsed;

            PageButton3.Content = (startPage + 2).ToString();
            PageButton3.Tag = startPage + 2;
            PageButton3.Background = (startPage + 2 == _currentPage) ? (Brush)new BrushConverter().ConvertFrom("#5D6EFF") : (Brush)new BrushConverter().ConvertFrom("#293153");
            PageButton3.Visibility = (startPage + 2 <= _totalPages) ? Visibility.Visible : Visibility.Collapsed;

            PageButton4.Content = (startPage + 3).ToString();
            PageButton4.Tag = startPage + 3;
            PageButton4.Background = (startPage + 3 == _currentPage) ? (Brush)new BrushConverter().ConvertFrom("#5D6EFF") : (Brush)new BrushConverter().ConvertFrom("#293153");
            PageButton4.Visibility = (startPage + 3 <= _totalPages) ? Visibility.Visible : Visibility.Collapsed;

            PageButton5.Content = (startPage + 4).ToString();
            PageButton5.Tag = startPage + 4;
            PageButton5.Background = (startPage + 4 == _currentPage) ? (Brush)new BrushConverter().ConvertFrom("#5D6EFF") : (Brush)new BrushConverter().ConvertFrom("#293153");
            PageButton5.Visibility = (startPage + 4 <= _totalPages) ? Visibility.Visible : Visibility.Collapsed;
        }





        private void UpdateStockUI(List<StockInfo> stocks)
        {
            // 清空股票面板
            StockItemsPanel.Children.Clear();

            // 创建一个StackPanel作为主容器
            StackPanel mainContainer = new StackPanel
            {
                Orientation = Orientation.Vertical
            };
            StockItemsPanel.Children.Add(mainContainer);

            // 创建一个WrapPanel来包含所有股票卡片
            WrapPanel stockCardsPanel = new WrapPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center
            };
            mainContainer.Children.Add(stockCardsPanel);

            // 计算总盈亏
            decimal totalProfit = 0;
            foreach (var stock in stocks)
            {
                totalProfit += stock.DailyProfit;
                var stockCard = CreateStockCard(stock);
                stockCardsPanel.Children.Add(stockCard);
            }

            // 添加总盈亏摘要
            Border summaryCard = new Border
            {
                Style = (Style)FindResource("StockCardStyle"),
                Background = (Brush)new BrushConverter().ConvertFrom("#293153"),
                Margin = new Thickness(20, 30, 20, 15),
                Padding = new Thickness(20),
                BorderThickness = new Thickness(0),
                CornerRadius = new CornerRadius(10),
                HorizontalAlignment = HorizontalAlignment.Center,
                Width = 300
            };

            StackPanel summaryContent = new StackPanel
            {
                Orientation = Orientation.Vertical,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            // 添加总盈亏标题
            TextBlock summaryTitle = new TextBlock
            {
                Text = "今日总盈亏",
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White,
                Margin = new Thickness(0, 0, 0, 10),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            summaryContent.Children.Add(summaryTitle);

            // 添加总盈亏金额
            TextBlock summaryAmount = new TextBlock
            {
                Text = $"{(totalProfit > 0 ? "+" : "")}{totalProfit:F2}元",
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                Foreground = totalProfit > 0 ? Brushes.LightCoral : (totalProfit < 0 ? Brushes.LightGreen : Brushes.White),
                Margin = new Thickness(0, 0, 0, 5),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            summaryContent.Children.Add(summaryAmount);

            // 添加更新时间
            TextBlock updateTime = new TextBlock
            {
                Text = $"更新时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                FontSize = 12,
                Foreground = Brushes.LightGray,
                Margin = new Thickness(0, 10, 0, 0),
                HorizontalAlignment = HorizontalAlignment.Center
            };
            summaryContent.Children.Add(updateTime);

            summaryCard.Child = summaryContent;
            mainContainer.Children.Add(summaryCard);
        }

        private Border CreateStockCard(StockInfo stock)
        {
            // 创建股票卡片主容器
            Border outerCard = new Border
            {
                Width = 250,  // 加宽确保内容完整显示
                Height = 220,  // 增加高度确保内容显示完整
                Margin = new Thickness(10),
                CornerRadius = new CornerRadius(16),
                BorderThickness = new Thickness(0),
                Background = new SolidColorBrush(Colors.Transparent)
            };

            // 创建玻璃态效果的主卡片
            Border mainCard = new Border
            {
                Margin = new Thickness(3),
                CornerRadius = new CornerRadius(16),
                BorderThickness = new Thickness(1),
                Background = new SolidColorBrush(Colors.Transparent),
                Effect = new DropShadowEffect
                {
                    Color = Colors.Black,
                    Direction = 315,
                    ShadowDepth = 3,
                    BlurRadius = 10,
                    Opacity = 0.25
                }
            };

            // 创建背景渐变
            LinearGradientBrush backgroundGradient = new LinearGradientBrush();
            backgroundGradient.StartPoint = new Point(0, 0);
            backgroundGradient.EndPoint = new Point(1, 1);

            // 根据涨跌设置渐变背景色
            if (stock.ChangePercent > 0)
            {
                // 上涨股票 - 红色渐变 (更柔和的配色)
                backgroundGradient.GradientStops.Add(new GradientStop(Color.FromRgb(255, 120, 120), 0.0));
                backgroundGradient.GradientStops.Add(new GradientStop(Color.FromRgb(235, 80, 80), 1.0));
            }
            else if (stock.ChangePercent < 0)
            {
                // 下跌股票 - 绿色渐变 (更柔和的配色)
                backgroundGradient.GradientStops.Add(new GradientStop(Color.FromRgb(100, 205, 120), 0.0));
                backgroundGradient.GradientStops.Add(new GradientStop(Color.FromRgb(70, 170, 90), 1.0));
            }
            else
            {
                // 平盘股票 - 蓝灰色渐变 (更柔和的配色)
                backgroundGradient.GradientStops.Add(new GradientStop(Color.FromRgb(110, 140, 160), 0.0));
                backgroundGradient.GradientStops.Add(new GradientStop(Color.FromRgb(80, 110, 135), 1.0));
            }

            // 设置背景渐变
            mainCard.Background = backgroundGradient;

            // 创建内容容器
            Grid contentGrid = new Grid();

            // 定义行，第一行为标题区域，第二行为内容区域
            contentGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(45) });  // 标题高度
            contentGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });

            // 创建标题区域 - 半透明玻璃效果
            Border titleBorder = new Border
            {
                CornerRadius = new CornerRadius(16, 16, 0, 0),
                Padding = new Thickness(10, 5, 10, 5),
                Background = new SolidColorBrush(Color.FromArgb(60, 255, 255, 255))
            };

            // 添加标题文本
            TextBlock titleText = new TextBlock
            {
                Text = $"{stock.Name} ({stock.Code})",
                FontSize = 16,
                FontWeight = FontWeights.SemiBold,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                TextAlignment = TextAlignment.Center
            };

            // 添加文本阴影效果，增强可读性
            titleText.Effect = new DropShadowEffect
            {
                ShadowDepth = 1,
                Direction = 320,
                Color = Colors.Black,
                Opacity = 0.6,
                BlurRadius = 2
            };

            titleBorder.Child = titleText;
            Grid.SetRow(titleBorder, 0);
            contentGrid.Children.Add(titleBorder);

            // 创建内容区域 - 主要信息
            StackPanel infoPanel = new StackPanel
            {
                Margin = new Thickness(15, 8, 15, 10),
                HorizontalAlignment = HorizontalAlignment.Stretch, // 使用Stretch确保内容填充可用空间
                Width = 220 // 设置固定宽度，确保文本有足够显示空间
            };
            Grid.SetRow(infoPanel, 1);

            // 添加价格
            TextBlock priceText = new TextBlock
            {
                Text = $"{stock.CurrentPrice:F2}",
                FontSize = 28,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 5),
                Effect = new DropShadowEffect
                {
                    ShadowDepth = 1,
                    Direction = 320,
                    Color = Colors.Black,
                    Opacity = 0.5,
                    BlurRadius = 2
                }
            };
            infoPanel.Children.Add(priceText);

            // 添加涨跌幅 - 使用特殊样式
            Border changeContainer = new Border
            {
                Background = new SolidColorBrush(Color.FromArgb(50, 255, 255, 255)),
                CornerRadius = new CornerRadius(15),
                Padding = new Thickness(10, 4, 10, 4),
                Margin = new Thickness(0, 0, 0, 8),
                HorizontalAlignment = HorizontalAlignment.Center
            };

            TextBlock changeText = new TextBlock
            {
                Text = $"{(stock.ChangePercent > 0 ? "+" : "")}{stock.ChangePercent:F2}%",
                FontSize = 16,
                FontWeight = FontWeights.SemiBold,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            changeContainer.Child = changeText;
            infoPanel.Children.Add(changeContainer);

            // 添加分隔线
            Border separator = new Border
            {
                Height = 1,
                Margin = new Thickness(10, 2, 10, 8),
                Background = new SolidColorBrush(Color.FromArgb(40, 255, 255, 255))
            };
            infoPanel.Children.Add(separator);

            // 创建详细信息表格
            Grid detailsGrid = new Grid();

            // 添加列定义
            detailsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto }); // 标签列
            detailsGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) }); // 值列

            // 添加行定义
            detailsGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // 最高价
            detailsGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // 最低价
            detailsGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto }); // 当日盈亏

            // 1. 最高价行
            TextBlock highPriceLabel = new TextBlock
            {
                Text = "最高价:",
                FontSize = 13,
                Foreground = new SolidColorBrush(Color.FromArgb(220, 255, 255, 255)),
                Margin = new Thickness(0, 0, 5, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            TextBlock highPriceValue = new TextBlock
            {
                Text = $"{stock.HighPrice:F2}",
                FontSize = 13,
                Foreground = Brushes.White,
                FontWeight = FontWeights.SemiBold,
                VerticalAlignment = VerticalAlignment.Center
            };

            Grid.SetRow(highPriceLabel, 0);
            Grid.SetColumn(highPriceLabel, 0);
            Grid.SetRow(highPriceValue, 0);
            Grid.SetColumn(highPriceValue, 1);
            detailsGrid.Children.Add(highPriceLabel);
            detailsGrid.Children.Add(highPriceValue);

            // 2. 最低价行
            TextBlock lowPriceLabel = new TextBlock
            {
                Text = "最低价:",
                FontSize = 13,
                Foreground = new SolidColorBrush(Color.FromArgb(220, 255, 255, 255)),
                Margin = new Thickness(0, 4, 5, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            TextBlock lowPriceValue = new TextBlock
            {
                Text = $"{stock.LowPrice:F2}",
                FontSize = 13,
                Foreground = Brushes.White,
                FontWeight = FontWeights.SemiBold,
                VerticalAlignment = VerticalAlignment.Center
            };

            Grid.SetRow(lowPriceLabel, 1);
            Grid.SetColumn(lowPriceLabel, 0);
            Grid.SetRow(lowPriceValue, 1);
            Grid.SetColumn(lowPriceValue, 1);
            detailsGrid.Children.Add(lowPriceLabel);
            detailsGrid.Children.Add(lowPriceValue);

            // 3. 当日盈亏行
            TextBlock profitLabel = new TextBlock
            {
                Text = "当日盈亏:",
                FontSize = 13,
                Foreground = new SolidColorBrush(Color.FromArgb(220, 255, 255, 255)),
                Margin = new Thickness(0, 4, 5, 0),
                VerticalAlignment = VerticalAlignment.Center
            };

            // 为当日盈亏值使用滚动查看器确保完整显示
            ScrollViewer profitScrollViewer = new ScrollViewer
            {
                HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
                VerticalScrollBarVisibility = ScrollBarVisibility.Disabled,
                Padding = new Thickness(0),
                Height = 20, // 固定高度
                Margin = new Thickness(0)
            };

            TextBlock profitValue = new TextBlock
            {
                Text = $"{(stock.DailyProfit > 0 ? "+" : "")}{stock.DailyProfit:F2}元",
                FontSize = 13,
                FontWeight = FontWeights.SemiBold,
                Foreground = stock.DailyProfit > 0 ? new SolidColorBrush(Color.FromRgb(255, 220, 220)) :
                    (stock.DailyProfit < 0 ? new SolidColorBrush(Color.FromRgb(200, 255, 200)) : Brushes.White),
                VerticalAlignment = VerticalAlignment.Center,
                TextWrapping = TextWrapping.NoWrap
            };

            profitScrollViewer.Content = profitValue;

            Grid.SetRow(profitLabel, 2);
            Grid.SetColumn(profitLabel, 0);
            Grid.SetRow(profitScrollViewer, 2);
            Grid.SetColumn(profitScrollViewer, 1);
            detailsGrid.Children.Add(profitLabel);
            detailsGrid.Children.Add(profitScrollViewer);

            // 将详细信息表格添加到面板
            infoPanel.Children.Add(detailsGrid);

            // 添加内容面板到网格
            contentGrid.Children.Add(infoPanel);

            // 创建顶部光泽效果
            Border glossEffect = new Border
            {
                Height = 100,
                VerticalAlignment = VerticalAlignment.Top,
                CornerRadius = new CornerRadius(16, 16, 0, 0),
                Opacity = 0.15,
                Background = new LinearGradientBrush(
                    Colors.White,
                    Colors.Transparent,
                    new Point(0, 0),
                    new Point(0, 1))
            };

            // 组装卡片内部结构
            Grid cardInnerContainer = new Grid();
            cardInnerContainer.Children.Add(contentGrid);
            cardInnerContainer.Children.Add(glossEffect);

            mainCard.Child = cardInnerContainer;
            outerCard.Child = mainCard;

            return outerCard;
        }

        // 修改 SettingsButton_Click 方法，确保它被标记为 async
        private async void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            SwitchView(StockContent);
            if (StockItemsPanel.Children.Count == 0)
            {
                await LoadStockData();
            }

            // 加载用户自选股列表
            await LoadUserStocksData();
        }

        // 加载用户自选股列表
        private async Task LoadUserStocksData()
        {
            try
            {
                // 初始化自选股集合
                if (_userStocks == null)
                {
                    _userStocks = new ObservableCollection<CustomStock>();
                }
                else
                {
                    _userStocks.Clear();
                }

                // 获取用户自选股列表
                var userStocks = await _stockService.GetUserStocksAsync();

                // 如果没有自选股，尝试初始化默认自选股
                if (userStocks.Count == 0)
                {
                    await _stockService.InitializeDefaultStocksAsync();
                    userStocks = await _stockService.GetUserStocksAsync();
                }

                // 更新UI
                foreach (var stock in userStocks)
                {
                    _userStocks.Add(stock);
                }

                // 绑定到ListView
                CustomStockListView.ItemsSource = _userStocks;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"加载用户自选股失败: {ex.Message}");
            }
        }

        // 添加股票文本框获取焦点
        private void StockNameTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            StockNamePlaceholder.Visibility = Visibility.Collapsed;
        }

        // 添加股票文本框失去焦点
        private void StockNameTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(StockNameTextBox.Text))
            {
                StockNamePlaceholder.Visibility = Visibility.Visible;
            }
        }

        // 持有数量文本框获取焦点
        private void StockSharesTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            StockSharesPlaceholder.Visibility = Visibility.Collapsed;
        }

        // 持有数量文本框失去焦点
        private void StockSharesTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(StockSharesTextBox.Text))
            {
                StockSharesPlaceholder.Visibility = Visibility.Visible;
            }
        }

        // 添加自选股
        private async void AddStockButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 获取输入内容
                string stockName = StockNameTextBox.Text.Trim();
                string sharesText = StockSharesTextBox.Text.Trim();

                // 验证输入
                if (string.IsNullOrWhiteSpace(stockName))
                {
                    MessageBox.Show("请输入股票名称", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                if (string.IsNullOrWhiteSpace(sharesText) || !int.TryParse(sharesText, out int shares) || shares <= 0)
                {
                    MessageBox.Show("请输入有效的持有数量", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 显示加载提示
            StockLoadingPanel.Visibility = Visibility.Visible;
                StockLoadingText.Text = "正在添加自选股...";

                // 添加自选股
                bool success = await _stockService.AddUserStockAsync(stockName, shares);

                // 隐藏加载提示
                StockLoadingPanel.Visibility = Visibility.Collapsed;

                if (success)
                {
                    // 清空输入框
                    StockNameTextBox.Text = string.Empty;
                    StockSharesTextBox.Text = string.Empty;
                    StockNamePlaceholder.Visibility = Visibility.Visible;
                    StockSharesPlaceholder.Visibility = Visibility.Visible;

                    // 重新加载数据
                    await LoadUserStocksData();
                    await LoadStockData();
                }
                else
                {
                    MessageBox.Show("添加自选股失败，请重试", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                StockLoadingPanel.Visibility = Visibility.Collapsed;
                MessageBox.Show($"添加自选股失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 编辑自选股
        private async void EditStockButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Button button = sender as Button;
                string code = button.Tag.ToString();

                // 查找要编辑的自选股
                CustomStock stockToEdit = _userStocks.FirstOrDefault(s => s.Code == code);
                if (stockToEdit == null)
                {
                    return;
                }

                // 弹出编辑对话框
                string input = Microsoft.VisualBasic.Interaction.InputBox(
                    $"请输入 {stockToEdit.Name}({stockToEdit.Code}) 的持有数量:",
                    "编辑自选股",
                    stockToEdit.Shares.ToString(),
                    -1, -1);

                // 验证输入
                if (string.IsNullOrWhiteSpace(input))
                {
                    return; // 用户取消
                }

                if (!int.TryParse(input, out int shares) || shares <= 0)
                {
                    MessageBox.Show("请输入有效的持有数量", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 显示加载提示
                StockLoadingPanel.Visibility = Visibility.Visible;
                StockLoadingText.Text = "正在更新自选股...";

                // 更新自选股
                bool success = await _stockService.UpdateUserStockAsync(code, shares);

                // 隐藏加载提示
                StockLoadingPanel.Visibility = Visibility.Collapsed;

                if (success)
                {
                    // 重新加载数据
                    await LoadUserStocksData();
                    await LoadStockData();
                }
                else
                {
                    MessageBox.Show("更新自选股失败", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                StockLoadingPanel.Visibility = Visibility.Collapsed;
                MessageBox.Show($"编辑自选股失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 删除自选股
        private async void RemoveStockButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Button button = sender as Button;
                string code = button.Tag.ToString();

                // 查找要删除的自选股
                CustomStock stockToRemove = _userStocks.FirstOrDefault(s => s.Code == code);
                if (stockToRemove == null)
                {
                    return;
                }

                // 确认是否删除
                MessageBoxResult result = MessageBox.Show(
                    $"确定要删除自选股 {stockToRemove.Name}({stockToRemove.Code}) 吗?",
                    "删除确认",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result != MessageBoxResult.Yes)
                {
                    return;
                }

                // 显示加载提示
                StockLoadingPanel.Visibility = Visibility.Visible;
                StockLoadingText.Text = "正在删除自选股...";

                // 删除自选股
                bool success = await _stockService.RemoveUserStockAsync(code);

                // 隐藏加载提示
                StockLoadingPanel.Visibility = Visibility.Collapsed;

                if (success)
                {
                    // 重新加载数据
                    await LoadUserStocksData();
                    await LoadStockData();
                }
                else
                {
                    MessageBox.Show("删除自选股失败", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                StockLoadingPanel.Visibility = Visibility.Collapsed;
                MessageBox.Show($"删除自选股失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 刷新股票数据
        private async void RefreshStocks_Click(object sender, RoutedEventArgs e)
        {
            await LoadStockData();
        }

        // 加载股票数据
        private async Task LoadStockData()
        {
            StockLoadingPanel.Visibility = Visibility.Visible;
            StockItemsPanel.Children.Clear();

            try
            {
                var stocks = await _stockService.GetStocksAsync();
                UpdateStockUI(stocks);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载股票数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }

                StockLoadingPanel.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// 处理Logout按钮点击事件
        /// </summary>
        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            // 创建淡出动画
            DoubleAnimation fadeOutAnimation = new DoubleAnimation
            {
                From = 1.0,
                To = 0.0,
                Duration = new Duration(TimeSpan.FromSeconds(0.5))
            };

            // 设置动画完成事件
            fadeOutAnimation.Completed += (s, args) =>
            {
                // 关闭应用程序
                Application.Current.Shutdown();
            };

            // 应用动画到主窗口
            this.BeginAnimation(UIElement.OpacityProperty, fadeOutAnimation);
        }

        private void Border_Mousedown(object sender, MouseButtonEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed)
            {
                DragMove();
            }
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        private void MaximizeButton_Click(object sender, RoutedEventArgs e)
        {
            if (WindowState == WindowState.Normal)
            {
                WindowState = WindowState.Maximized;
            }
            else
            {
                WindowState = WindowState.Normal;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        // 首先在窗口类中添加排序相关的字段
        private bool _isTimeAscending = true; // 用于跟踪排序方向
                                              // 为时间按钮添加点击事件处理程序
                                              // 在窗口类中添加所有排序相关的字段

        private bool _isTypeAscending = true;
        private bool _isPriceAscending = true;
        private bool _isWeightAscending = true;
        private bool _isCountryAscending = true;
        private void TimeButton_Click(object sender, RoutedEventArgs e)
        {
            // 显示进度条
            CoffeeLoadingProgress.Visibility = Visibility.Visible;

            try
        {
            LoadCoffeeData();
            if (CoffeeDataGrid.ItemsSource != null)
            {
                // 获取数据源
                var items = CoffeeDataGrid.ItemsSource as IEnumerable<CoffeeItem>;
                if (items != null)
                {
                    // 根据当前排序方向进行排序
                    IEnumerable<CoffeeItem> sortedItems;

                    if (_isTimeAscending)
                    {
                        // 按时间升序排序
                        sortedItems = items.OrderBy(c => c.Date).ToList();
                    }
                    else
                    {
                        // 按时间降序排序
                        sortedItems = items.OrderByDescending(c => c.Date).ToList();
                    }

                    // 切换排序方向，用于下次点击
                    _isTimeAscending = !_isTimeAscending;

                    // 更新DataGrid的数据源
                    CoffeeDataGrid.ItemsSource = new ObservableCollection<CoffeeItem>(sortedItems);
                }
                }
            }
            finally
            {
                // 隐藏进度条
                CoffeeLoadingProgress.Visibility = Visibility.Collapsed;
            }
        }

        // 类型按钮点击事件处理方法
        private void TypeButton_Click(object sender, RoutedEventArgs e)
        {
            // 显示进度条
            CoffeeLoadingProgress.Visibility = Visibility.Visible;

            try
        {
            if (CoffeeDataGrid.ItemsSource != null)
            {
                var items = CoffeeDataGrid.ItemsSource as IEnumerable<CoffeeItem>;
                if (items != null)
                {
                    IEnumerable<CoffeeItem> sortedItems;

                    if (_isTypeAscending)
                    {
                        sortedItems = items.OrderBy(c => c.Status).ToList();
                    }
                    else
                    {
                        sortedItems = items.OrderByDescending(c => c.Status).ToList();
                    }

                    _isTypeAscending = !_isTypeAscending;
                    CoffeeDataGrid.ItemsSource = new ObservableCollection<CoffeeItem>(sortedItems);
                }
            }
        }
            finally
            {
                // 隐藏进度条
                CoffeeLoadingProgress.Visibility = Visibility.Collapsed;
            }
        }

        // 价格按钮点击事件处理方法
        private void PriceButton_Click(object sender, RoutedEventArgs e)
        {
            // 显示进度条
            CoffeeLoadingProgress.Visibility = Visibility.Visible;

            try
        {
            if (CoffeeDataGrid.ItemsSource != null)
            {
                var items = CoffeeDataGrid.ItemsSource as IEnumerable<CoffeeItem>;
                if (items != null)
                {
                    IEnumerable<CoffeeItem> sortedItems;

                    if (_isPriceAscending)
                    {
                        sortedItems = items.OrderBy(c => c.Price).ToList();
                    }
                    else
                    {
                        sortedItems = items.OrderByDescending(c => c.Price).ToList();
                    }

                    _isPriceAscending = !_isPriceAscending;
                    CoffeeDataGrid.ItemsSource = new ObservableCollection<CoffeeItem>(sortedItems);
                }
            }
        }
            finally
            {
                // 隐藏进度条
                CoffeeLoadingProgress.Visibility = Visibility.Collapsed;
            }
        }

        // 重量按钮点击事件处理方法
        private void WeightButton_Click(object sender, RoutedEventArgs e)
        {
            // 显示进度条
            CoffeeLoadingProgress.Visibility = Visibility.Visible;

            try
        {
            if (CoffeeDataGrid.ItemsSource != null)
            {
                var items = CoffeeDataGrid.ItemsSource as IEnumerable<CoffeeItem>;
                if (items != null)
                {
                    IEnumerable<CoffeeItem> sortedItems;

                    if (_isWeightAscending)
                    {
                        sortedItems = items.OrderBy(c => c.Weight).ToList();
                    }
                    else
                    {
                        sortedItems = items.OrderByDescending(c => c.Weight).ToList();
                    }

                    _isWeightAscending = !_isWeightAscending;
                    CoffeeDataGrid.ItemsSource = new ObservableCollection<CoffeeItem>(sortedItems);
                }
            }
        }
            finally
            {
                // 隐藏进度条
                CoffeeLoadingProgress.Visibility = Visibility.Collapsed;
            }
        }

        // 国家按钮点击事件处理方法
        private void CountryButton_Click(object sender, RoutedEventArgs e)
        {
            // 显示进度条
            CoffeeLoadingProgress.Visibility = Visibility.Visible;

            try
        {
            if (CoffeeDataGrid.ItemsSource != null)
            {
                var items = CoffeeDataGrid.ItemsSource as IEnumerable<CoffeeItem>;
                if (items != null)
                {
                    IEnumerable<CoffeeItem> sortedItems;

                    if (_isCountryAscending)
                    {
                        sortedItems = items.OrderBy(c => c.Country).ToList();
                    }
                    else
                    {
                        sortedItems = items.OrderByDescending(c => c.Country).ToList();
                    }

                    _isCountryAscending = !_isCountryAscending;
                    CoffeeDataGrid.ItemsSource = new ObservableCollection<CoffeeItem>(sortedItems);
                }
            }
        }
            finally
            {
                // 隐藏进度条
                CoffeeLoadingProgress.Visibility = Visibility.Collapsed;
            }
        }

        private void QueryButton_Click(object sender, RoutedEventArgs e)
        {
            // 创建并显示查询窗口
            var searchWindow = new SearchCoffeeWindow();
            bool? result = searchWindow.ShowDialog();

            // 如果用户点击了查询按钮
            if (result == true && searchWindow.IsSearchConfirmed)
            {
                // 显示进度条
                CoffeeLoadingProgress.Visibility = Visibility.Visible;

                try
            {
                string coffeeName = searchWindow.CoffeeName;

                // 根据咖啡名称进行查询
                if (!string.IsNullOrWhiteSpace(coffeeName))
                {
                    // 假设CoffeeDataGrid.ItemsSource是一个集合
                    var allCoffees = CoffeeDataGrid.ItemsSource as System.Collections.IEnumerable;
                    if (allCoffees != null)
                    {
                        // 创建一个新的集合来存储查询结果
                        var filteredCoffees = new System.Collections.Generic.List<object>();

                        // 遍历所有咖啡记录
                        foreach (var coffee in allCoffees)
                        {
                            // 使用反射获取Name属性值
                            var nameProperty = coffee.GetType().GetProperty("Name");
                            if (nameProperty != null)
                            {
                                string name = nameProperty.GetValue(coffee) as string;

                                // 如果名称包含查询条件，则添加到结果集合中
                                if (name != null && name.Contains(coffeeName, StringComparison.OrdinalIgnoreCase))
                                {
                                    filteredCoffees.Add(coffee);
                                }
                            }
                        }

                        // 更新DataGrid的数据源
                        CoffeeDataGrid.ItemsSource = filteredCoffees;
                    }
                }
                else
                {
                    // 如果查询条件为空，则显示所有记录
                    LoadCoffeeData(); // 假设这是加载所有咖啡数据的方法
                    }
                }
                finally
                {
                    // 隐藏进度条
                    CoffeeLoadingProgress.Visibility = Visibility.Collapsed;
                }
            }
        }

        private void ViewMemoButton_Click(object sender, RoutedEventArgs e)
        {
            // 获取按钮关联的CoffeeItem对象
            Button button = sender as Button;
            if (button != null)
            {
                CoffeeItem coffeeItem = button.Tag as CoffeeItem;
                if (coffeeItem != null)
                {
                    // 创建并显示备注窗口，传递整个coffeeItem对象而不仅仅是memo
                    MemoWindow memoWindow = new MemoWindow(coffeeItem);
                    memoWindow.Owner = this; // 设置主窗口为所有者
                    memoWindow.ShowDialog(); // 显示为模态对话框
                }
            }
        }




        // 数据添加按钮点击事件处理
        private void OrderAddButton_Click(object sender, RoutedEventArgs e)
        {
            ShowAddCoffeeWindow();
        }
        // 显示添加咖啡窗口的方法
        private void ShowAddCoffeeWindow()
        {
            AddCoffeeWindow addWindow = new AddCoffeeWindow(_supabaseClient);
            addWindow.Owner = this; // 设置主窗口为父窗口
            //addWindow.CoffeeAdded += AddWindow_CoffeeAdded; // 订阅咖啡添加事件

            bool? result = addWindow.ShowDialog();
            if (result == true)
            {
                // 窗口确认关闭，可以在这里刷新UI显示
                // 如果CoffeeAdded事件已经处理了数据更新，此处可以不需要额外操作
            }
        }

        private void EditCoffee_Click(object sender, RoutedEventArgs e)
        {
            // 获取按钮关联的CoffeeItem对象
            Button button = sender as Button;
            if (button != null)
            {
                Console.WriteLine($"按钮Tag类型: {button.Tag?.GetType()?.FullName ?? "null"}");

                CoffeeItem coffeeItem = button.Tag as CoffeeItem;
                if (coffeeItem != null)
                {
                    // 调试输出完整的CoffeeItem信息
                    Console.WriteLine($"准备编辑咖啡: ID={coffeeItem.Id}, Name={coffeeItem.Name}, Status={coffeeItem.Status}");

                    // 创建并显示编辑窗口，传递整个coffeeItem对象
                    EditCoffeeWindow editWindow = new EditCoffeeWindow(_supabaseClient, coffeeItem);
                    editWindow.Owner = this; // 设置主窗口为所有者
                    bool? result = editWindow.ShowDialog(); // 显示为模态对话框

                    // 如果用户确认了编辑，刷新数据
                    if (result == true)
                    {
                        // 刷新咖啡数据
                        LoadCoffeeData();
                    }
                }
                else
                {
                    // 如果无法获取CoffeeItem，尝试从当前选中的行获取
                    if (CoffeeDataGrid.SelectedItem is CoffeeItem selectedItem)
                    {
                        Console.WriteLine($"从选中行获取咖啡: ID={selectedItem.Id}, Name={selectedItem.Name}, Status={selectedItem.Status}");

                        // 创建并显示编辑窗口，传递选中的CoffeeItem对象
                        EditCoffeeWindow editWindow = new EditCoffeeWindow(_supabaseClient, selectedItem);
                        editWindow.Owner = this; // 设置主窗口为所有者
                        bool? result = editWindow.ShowDialog(); // 显示为模态对话框

                        // 如果用户确认了编辑，刷新数据
                        if (result == true)
                        {
                            // 刷新咖啡数据
                            LoadCoffeeData();
                        }
                    }
                    else
                    {
                        MessageBox.Show("无法获取咖啡数据。请重试或刷新列表。", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        // 添加查看详情按钮的点击事件处理程序
        private void ViewMovieDetail_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            if (button != null && button.Tag != null)
            {
                var movie = button.Tag;
                var detailWindow = new MovieDetailWindow(movie);
                detailWindow.ShowDialog();
            }
        }

        // 添加编辑按钮的点击事件处理程序
        private async void EditMovie_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                if (button != null && button.Tag != null)
                {
                    // 获取电影对象
                    var movie = button.Tag as MovieItem;
                    if (movie == null)
                    {
                        MessageBox.Show("无法获取电影信息", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    // 创建并显示编辑窗口
                    var editWindow = new MovieEditWindow(movie, _supabaseClient);
                    editWindow.Owner = this;
                    bool? result = editWindow.ShowDialog();

                    // 如果用户确认了编辑，刷新数据
                    if (result == true)
                    {
                        // 刷新电影数据
                        await LoadPageData(_currentPage);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"编辑电影信息失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // 搜索按钮点击事件
        private async void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            string searchText = MovieSearchTextBox.Text?.Trim();
            await SearchMovies(searchText);
        }

        // 搜索框按下回车键事件
        private async void MovieSearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                string searchText = MovieSearchTextBox.Text?.Trim();
                await SearchMovies(searchText);
            }
        }

        // 电影搜索方法
        private async Task SearchMovies(string searchText)
        {
            // 显示加载进度条
            MovieLoadingProgressGrid.Visibility = Visibility.Visible;
            MovieItemsControl.Visibility = Visibility.Collapsed;

            if (string.IsNullOrWhiteSpace(searchText))
            {
                // 如果搜索文本为空，显示所有电影
                await LoadPageData(_currentPage);
                return;
            }

            try
            {
                // 从数据库搜索电影
                var query = _supabaseClient
                    .From<MovieItem>()
                    .Select(x => new object[] { x.PosterUrl, x.Title, x.Director, x.Country, x.Intro, x.Rating, x.Douban, x.Sublink, x.Magnet, x.ReleaseDate, x.Awards })
                    .Filter("mname", Operator.ILike, $"%{searchText}%"); // 使用ILike进行不区分大小写的模糊搜索

                // 添加排序
                if (_currentSortField == "rating")
                {
                    query = query.Order(x => x.Rating, _currentSortOrder);
                }
                else if (_currentSortField == "mdate")
                {
                    query = query.Order(x => x.ReleaseDate, _currentSortOrder);
                }
                else if (_currentSortField == "id")
                {
                    query = query.Order(x => x.Id, _currentSortOrder);
                }
                else
                {
                    // 默认按ID排序
                    query = query.Order(x => x.Id, Ordering.Descending);
                }

                var response = await query.Get();

                // 更新UI显示
                var searchResults = response.Models;
                MovieItemsControl.ItemsSource = searchResults;

                // 更新搜索结果计数
                TotalMoviesText.Text = searchResults.Count.ToString();

                // 隐藏进度条，显示内容
                MovieLoadingProgressGrid.Visibility = Visibility.Collapsed;
                MovieItemsControl.Visibility = Visibility.Visible;

                // 显示搜索结果提示
                //MessageBox.Show($"找到 {searchResults.Count} 条与 \"{searchText}\" 相关的电影记录", "搜索结果", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                // 发生错误时也隐藏进度条
                MovieLoadingProgressGrid.Visibility = Visibility.Collapsed;
                MovieItemsControl.Visibility = Visibility.Visible;

                MessageBox.Show($"搜索电影失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        public SeriesCollection SeriesCollection { get; set; }
        public string[] Labels { get; set; }

        private void NotesButton_Click(object sender, RoutedEventArgs e)
        {
            // 创建并显示随手记窗口
            NotesWindow notesWindow = new NotesWindow(_supabaseClient);
            notesWindow.Owner = this;
            notesWindow.ShowDialog();
        }

        private void WeatherButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 创建并显示天气窗口
                WeatherWindow weatherWindow = new WeatherWindow();
                weatherWindow.Owner = this;
                weatherWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开天气窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 发送消息按钮点击事件
        /// </summary>
        private async void SendMessageButton_Click(object sender, RoutedEventArgs e)
        {
            // 聊天功能已移除，工具集页面不需要此功能
            await Task.CompletedTask;
            /*
            string message = MessageInputBox.Text.Trim();
            if (string.IsNullOrEmpty(message))
            {
                return;
            }

            // 添加用户消息到聊天列表
            _chatMessages.Add(new ChatMessage
            {
                Sender = "我",
                Content = message,
                Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                IsUser = true
            });

            // 清空输入框并显示提示文本
            MessageInputBox.Clear();
            MessageInputPlaceholder.Visibility = Visibility.Visible;

            // 滚动到底部
            ChatScrollViewer.ScrollToEnd();
            */

            /*
            // 添加AI正在输入消息
            int typingMessageIndex = _chatMessages.Count; // 记录正在思考的消息索引
            _chatMessages.Add(new ChatMessage
            {
                Sender = "DeepSeek R1",
                Content = "正在思考...",
                Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                IsUser = false
            });

            try
            {
                // 调用DeepSeek API并获取流式响应
                await GetDeepSeekResponseAsync(message, typingMessageIndex);
            }
            catch (Exception ex)
            {
                // 更新为错误消息
                _chatMessages[typingMessageIndex] = new ChatMessage
                {
                    Sender = "DeepSeek R1",
                    Content = $"抱歉，发生错误: {ex.Message}",
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    IsUser = false
                };

                MessageBox.Show($"调用AI API失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }

            // 再次滚动到底部
            ChatScrollViewer.ScrollToEnd();
            */
        }

        /// <summary>
        /// 调用DeepSeek API
        /// </summary>
        private async Task GetDeepSeekResponseAsync(string userMessage, int typingMessageIndex)
        {
            // 收集历史消息（最多保留最近5轮对话）
            var conversationHistory = new List<object>();

            // 添加系统消息
            conversationHistory.Add(new { role = "system", content = "你是DeepSeek R1, 一个由DeepSeek研发的强大大语言模型。你很有礼貌并且乐于帮助用户解答各种问题。" });

            // 添加最近5轮对话历史
            int startIndex = Math.Max(0, _chatMessages.Count - 10);
            for (int i = startIndex; i < _chatMessages.Count - 1; i++)
            {
                var message = _chatMessages[i];
                string role = message.IsUser ? "user" : "assistant";
                conversationHistory.Add(new { role, content = message.Content });
            }

            // 添加当前用户消息
            conversationHistory.Add(new { role = "user", content = userMessage });

            // 构建请求体
            var requestData = new
            {
                model = "deepseek-chat",

                messages = conversationHistory.ToArray(),
                temperature = 0.7,
                max_tokens = 800,
                stream = true  // 启用流式输出
            };

            // 序列化为JSON
            string jsonContent = JsonSerializer.Serialize(requestData);

            // 发送请求
            var requestContent = new StringContent(jsonContent, Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(DeepSeekApiUrl, requestContent);

            // 检查响应
            response.EnsureSuccessStatusCode();

            // 获取响应流
            using var stream = await response.Content.ReadAsStreamAsync();
            using var reader = new StreamReader(stream);

            var fullResponse = new StringBuilder();

            // 逐行读取流式响应
            while (!reader.EndOfStream)
            {
                var line = await reader.ReadLineAsync();
                if (string.IsNullOrEmpty(line) || line == "data: [DONE]") continue;

                if (line.StartsWith("data:"))
                {
                    try
                    {
                        var json = line.Substring(5); // 去掉 "data:" 前缀
                        using var doc = JsonDocument.Parse(json);
                        var choicesElement = doc.RootElement.GetProperty("choices")[0];
                        var delta = choicesElement.GetProperty("delta");

                        if (delta.TryGetProperty("content", out var contentElement))
                        {
                            var messageContent = contentElement.GetString();
                            if (!string.IsNullOrEmpty(messageContent))
                            {
                                fullResponse.Append(messageContent);

                                // 更新UI上的消息
                                await Application.Current.Dispatcher.InvokeAsync(() =>
                                {
                                    _chatMessages[typingMessageIndex] = new ChatMessage
                                    {
                                        Sender = "DeepSeek R1",
                                        Content = fullResponse.ToString(),
                                        Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                        IsUser = false
                                    };

                                    // 渲染Markdown内容
                                    RenderMarkdown(fullResponse.ToString());

                                    // 滚动到底部
                                    // ChatScrollViewer.ScrollToEnd(); // 已移除
                                });

                                // 添加一个小延迟，使打字效果更自然
                                await Task.Delay(10);
                            }
                        }
                    }
                    catch (JsonException) { continue; }
                }
            }
        }

        private void RenderMarkdown(string markdown)
        {
            // 将Markdown转换为HTML
            var html = Markdig.Markdown.ToHtml(markdown);

            // 在WebBrowser控件中显示HTML
            //MarkdownPreview.NavigateToString(html);
        }

        /// <summary>
        /// 处理输入框按键事件
        /// </summary>
        private void MessageInputBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && !e.KeyboardDevice.Modifiers.HasFlag(ModifierKeys.Shift))
            {
                e.Handled = true;
                SendMessageButton_Click(sender, e);
            }
        }

        /// <summary>
        /// 处理数据分析按钮点击事件
        /// </summary>
        private void DataAnalysisButton_Click(object sender, RoutedEventArgs e)
        {
            // 切换到火山方舟AI对话视图
            SwitchView(VolcanoContent);

            // 确保滚动到底部
            VolcanoScrollViewer.ScrollToEnd();

        }

        /// <summary>
        /// 处理数据分析按钮点击事件
        /// </summary>
        private async void CoffeeAnalysisButton_Click(object sender, RoutedEventArgs e)
        {
            // 切换到咖啡分析视图
            SwitchView(CoffeeAnalysisContent);

            // 显示加载进度条
            AnalysisLoadingProgress.Visibility = Visibility.Visible;

            try
            {
                // 加载并分析咖啡数据
                await LoadCoffeeAnalysisData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载咖啡分析数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 隐藏加载进度条
                AnalysisLoadingProgress.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 加载咖啡分析数据并生成图表
        /// </summary>
        private async Task LoadCoffeeAnalysisData()
        {
            try
            {
                // 从数据库获取咖啡数据
                var response = await _supabaseClient
                    .From<CoffeeItem>()
                    .Select("*")
                    .Get();

                var coffeeItems = response.Models;

                if (coffeeItems.Count == 0)
                {
                    MessageBox.Show("没有可用的咖啡数据进行分析。", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 生成各种图表数据
                GenerateMonthlyExpenseChart(coffeeItems);
                GenerateCountryDistributionChart(coffeeItems);
                GenerateCoffeeTypeChart(coffeeItems);
                GenerateCoffeeStatusChart(coffeeItems);

                // 更新数据上下文以刷新图表
                DataContext = this;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载咖啡分析数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 生成月度消费统计图表
        /// </summary>
        private void GenerateMonthlyExpenseChart(List<CoffeeItem> coffeeItems)
        {
            // 按月份分组并计算总费用
            var monthlyExpenses = new Dictionary<string, double>();

            // 按年-月分组统计
            var groupedByMonth = coffeeItems
                .Where(c => DateTime.TryParse(c.Date, out _)) // 过滤有效日期
                .GroupBy(c =>
                {
                    DateTime.TryParse(c.Date, out var date);
                    return date.ToString("yyyy-MM");
                })
                .OrderBy(g => g.Key) // 按年月排序
                .ToList();

            // 提取最近12个月的数据
            var recentMonths = groupedByMonth.TakeLast(12).ToList();

            // 准备图表数据
            var monthlyValues = new ChartValues<double>();
            var labels = new List<string>();

            foreach (var group in recentMonths)
            {
                double totalExpense = group.Sum(c => c.Price);
                monthlyValues.Add(totalExpense);

                // 提取月份标签 (MM)
                DateTime.TryParse(group.First().Date, out var date);
                labels.Add(date.ToString("yyyy-MM"));
            }

            // 创建图表系列
            MonthlyExpenseSeries = new SeriesCollection
            {
                new ColumnSeries
                {
                    Title = "月度消费",
                    Values = monthlyValues,
                    Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#5D6EFF")),
                    ColumnPadding = 5
                }
            };

            // 设置X轴标签
            MonthLabels = labels.ToArray();

            // 设置Y轴格式化器
            YFormatter = value => value.ToString("C0"); // 货币格式

            // 更新图表属性
            MonthlyExpenseChart.AxisX.Clear();
            MonthlyExpenseChart.AxisX.Add(new Axis
            {
                Title = "月份",
                Labels = MonthLabels,
                Foreground = Brushes.White,
                Separator = new LiveCharts.Wpf.Separator { Stroke = Brushes.DarkGray, StrokeThickness = 1 }
            });

            MonthlyExpenseChart.AxisY.Clear();
            MonthlyExpenseChart.AxisY.Add(new Axis
            {
                Title = "消费金额 (元)",
                LabelFormatter = YFormatter,
                Foreground = Brushes.White,
                Separator = new LiveCharts.Wpf.Separator { Stroke = Brushes.DarkGray, StrokeThickness = 1 }
            });

            MonthlyExpenseChart.Series = MonthlyExpenseSeries;
        }

        /// <summary>
        /// 生成国家/产地分布饼图
        /// </summary>
        private void GenerateCountryDistributionChart(List<CoffeeItem> coffeeItems)
        {
            // 按国家分组并计算数量
            var countryDistribution = coffeeItems
                .GroupBy(c => string.IsNullOrEmpty(c.Country) ? "未知" : c.Country)
                .Select(g => new { Country = g.Key, Count = g.Count() })
                .OrderByDescending(x => x.Count)
                .ToList();

            // 准备饼图数据
            var pieValues = new SeriesCollection();

            foreach (var item in countryDistribution)
            {
                pieValues.Add(new PieSeries
                {
                    Title = item.Country,
                    Values = new ChartValues<double> { item.Count },
                    DataLabels = true,
                    LabelPoint = point => $"{item.Country}: {point.Y} ({point.Participation:P1})"
                });
            }

            // 更新图表
            CountryDistributionSeries = pieValues;
            CountryDistributionChart.Series = CountryDistributionSeries;
            CountryDistributionChart.LegendLocation = LegendLocation.Bottom;
        }

        /// <summary>
        /// 生成咖啡种类分布饼图
        /// </summary>
        private void GenerateCoffeeTypeChart(List<CoffeeItem> coffeeItems)
        {
            // 按咖啡类型分组
            var typeDistribution = coffeeItems
                .GroupBy(c => string.IsNullOrEmpty(c.Type) ? "未知" : c.Type)
                .Select(g => new { Type = g.Key, Count = g.Count() })
                .OrderByDescending(x => x.Count)
                .ToList();

            // 准备饼图数据
            var pieValues = new SeriesCollection();

            foreach (var item in typeDistribution)
            {
                pieValues.Add(new PieSeries
                {
                    Title = item.Type,
                    Values = new ChartValues<double> { item.Count },
                    DataLabels = true,
                    LabelPoint = point => $"{item.Type}: {point.Y} ({point.Participation:P1})"
                });
            }

            // 更新图表
            CoffeeTypeSeries = pieValues;
            CoffeeTypeChart.Series = CoffeeTypeSeries;
            CoffeeTypeChart.LegendLocation = LegendLocation.Bottom;
        }

        /// <summary>
        /// 生成咖啡状态分布图表
        /// </summary>
        private void GenerateCoffeeStatusChart(List<CoffeeItem> coffeeItems)
        {
            // 定义状态和对应的显示名称
            var statusMapping = new Dictionary<string, string>
            {
                { "road", "在路上" },
                { "new", "已到" },
                { "ing", "进行中" },
                { "over", "喝完" }
            };

            // 按状态分组
            var statusDistribution = coffeeItems
                .GroupBy(c => string.IsNullOrEmpty(c.Status) ? "未知" : c.Status)
                .Select(g => new
                {
                    Status = statusMapping.ContainsKey(g.Key) ? statusMapping[g.Key] : g.Key,
                    Count = g.Count()
                })
                .OrderBy(x => x.Status)
                .ToList();

            // 准备图表数据
            var statusValues = new ChartValues<double>();
            var statusLabels = new List<string>();

            foreach (var item in statusDistribution)
            {
                statusValues.Add(item.Count);
                statusLabels.Add(item.Status);
            }

            // 创建图表系列
            CoffeeStatusSeries = new SeriesCollection
            {
                new ColumnSeries
                {
                    Title = "数量",
                    Values = statusValues,
                    Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF4CD964")),
                    DataLabels = true
                }
            };

            // 更新图表
            CoffeeStatusChart.AxisX.Clear();
            CoffeeStatusChart.AxisX.Add(new Axis
            {
                Title = "状态",
                Labels = statusLabels.ToArray(),
                Foreground = Brushes.White,
                Separator = new LiveCharts.Wpf.Separator { Stroke = Brushes.DarkGray, StrokeThickness = 1 }
            });

            CoffeeStatusChart.AxisY.Clear();
            CoffeeStatusChart.AxisY.Add(new Axis
            {
                Title = "数量",
                Foreground = Brushes.White,
                Separator = new LiveCharts.Wpf.Separator { Stroke = Brushes.DarkGray, StrokeThickness = 1 }
            });

            CoffeeStatusChart.Series = CoffeeStatusSeries;
        }

        /// <summary>
        /// 发送消息到火山方舟API
        /// </summary>
        private async void SendVolcanoMessageButton_Click(object sender, RoutedEventArgs e)
        {
            string message = VolcanoMessageInputBox.Text.Trim();
            if (string.IsNullOrEmpty(message))
            {
                return;
            }

            // 添加用户消息到聊天列表
            _volcanoMessages.Add(new ChatMessage
            {
                Sender = "我的提问",
                Content = message,
                Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                IsUser = true
            });

            // 清空输入框并显示提示文本
            VolcanoMessageInputBox.Clear();
            VolcanoMessageInputPlaceholder.Visibility = Visibility.Visible;

            // 滚动到底部
            VolcanoScrollViewer.ScrollToEnd();

            // 显示进度条
            VolcanoProgressBar.Visibility = Visibility.Visible;

            // 添加AI正在输入消息
            int typingMessageIndex = _volcanoMessages.Count; // 记录正在思考的消息索引
            _volcanoMessages.Add(new ChatMessage
            {
                Sender = "我的火山智能体",
                Content = "正在思考...",
                Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                IsUser = false
            });

            try
            {
                // 调用火山方舟API并获取流式响应
                await GetVolcanoResponseAsync(message, typingMessageIndex);
            }
            catch (Exception ex)
            {
                // 更新为错误消息
                _volcanoMessages[typingMessageIndex] = new ChatMessage
                {
                    Sender = "我的火山智能体",
                    Content = $"抱歉，发生错误: {ex.Message}",
                    Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    IsUser = false
                };

                MessageBox.Show($"调用火山方舟API失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 隐藏进度条
                VolcanoProgressBar.Visibility = Visibility.Collapsed;
            }

            // 再次滚动到底部
            VolcanoScrollViewer.ScrollToEnd();
        }

        /// <summary>
        /// 调用火山方舟API
        /// </summary>
        private async Task GetVolcanoResponseAsync(string userMessage, int typingMessageIndex)
        {
            try
            {
                // 收集历史消息
                var messages = new List<object>();

                // 添加系统消息
                messages.Add(new { role = "system", content = "你是火山方舟AI助手，一个由火山引擎研发的强大大语言模型。你很有礼貌并且乐于帮助用户解答各种问题。在回答问题时，请先进行详细分析，然后给出清晰明确的结论。总是确保你的回答包含分析部分和结论部分。" });

                // 添加最近的对话历史
                int startIndex = Math.Max(0, _volcanoMessages.Count - 10);
                for (int i = startIndex; i < _volcanoMessages.Count - 1; i++)
                {
                    var message = _volcanoMessages[i];
                    string role = message.IsUser ? "user" : "assistant";
                    messages.Add(new { role, content = message.Content });
                }

                // 添加当前用户消息
                messages.Add(new { role = "user", content = userMessage });

                // 构建新的HTTP客户端，避免重用之前的客户端
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", VolcanoApiKey);
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                // 构建请求体 - 使用正确的格式
                var requestData = new
                {
                    model = VolcanoAppId,
                    messages = messages,
                    stream = true
                };

                // 序列化为JSON并输出调试信息
                string jsonContent = JsonSerializer.Serialize(requestData);
                Console.WriteLine($"请求内容: {jsonContent}");

                // 发送请求
                var requestContent = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                // 打印完整URL以便调试
                Console.WriteLine($"请求URL: {VolcanoApiUrl}");

                // 发送请求
                var response = await client.PostAsync(VolcanoApiUrl, requestContent);

                // 检查响应
                Console.WriteLine($"响应状态码: {(int)response.StatusCode} {response.StatusCode}");
                foreach (var header in response.Headers)
                {
                    Console.WriteLine($"响应头: {header.Key}: {string.Join(", ", header.Value)}");
                }

                if (!response.IsSuccessStatusCode)
                {
                    string errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"API返回错误: {errorContent}");
                    throw new Exception($"API调用失败，状态码：{response.StatusCode}，错误信息：{errorContent}");
                }

                // 获取响应流
                using var stream = await response.Content.ReadAsStreamAsync();
                using var reader = new StreamReader(stream);

                var fullResponse = new StringBuilder();
                bool receivedAnyContent = false;
                var references = new List<string>();

                // 逐行读取流式响应
                while (!reader.EndOfStream)
                {
                    var line = await reader.ReadLineAsync();
                    Console.WriteLine($"收到的行：{line}");

                    if (string.IsNullOrEmpty(line)) continue;
                    if (line == "data: [DONE]")
                    {
                        Console.WriteLine("收到流结束标记");
                        continue;
                    }

                    if (line.StartsWith("data:"))
                    {
                        receivedAnyContent = true;
                        try
                        {
                            var json = line.Substring(5); // 去掉 "data:" 前缀
                            Console.WriteLine($"处理JSON: {json}");

                            // 如果JSON内容为空或只有空白字符，可能是一个结束标记
                            if (string.IsNullOrWhiteSpace(json))
                            {
                                Console.WriteLine("收到空的data行，可能是结束标记");
                                continue;
                            }

                            // 尝试捕获JSON解析错误
                            JsonDocument doc;
                            try
                            {
                                doc = JsonDocument.Parse(json);
                            }
                            catch (JsonException ex)
                            {
                                Console.WriteLine($"JSON解析错误: {ex.Message}, JSON内容: {json}");

                                // 检查是否包含原始文本格式的响应
                                if (json.StartsWith("\"") && json.EndsWith("\""))
                                {
                                    // 可能是直接返回的文本内容，移除引号
                                    string directText = json.Trim('"');
                                    Console.WriteLine($"检测到直接文本响应: {directText}");

                                    // 直接添加到响应中
                                    fullResponse.Append(directText);

                                    // 更新UI上的消息
                                    await Application.Current.Dispatcher.InvokeAsync(() =>
                                    {
                                        _volcanoMessages[typingMessageIndex] = new ChatMessage
                                        {
                                            Sender = "火山方舟",
                                            Content = fullResponse.ToString(),
                                            Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                            IsUser = false
                                        };

                                        // 滚动到底部
                                        VolcanoScrollViewer.ScrollToEnd();
                                    });
                                }

                                continue;
                            }

                            using (doc)
                            {
                                string messageContent = null;

                                // 检查是否有references数据
                                if (doc.RootElement.TryGetProperty("references", out var refsArray) &&
                                    refsArray.GetArrayLength() > 0)
                                {
                                    // 从references中提取summary信息
                                    for (int i = 0; i < refsArray.GetArrayLength(); i++)
                                    {
                                        if (refsArray[i].TryGetProperty("summary", out var summary))
                                        {
                                            string summaryText = summary.GetString();
                                            if (!string.IsNullOrEmpty(summaryText) && !references.Contains(summaryText))
                                            {
                                                references.Add(summaryText);
                                                Console.WriteLine($"添加reference: {summaryText.Substring(0, Math.Min(50, summaryText.Length))}...");
                                            }
                                        }
                                    }
                                }

                                // 尝试提取reasoning_content或content
                                if (doc.RootElement.TryGetProperty("choices", out var choices) &&
                                    choices.GetArrayLength() > 0)
                                {
                                    var choice = choices[0];

                                    if (choice.TryGetProperty("delta", out var delta))
                                    {
                                        // 检查conclusion_content字段（结论内容）
                                        if (delta.TryGetProperty("conclusion_content", out var conclusionContent))
                                        {
                                            string content = conclusionContent.GetString();
                                            // 确保不是空字符串，null，或空引号
                                            if (!string.IsNullOrWhiteSpace(content) && content != "\"\"" && content != "好的" && content != "好的。" && content != "好的，" && content != "好")
                                            {
                                                messageContent = content;
                                                Console.WriteLine($"找到有效的conclusion_content: {messageContent}");
                                            }
                                            else
                                            {
                                                Console.WriteLine($"忽略无效的conclusion_content: '{content}'");
                                            }
                                        }
                                        // 检查reasoning_content字段
                                        else if (delta.TryGetProperty("reasoning_content", out var reasoningContent))
                                        {
                                            string content = reasoningContent.GetString();
                                            // 确保不是空字符串，null，或空引号
                                            if (!string.IsNullOrWhiteSpace(content) && content != "\"\"" && content != "好的" && content != "好的。" && content != "好的，" && content != "好")
                                            {
                                                messageContent = content;
                                                Console.WriteLine($"找到有效的reasoning_content: {messageContent}");
                                            }
                                            else
                                            {
                                                Console.WriteLine($"忽略无效的reasoning_content: '{content}'");
                                            }
                                        }
                                        // 检查content字段
                                        else if (delta.TryGetProperty("content", out var content))
                                        {
                                            string contentStr = content.GetString();
                                            // 确保不是空字符串，null，或空引号
                                            if (!string.IsNullOrWhiteSpace(contentStr) && contentStr != "\"\"")
                                            {
                                                messageContent = contentStr;
                                                Console.WriteLine($"找到content: {messageContent}");
                                            }
                                            else
                                            {
                                                Console.WriteLine($"忽略无效的content: '{contentStr}'");
                                            }
                                        }
                                    }
                                }

                                // 如果找到了内容，更新UI
                                if (!string.IsNullOrEmpty(messageContent))
                                {
                                    fullResponse.Append(messageContent);

                                    // 更新UI上的消息
                                    await Application.Current.Dispatcher.InvokeAsync(() =>
                                    {
                                        _volcanoMessages[typingMessageIndex] = new ChatMessage
                                        {
                                            Sender = "火山方舟",
                                            Content = fullResponse.ToString(),
                                            Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                                            IsUser = false
                                        };

                                        // 滚动到底部
                                        VolcanoScrollViewer.ScrollToEnd();
                                    });

                                    // 添加一个小延迟，使打字效果更自然
                                    await Task.Delay(10);
                                }
                            }
                        }
                        catch (JsonException ex)
                        {
                            Console.WriteLine($"JSON解析错误: {ex.Message}");
                            continue;
                        }
                    }
                }

                // 处理完流后，如果有references数据，添加到响应末尾
                if (references.Count > 0)
                {
                    var referencesText = new StringBuilder("\n\n参考资料:\n");

                    // 最多展示3条参考资料，每条最多500个字符
                    int maxReferences = Math.Min(3, references.Count);
                    for (int i = 0; i < maxReferences; i++)
                    {
                        string reference = references[i].Trim();
                        // 如果参考资料太长，只显示前500个字符
                        if (reference.Length > 500)
                        {
                            reference = reference.Substring(0, 500) + "...";
                        }
                        referencesText.AppendLine($"{i + 1}. {reference}\n");
                    }

                    // 如果有更多参考资料，添加提示
                    if (references.Count > maxReferences)
                    {
                        referencesText.AppendLine($"(还有 {references.Count - maxReferences} 条参考资料未显示)");
                    }

                    string finalResponse = fullResponse.ToString();
                    if (!string.IsNullOrEmpty(finalResponse))
                    {
                        finalResponse += referencesText.ToString();
                    }
                    else
                    {
                        finalResponse = "根据参考资料整理如下：\n" + referencesText.ToString();
                    }

                    // 更新UI上的消息
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        _volcanoMessages[typingMessageIndex] = new ChatMessage
                        {
                            Sender = "火山方舟",
                            Content = finalResponse,
                            Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                            IsUser = false
                        };

                        // 滚动到底部
                        VolcanoScrollViewer.ScrollToEnd();
                    });

                    receivedAnyContent = true;
                }

                // 如果没有收到任何响应，显示错误消息
                if (!receivedAnyContent)
                {
                    Console.WriteLine("没有收到任何内容");

                    // 尝试读取整个响应体
                    string entireResponse = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"完整响应内容: {entireResponse}");

                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        _volcanoMessages[typingMessageIndex] = new ChatMessage
                        {
                            Sender = "火山方舟",
                            Content = "抱歉，服务器没有返回任何响应。请检查API配置是否正确。服务器返回:" + entireResponse,
                            Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                            IsUser = false
                        };
                    });
                }
            }
            catch (Exception ex)
            {
                // 输出详细错误到控制台
                Console.WriteLine($"火山方舟API调用错误: {ex.Message}");
                Console.WriteLine($"异常堆栈: {ex.StackTrace}");

                // 更新UI显示错误消息
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    _volcanoMessages[typingMessageIndex] = new ChatMessage
                    {
                        Sender = "火山方舟",
                        Content = $"抱歉，发生错误: {ex.Message}",
                        Timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        IsUser = false
                    };
                });

                // 重新抛出异常，让调用方处理
                throw;
            }
        }

        /// <summary>
        /// 处理火山方舟输入框按键事件
        /// </summary>
        private void VolcanoMessageInputBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter && !e.KeyboardDevice.Modifiers.HasFlag(ModifierKeys.Shift))
            {
                e.Handled = true;
                SendVolcanoMessageButton_Click(sender, e);
            }
        }

        /// <summary>
        /// 复制AI消息内容
        /// </summary>
        private void CopyVolcanoMessage_Click(object sender, RoutedEventArgs e)
        {
            Button button = sender as Button;
            if (button != null && button.Tag != null)
            {
                string messageContent = button.Tag.ToString();
                Clipboard.SetText(messageContent);

                // 显示复制成功提示（可选）
                var originalContent = button.Content;
                button.Content = "已复制";

                // 1.5秒后恢复原始内容
                var timer = new System.Windows.Threading.DispatcherTimer();
                timer.Interval = TimeSpan.FromSeconds(1.5);
                timer.Tick += (s, args) =>
                {
                    button.Content = originalContent;
                    timer.Stop();
                };
                timer.Start();
            }
        }

        /// <summary>
        /// 从数据库加载咖啡数据
        /// </summary>
        private async void LoadCoffeeData()
        {
            try
            {
                // 从数据库获取咖啡数据
                var response = await _supabaseClient
                    .From<CoffeeItem>()
                    .Select("*")
                    .Order(x => x.Date, Ordering.Descending) // 默认按日期降序排列
                    .Get();

                var coffeeItems = response.Models;

                // 更新DataGrid的数据源
                CoffeeDataGrid.ItemsSource = new ObservableCollection<CoffeeItem>(coffeeItems);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载咖啡数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 隐藏进度条
                CoffeeLoadingProgress.Visibility = Visibility.Collapsed;
            }
        }

        private async void QueryStockButton_Click(object sender, RoutedEventArgs e)
        {
            string query = StockQueryTextBox.Text.Trim();
            if (string.IsNullOrEmpty(query))
            {
                MessageBox.Show("请输入股票名称或代码", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            try
            {
                // 显示加载状态
                StockLoadingPanel.Visibility = Visibility.Visible;
                StockLoadingText.Text = "正在查询股票数据...";

                // 查询股票信息
                var stockInfo = await _stockService.GetStockInfoFromQueryAsync(query);

                if (stockInfo != null)
                {
                    // 创建一个新的WrapPanel来显示单个股票信息
                    StockItemsPanel.Children.Clear();
                    StockItemsPanel.Children.Add(CreateStockCard(stockInfo));

                    // 清空输入框的内容
                    StockQueryTextBox.Clear();
                    // 显示提示文本
                    StockQueryPlaceholder.Visibility = Visibility.Visible;
                }
                else
                {
                    if (query.All(char.IsDigit))
                    {
                        MessageBox.Show("未找到该股票代码的相关信息", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("未找到该股票名称的相关信息，请尝试使用股票代码查询", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"查询失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                StockLoadingPanel.Visibility = Visibility.Collapsed;
            }
        }

        private void StockQueryTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            // 当输入框获得焦点时，如果显示的是默认提示文本，则隐藏提示文本
            if (string.IsNullOrEmpty(StockQueryTextBox.Text))
            {
                StockQueryPlaceholder.Visibility = Visibility.Collapsed;
            }
        }

        private void StockQueryTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            // 当输入框失去焦点时，如果输入框为空，则显示提示文本
            if (string.IsNullOrEmpty(StockQueryTextBox.Text))
            {
                StockQueryPlaceholder.Visibility = Visibility.Visible;
            }
        }

        // DeepSeek对话框的输入框焦点事件处理 - 已移除
        private void MessageInputBox_GotFocus(object sender, RoutedEventArgs e)
        {
            /*
            if (string.IsNullOrEmpty(MessageInputBox.Text))
            {
                MessageInputPlaceholder.Visibility = Visibility.Collapsed;
            }
            */
        }

        private void MessageInputBox_LostFocus(object sender, RoutedEventArgs e)
        {
            /*
            if (string.IsNullOrEmpty(MessageInputBox.Text))
            {
                MessageInputPlaceholder.Visibility = Visibility.Visible;
            }
            */
        }

        // 火山方舟对话框的输入框焦点事件处理
        private void VolcanoMessageInputBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(VolcanoMessageInputBox.Text))
            {
                VolcanoMessageInputPlaceholder.Visibility = Visibility.Collapsed;
            }
        }

        private void VolcanoMessageInputBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(VolcanoMessageInputBox.Text))
            {
                VolcanoMessageInputPlaceholder.Visibility = Visibility.Visible;
            }
        }

        /// <summary>
        /// 处理查看导演资料按钮点击事件
        /// </summary>
        private void ViewDirectorInfo_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var director = button.Tag as MovieDirector;

                if (director == null)
                {
                    MessageBox.Show("无法获取导演信息", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                var directorInfoWindow = new DirectorInfoWindow(director.DirectorEnglishName, _supabaseClient);
                directorInfoWindow.Owner = this;
                directorInfoWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"查看导演信息失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 导出导演信息到CSV文件
        /// </summary>
        private async void ExportDirectors_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 创建文件夹选择对话框
                var folderDialog = new CommonOpenFileDialog
                {
                    Title = "选择导出文件夹",
                    IsFolderPicker = true,
                    InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                    AddToMostRecentlyUsedList = false,
                    AllowNonFileSystemItems = false,
                    DefaultDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments),
                    EnsureFileExists = true,
                    EnsurePathExists = true,
                    EnsureReadOnly = false,
                    EnsureValidNames = true,
                    Multiselect = false,
                    ShowPlacesList = true
                };

                // 显示对话框
                if (folderDialog.ShowDialog() != CommonFileDialogResult.Ok)
                {
                    return;
                }

                string folderPath = folderDialog.FileName;

                // 创建并显示进度对话框
                var progressWindow = new ExportProgressWindow("导演信息");
                progressWindow.Owner = this;
                progressWindow.Show();

                // 开始导出操作
                string exportedFile = await _exportService.ExportDirectorsToCsvAsync(folderPath, (current, total) =>
                {
                    // 更新进度
                    Dispatcher.Invoke(() => progressWindow.UpdateProgress(current, total));
                });

                // 导出完成
                Dispatcher.Invoke(() =>
                {
                    progressWindow.SetCompleted();

                    if (!string.IsNullOrEmpty(exportedFile))
                    {
                        MessageBox.Show($"导出完成\n\n文件已保存到: {exportedFile}", "导出成功", MessageBoxButton.OK, MessageBoxImage.Information);

                        // 打开导出文件夹
                        System.Diagnostics.Process.Start("explorer.exe", $"/select,\"{exportedFile}\"");
                    }
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出导演数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void FilterButton_Click(object sender, RoutedEventArgs e)
        {
            // 显示筛选选项弹窗
            FilterOptionsPopup.IsOpen = true;
        }

        private async void RatingFilterButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 设置当前排序方式
                _currentSortField = "rating";
                _currentSortOrder = Ordering.Descending;
                _lastFilterType = "rating";

                // 重置页码
                _currentPage = 1;

                // 加载数据
                // 显示加载进度条
                MovieLoadingProgressGrid.Visibility = Visibility.Visible;
                MovieItemsControl.Visibility = Visibility.Collapsed;

                await LoadPageData(_currentPage);

                // 更新分页按钮状态
                UpdatePaginationButtons();

                // 隐藏进度条，显示内容
                MovieLoadingProgressGrid.Visibility = Visibility.Collapsed;
                MovieItemsControl.Visibility = Visibility.Visible;

                // 关闭弹窗
                FilterOptionsPopup.IsOpen = false;
            }
            catch (Exception ex)
            {
                MovieLoadingProgressGrid.Visibility = Visibility.Collapsed;
                MovieItemsControl.Visibility = Visibility.Visible;
                MessageBox.Show($"筛选电影失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void TimeFilterButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 设置当前排序方式
                _currentSortField = "mdate";
                _currentSortOrder = Ordering.Descending;
                _lastFilterType = "time";

                // 重置页码
                _currentPage = 1;

                // 加载数据
                // 显示加载进度条
                MovieLoadingProgressGrid.Visibility = Visibility.Visible;
                MovieItemsControl.Visibility = Visibility.Collapsed;

                await LoadPageData(_currentPage);

                // 更新分页按钮状态
                UpdatePaginationButtons();

                // 隐藏进度条，显示内容
                MovieLoadingProgressGrid.Visibility = Visibility.Collapsed;
                MovieItemsControl.Visibility = Visibility.Visible;

                // 关闭弹窗
                FilterOptionsPopup.IsOpen = false;
            }
            catch (Exception ex)
            {
                MovieLoadingProgressGrid.Visibility = Visibility.Collapsed;
                MovieItemsControl.Visibility = Visibility.Visible;
                MessageBox.Show($"筛选电影失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 评奖筛选按钮点击事件
        /// </summary>
        private void AwardsFilterButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 关闭筛选弹窗
                FilterOptionsPopup.IsOpen = false;

                // 打开评奖筛选窗口
                var awardsWindow = new AwardsFilterWindow(_supabaseClient, OnAwardSelected)
                {
                    Owner = this
                };
                awardsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开评奖筛选窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 获奖筛选回调函数
        /// </summary>
        private async void OnAwardSelected(string selectedAward)
        {
            try
            {
                // 显示加载进度条
                MovieLoadingProgressGrid.Visibility = Visibility.Visible;
                MovieItemsControl.Visibility = Visibility.Collapsed;

                if (string.IsNullOrEmpty(selectedAward))
                {
                    // 清除筛选，重新加载所有数据
                    _lastFilterType = "";
                    _currentPage = 1;
                    await LoadPageData(_currentPage);
                }
                else
                {
                    // 按获奖信息筛选
                    await FilterMoviesByAward(selectedAward);
                }

                // 更新分页按钮状态
                UpdatePaginationButtons();

                // 隐藏进度条，显示内容
                MovieLoadingProgressGrid.Visibility = Visibility.Collapsed;
                MovieItemsControl.Visibility = Visibility.Visible;
            }
            catch (Exception ex)
            {
                MovieLoadingProgressGrid.Visibility = Visibility.Collapsed;
                MovieItemsControl.Visibility = Visibility.Visible;
                MessageBox.Show($"筛选电影失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 按获奖信息筛选电影
        /// </summary>
        private async Task FilterMoviesByAward(string selectedAward)
        {
            try
            {
                // 获取分类对应的关键词
                var keywords = GetAwardCategoryKeywords(selectedAward);

                // 从数据库获取所有电影
                var allMoviesQuery = _supabaseClient
                    .From<MovieItem>()
                    .Select(x => new object[] { x.PosterUrl, x.Title, x.Director, x.Country, x.Intro, x.Rating, x.Douban, x.Sublink, x.Magnet, x.ReleaseDate, x.Awards })
                    .Filter("ccprize", Supabase.Postgrest.Constants.Operator.Not, "null");

                var allMoviesResponse = await allMoviesQuery.Get();
                var allMovies = allMoviesResponse.Models;

                // 在客户端进行智能筛选
                var filteredMovies = allMovies.Where(movie =>
                {
                    if (string.IsNullOrWhiteSpace(movie.Awards))
                        return false;

                    var awardText = movie.Awards.ToLower();
                    return keywords.Any(keyword => awardText.Contains(keyword.ToLower()));
                }).ToList();

                // 添加排序
                if (_currentSortField == "rating")
                {
                    filteredMovies = _currentSortOrder == Ordering.Ascending
                        ? filteredMovies.OrderBy(x => x.Rating).ToList()
                        : filteredMovies.OrderByDescending(x => x.Rating).ToList();
                }
                else if (_currentSortField == "mdate")
                {
                    filteredMovies = _currentSortOrder == Ordering.Ascending
                        ? filteredMovies.OrderBy(x => x.ReleaseDate).ToList()
                        : filteredMovies.OrderByDescending(x => x.ReleaseDate).ToList();
                }
                else
                {
                    // 默认按ID排序
                    filteredMovies = filteredMovies.OrderByDescending(x => x.Id).ToList();
                }

                // 更新UI显示
                MovieItemsControl.ItemsSource = filteredMovies;

                // 更新电影总数显示
                _totalMovies = filteredMovies.Count;
                TotalMoviesText.Text = _totalMovies.ToString();

                // 重置分页相关变量
                _currentPage = 1;
                _totalPages = 1; // 筛选结果显示在一页中

                // 设置筛选类型
                _lastFilterType = "awards";

                // 显示筛选结果提示
                MessageBox.Show($"找到 {filteredMovies.Count} 部属于 \"{selectedAward}\" 类别的电影", "筛选结果",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                throw new Exception($"按获奖信息筛选失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取获奖分类对应的关键词
        /// </summary>
        private List<string> GetAwardCategoryKeywords(string category)
        {
            var categoryKeywords = new Dictionary<string, List<string>>
            {
                ["CC标准收藏"] = new List<string> { "cc", "criterion", "标准收藏", "标准", "收藏" },
                ["视与听影评人百佳"] = new List<string> { "视与听", "sight", "sound", "影评人", "百佳", "十佳" },
                ["豆瓣电影TOP250"] = new List<string> { "豆瓣", "douban", "top250", "top", "250" },
                ["奥斯卡奖项"] = new List<string> { "奥斯卡", "oscar", "academy", "学院奖" },
                ["戛纳电影节"] = new List<string> { "戛纳", "cannes", "金棕榈", "palme", "戛纳电影节" },
                ["威尼斯电影节"] = new List<string> { "威尼斯", "venice", "金狮", "lion", "威尼斯电影节" },
                ["柏林电影节"] = new List<string> { "柏林", "berlin", "金熊", "bear", "柏林电影节" },
                ["金球奖"] = new List<string> { "金球", "golden", "globe" },
                ["英国电影学院奖"] = new List<string> { "bafta", "英国电影", "学院奖" },
                ["美国导演工会奖"] = new List<string> { "导演工会", "dga", "directors", "guild" },
                ["美国编剧工会奖"] = new List<string> { "编剧工会", "wga", "writers", "guild" },
                ["圣丹斯电影节"] = new List<string> { "圣丹斯", "sundance" },
                ["多伦多电影节"] = new List<string> { "多伦多", "toronto", "tiff" },
                ["纽约影评人协会奖"] = new List<string> { "纽约影评", "new york", "critics" },
                ["洛杉矶影评人协会奖"] = new List<string> { "洛杉矶影评", "los angeles", "critics" },
                ["国家影评人协会奖"] = new List<string> { "国家影评", "national", "critics" },
                ["AFI百年系列"] = new List<string> { "afi", "american film institute", "百年", "century" },
                ["AFI TOP250"] = new List<string> { "afi top250", "afi top 250", "afi250", "afi 250" },
                ["IMDb Top 250"] = new List<string> { "imdb", "top250", "internet movie database", "imdb top250", "imdb top 250" },
                ["TSPDT TOP1000"] = new List<string> { "tspdt", "top1000", "top 1000", "they shoot pictures", "tspdt1000" },
                ["佳片有约"] = new List<string> { "佳片有约", "佳片", "有约" },
                ["乱耳字幕组"] = new List<string> { "乱耳", "字幕组", "乱耳字幕", "乱耳字幕组" },
                ["时代周刊"] = new List<string> { "时代", "time", "magazine", "时代周刊" },
                ["电影手册"] = new List<string> { "电影手册", "cahiers", "cinema" },
                ["帝国杂志"] = new List<string> { "帝国", "empire", "magazine", "帝国杂志" },
                ["娱乐周刊"] = new List<string> { "娱乐周刊", "entertainment", "weekly" },
                ["英国电影协会"] = new List<string> { "bfi", "british film institute", "英国电影协会" },
                ["法国电影手册"] = new List<string> { "cahiers du cinema", "电影手册", "法国电影手册" },
                ["纽约时报"] = new List<string> { "纽约时报", "new york times", "nyt" },
                ["卫报"] = new List<string> { "卫报", "guardian", "the guardian" },
                ["BBC文化"] = new List<string> { "bbc", "bbc culture", "bbc文化" },
                ["罗杰·伊伯特"] = new List<string> { "roger ebert", "伊伯特", "ebert" },
                ["村声杂志"] = new List<string> { "village voice", "村声", "村声杂志" },
                ["其他获奖"] = new List<string> { } // 其他获奖需要特殊处理
            };

            return categoryKeywords.ContainsKey(category) ? categoryKeywords[category] : new List<string>();
        }

        /// <summary>
        /// 处理电影查询按钮点击事件
        /// </summary>
        private async void MovieQueryButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string movieName = MovieQueryTextBox.Text?.Trim();
                if (string.IsNullOrEmpty(movieName))
                {
                    MessageBox.Show("请输入电影名称", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 禁用按钮，防止重复点击
                MovieQueryButton.IsEnabled = false;

                // 显示加载指示器
                MovieQueryLoadingGrid.Visibility = Visibility.Visible;
                MovieSearchResultsPanel.Visibility = Visibility.Collapsed;
                MovieQueryResultBorder.Visibility = Visibility.Collapsed;

                // 查询电影
                var movies = await _tmdbService.SearchMovieAsync(movieName);

                if (movies == null || movies.Count == 0)
                {
                    MessageBox.Show($"未找到与\"{movieName}\"相关的电影", "未找到", MessageBoxButton.OK, MessageBoxImage.Information);
                    MovieQueryLoadingGrid.Visibility = Visibility.Collapsed;
                    MovieQueryButton.IsEnabled = true;
                    return;
                }

                if (movies.Count == 1)
                {
                    // 如果只有一个结果，直接显示详情
                    DisplayMovieResult(movies[0]);
                    _currentMovie = movies[0];
                }
                else
                {
                    // 如果有多个结果，显示列表让用户选择
                    SearchResultsTitle.Text = $"找到 {movies.Count} 条与\"{movieName}\"相关的结果";
                    SearchResultsItemsControl.ItemsSource = movies;
                    MovieSearchResultsPanel.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"查询电影时出错: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 隐藏加载指示器
                MovieQueryLoadingGrid.Visibility = Visibility.Collapsed;
                // 重新启用按钮
                MovieQueryButton.IsEnabled = true;
            }
        }

        /// <summary>
        /// 处理查看电影详情按钮点击事件
        /// </summary>
        private void ViewMovieDetails_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 获取按钮标签中的电影信息
                if (sender is Button button && button.Tag is MovieSearchResult movie)
                {
                    // 显示选中电影的详情
                    DisplayMovieResult(movie);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载电影详情失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 显示电影查询结果
        /// </summary>
        private void DisplayMovieResult(MovieSearchResult movie)
        {
            // 存储当前电影信息，以便保存至数据库时使用
            _currentMovie = movie;

            // 隐藏搜索结果列表
            MovieSearchResultsPanel.Visibility = Visibility.Collapsed;

            // 显示结果容器
            MovieQueryResultBorder.Visibility = Visibility.Visible;

            // 设置电影海报
            if (!string.IsNullOrEmpty(movie.PosterPath))
            {
                MoviePosterImage.ImageSource = new System.Windows.Media.Imaging.BitmapImage(new Uri(movie.PosterPath));
            }
            else
            {
                // 设置默认海报图片
                MoviePosterImage.ImageSource = null;
            }

            // 设置电影信息
            MovieTitleText.Text = movie.Title;
            MovieDirectorText.Text = movie.Director ?? "未知";
            MovieCastText.Text = movie.Cast ?? "未知";
            MovieGenreText.Text = movie.Genres ?? "未知";
            MovieCountryText.Text = movie.Country ?? "未知";
            MovieReleaseDateText.Text = movie.ReleaseDate ?? "未知";
            MovieRatingText.Text = movie.VoteAverage.ToString("F1") + " / 10";
            MovieOverviewText.Text = movie.Overview ?? "暂无简介";
        }

        /// <summary>
        /// 在输入电影查询框按下Enter键时触发查询
        /// </summary>
        private void MovieQueryTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                MovieQueryButton_Click(sender, e);
            }
        }

        /// <summary>
        /// 将电影信息保存到数据库的按钮点击事件处理方法
        /// </summary>
        private async void SaveToDbButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 检查是否有电影信息
                if (_currentMovie == null)
                {
                    MessageBox.Show("请先查询或选择电影！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // 禁用按钮防止重复点击
                SaveToDbButton.IsEnabled = false;

                // 创建输入对话框以收集额外信息
                var inputFields = new Dictionary<string, string>
                {
                    { "磁力链接", "" },
                    { "字幕链接", "" },
                    { "豆瓣链接", "" },
                    { "获得奖项", "" }
                };

                var inputDialog = new InputDialog("添加电影附加信息",
                    $"即将保存电影《{_currentMovie.Title}》，请填写以下可选信息：", inputFields);

                inputDialog.Owner = this;

                // 显示对话框并等待用户输入
                bool? result = inputDialog.ShowDialog();

                // 如果用户取消了操作
                if (result != true)
                {
                    SaveToDbButton.IsEnabled = true;
                    return;
                }

                // 获取用户输入
                string magnetLink = inputDialog.InputValues["磁力链接"];
                string subtitleLink = inputDialog.InputValues["字幕链接"];
                string doubanLink = inputDialog.InputValues["豆瓣链接"];
                string awards = inputDialog.InputValues["获得奖项"];

                // 构建电影数据对象
                var movieItem = new MovieItem
                {
                    PosterUrl = _currentMovie.PosterUrl,
                    Title = _currentMovie.Title,
                    Director = _currentMovie.Director,

                    Country = _currentMovie.Country,
                    Intro = _currentMovie.Overview,
                    Rating = _currentMovie.Rating.ToString(),
                    ReleaseDate = _currentMovie.ReleaseDate,
                    Magnet = magnetLink,
                    Sublink = subtitleLink,
                    Douban = doubanLink,
                    Awards = awards,
                    CreatedAt = DateTime.Now
                };

                // 显示确认信息
                string additionalInfo = "";
                if (!string.IsNullOrEmpty(magnetLink)) additionalInfo += "\n已添加磁力链接";
                if (!string.IsNullOrEmpty(subtitleLink)) additionalInfo += "\n已添加字幕链接";
                if (!string.IsNullOrEmpty(doubanLink)) additionalInfo += "\n已添加豆瓣链接";
                if (!string.IsNullOrEmpty(awards)) additionalInfo += "\n已添加获奖信息";

                // 确认是否保存
                var confirmResult = MessageBox.Show(
                    $"确定要将电影《{_currentMovie.Title}》保存到数据库吗？{additionalInfo}",
                    "确认保存",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (confirmResult != MessageBoxResult.Yes)
                {
                    SaveToDbButton.IsEnabled = true;
                    return;
                }

                // 首先获取所有记录并找出最大ID值
                var allRecords = await _supabaseClient
                    .From<MovieItem>()
                    .Select(x => new object[] { x.Id })
                    .Order(x => x.Id, Supabase.Postgrest.Constants.Ordering.Descending)
                    .Limit(1)
                    .Get();

                // 获取最大ID值
                int maxId = 0;
                if (allRecords.Models.Count > 0)
                {
                    maxId = allRecords.Models.Max(m => m.Id);
                }

                // 设置电影ID为最大ID+1
                movieItem.Id = maxId + 1;

                // 将电影信息保存到数据库
                var response = await _supabaseClient.From<MovieItem>().Insert(movieItem);

                if (response != null && response.ResponseMessage.IsSuccessStatusCode)
                {
                    MessageBox.Show($"电影《{_currentMovie.Title}》已成功保存到数据库！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);

                    // 重置当前电影变量
                    _currentMovie = null!;
                }
                else
                {
                    MessageBox.Show($"保存失败: {response?.ResponseMessage?.ReasonPhrase ?? "未知错误"}",
                        "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }

                // 重新启用保存按钮
                SaveToDbButton.IsEnabled = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"保存过程中发生错误: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 重置当前电影变量
                _currentMovie = null!;
            }
        }

        #region 健康管理相关方法

        /// <summary>
        /// 加载健康数据并更新UI
        /// </summary>
        private async void LoadHealthData()
        {
            try
            {
                // 加载今日数据
                var todayData = await _healthService.GetTodayDataAsync();
                if (todayData != null)
                {
                    TodayStepsText.Text = todayData.Steps.ToString();
                    LatestSystolicText.Text = todayData.SystolicPressure.ToString();
                    LatestDiastolicText.Text = todayData.DiastolicPressure.ToString();
                }
                else
                {
                    TodayStepsText.Text = "0";
                    LatestSystolicText.Text = "120";
                    LatestDiastolicText.Text = "80";
                }

                // 加载本月总步数
                var now = DateTime.Now;
                var monthlySteps = await _healthService.GetMonthlyStepsAsync(now.Year, now.Month);
                MonthlyStepsText.Text = monthlySteps.ToString();

                // 计算并显示单日平均步数
                var dailyAverage = await CalculateDailyAverageSteps(now.Year, now.Month, now.Day);
                DailyAverageStepsText.Text = dailyAverage.ToString("N0");

                // 更新目标提示标签栏
                UpdateTargetProgress(monthlySteps, now);

                // 加载图表数据
                await LoadHealthCharts();

                // 加载血压统计数据
                await LoadBloodPressureStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载健康数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// 加载健康数据图表
        /// </summary>
        private async Task LoadHealthCharts()
        {
            try
            {
                var now = DateTime.Now;
                var monthlyData = await _healthService.GetMonthlyDataAsync(now.Year, now.Month);

                // 步数图表
                var stepsValues = new ChartValues<double>();
                var stepsLabels = new List<string>();

                // 血压图表 - 使用0值替代NaN，避免动画错误
                var systolicValues = new ChartValues<double>();
                var diastolicValues = new ChartValues<double>();
                var abnormalSystolicValues = new ChartValues<double>();
                var abnormalDiastolicValues = new ChartValues<double>();

                // 按日期排序
                var sortedData = monthlyData.OrderBy(h => h.Date).ToList();

                foreach (var data in sortedData)
                {
                    stepsValues.Add(data.Steps);
                    stepsLabels.Add(data.Date.ToString("MM-dd"));

                    // 正常血压数据 - 使用0替代NaN
                    if (!data.IsBloodPressureAbnormal)
                    {
                        systolicValues.Add(data.SystolicPressure);
                        diastolicValues.Add(data.DiastolicPressure);
                        abnormalSystolicValues.Add(0); // 使用0替代NaN
                        abnormalDiastolicValues.Add(0); // 使用0替代NaN
                    }
                    else
                    {
                        // 异常血压数据 - 使用0替代NaN
                        systolicValues.Add(0); // 使用0替代NaN
                        diastolicValues.Add(0); // 使用0替代NaN
                        abnormalSystolicValues.Add(data.SystolicPressure);
                        abnormalDiastolicValues.Add(data.DiastolicPressure);
                    }
                }

                // 更新步数图表
                StepsChart.Series = new SeriesCollection
                {
                    new LineSeries
                    {
                        Title = "每日步数",
                        Values = stepsValues,
                        Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#5D6EFF")),
                        Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#205D6EFF")),
                        StrokeThickness = 3,
                        PointGeometry = DefaultGeometries.Circle,
                        PointGeometrySize = 8
                    }
                };

                // 更新血压图表 - 使用柱状图
                BloodPressureChart.Series = new SeriesCollection
                {
                    new ColumnSeries
                    {
                        Title = "收缩压(正常)",
                        Values = systolicValues,
                        Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")),
                        Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")),
                        StrokeThickness = 1,
                        ColumnPadding = 2,
                        MaxColumnWidth = 25
                    },
                    new ColumnSeries
                    {
                        Title = "舒张压(正常)",
                        Values = diastolicValues,
                        Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2196F3")),
                        Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#2196F3")),
                        StrokeThickness = 1,
                        ColumnPadding = 2,
                        MaxColumnWidth = 25
                    },
                    new ColumnSeries
                    {
                        Title = "收缩压(异常)",
                        Values = abnormalSystolicValues,
                        Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF5252")),
                        Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF5252")),
                        StrokeThickness = 1,
                        ColumnPadding = 2,
                        MaxColumnWidth = 25
                    },
                    new ColumnSeries
                    {
                        Title = "舒张压(异常)",
                        Values = abnormalDiastolicValues,
                        Fill = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF9800")),
                        Stroke = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF9800")),
                        StrokeThickness = 1,
                        ColumnPadding = 2,
                        MaxColumnWidth = 25
                    }
                };

                // 设置X轴标签
                if (StepsChart.AxisX.Count > 0)
                {
                    StepsChart.AxisX[0].Labels = stepsLabels;
                }
                if (BloodPressureChart.AxisX.Count > 0)
                {
                    BloodPressureChart.AxisX[0].Labels = stepsLabels;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载健康图表失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 录入健康数据按钮点击事件
        /// </summary>
        private async void AddHealthDataButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addWindow = new WpfAdmin.AddHealthDataWindow();
                addWindow.Owner = this;

                bool? result = addWindow.ShowDialog();

                if (result == true && addWindow.DataSaved)
                {
                    // 刷新健康数据显示
                    LoadHealthData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开录入窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 血压录入按钮点击事件
        /// </summary>
        private async void AddBloodPressureButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var addWindow = new WpfAdmin.AddBloodPressureWindow();
                addWindow.Owner = this;

                bool? result = addWindow.ShowDialog();

                if (result == true && addWindow.DataSaved)
                {
                    // 刷新血压统计数据
                    await LoadBloodPressureStatistics();
                    // 也刷新健康数据显示（如果需要）
                    LoadHealthData();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开血压录入窗口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 本月统计按钮点击事件
        /// </summary>
        private void MonthlyStatsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ShowMonthlyStatsDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"显示月度统计失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 显示月度统计对话框
        /// </summary>
        private void ShowMonthlyStatsDialog()
        {
            var currentMonth = DateTime.Now.ToString("yyyy-MM");
            var csvPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "HealthData", "health_data.csv");

            var monthlyData = LoadMonthlyStepsData(currentMonth);

            // 创建日历视图窗口
            var calendarWindow = new Window
            {
                Title = "本月步数统计",
                Width = 1000,
                Height = 800,
                MinWidth = 900,
                MinHeight = 700,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                Owner = this,
                ResizeMode = ResizeMode.CanResize,
                Background = new SolidColorBrush(Color.FromRgb(240, 248, 255)), // 淡蓝色背景
                FontFamily = new FontFamily("Microsoft YaHei UI")
            };

            var mainGrid = new Grid();
            mainGrid.Margin = new Thickness(15);
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(60) });
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(420, GridUnitType.Pixel) }); // 减少日历高度
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(220, GridUnitType.Pixel) }); // 增加统计区域高度
            mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(60) });

            // 标题区域
            var titleBorder = new Border
            {
                Background = new LinearGradientBrush(
                    Color.FromRgb(70, 130, 180),
                    Color.FromRgb(100, 149, 237),
                    90),
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(0, 0, 0, 10)
            };

            var titleText = new TextBlock
            {
                Text = $"{DateTime.Now:yyyy年MM月} 步数统计",
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };
            titleBorder.Child = titleText;
            Grid.SetRow(titleBorder, 0);
            mainGrid.Children.Add(titleBorder);

            // 日历网格
            var calendarBorder = new Border
            {
                Background = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(200, 200, 200)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(0, 0, 0, 10)
            };
            var calendarGrid = CreateCalendarGrid(monthlyData);
            calendarBorder.Child = calendarGrid;
            Grid.SetRow(calendarBorder, 1);
            mainGrid.Children.Add(calendarBorder);

            // 统计信息
            var statsBorder = new Border
            {
                Background = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(200, 200, 200)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(8),
                Margin = new Thickness(0, 0, 0, 10)
            };
            var statsPanel = CreateStatsPanel(monthlyData);
            statsBorder.Child = statsPanel;
            Grid.SetRow(statsBorder, 2);
            mainGrid.Children.Add(statsBorder);

            // 关闭按钮
            var closeButton = new Button
            {
                Content = "关闭",
                Width = 100,
                Height = 35,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                Background = new LinearGradientBrush(
                    Color.FromRgb(70, 130, 180),
                    Color.FromRgb(100, 149, 237),
                    90),
                Foreground = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(70, 130, 180)),
                BorderThickness = new Thickness(1),
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Cursor = System.Windows.Input.Cursors.Hand
            };
            closeButton.Click += (s, e) => calendarWindow.Close();
            Grid.SetRow(closeButton, 3);
            mainGrid.Children.Add(closeButton);

            calendarWindow.Content = mainGrid;
            calendarWindow.ShowDialog();
        }

        /// <summary>
        /// 创建日历网格
        /// </summary>
        private Grid CreateCalendarGrid(List<HealthData> monthlyData)
        {
            var grid = new Grid();
            grid.Background = Brushes.White;
            grid.Margin = new Thickness(10);

            // 创建7列（一周7天）
            for (int i = 0; i < 7; i++)
            {
                grid.ColumnDefinitions.Add(new ColumnDefinition());
            }

            // 创建7行（标题行 + 6周）
            // 第一行是星期标题，固定高度
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(35) });

            // 其余6行是日期，每行固定高度确保内容显示
            for (int i = 0; i < 6; i++)
            {
                grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(60) });
            }

            // 添加星期标题
            var weekDays = new[] { "日", "一", "二", "三", "四", "五", "六" };
            for (int i = 0; i < 7; i++)
            {
                var dayHeader = new TextBlock
                {
                    Text = weekDays[i],
                    FontWeight = FontWeights.Bold,
                    FontSize = 14,
                    Foreground = Brushes.White,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Center,
                    Padding = new Thickness(5)
                };

                var border = new Border
                {
                    Background = new LinearGradientBrush(
                        Color.FromRgb(70, 130, 180),
                        Color.FromRgb(100, 149, 237),
                        90),
                    BorderBrush = Brushes.White,
                    BorderThickness = new Thickness(1),
                    Child = dayHeader
                };

                Grid.SetRow(border, 0);
                Grid.SetColumn(border, i);
                grid.Children.Add(border);
            }

            // 获取当月第一天和天数
            var currentMonth = DateTime.Now;
            var firstDay = new DateTime(currentMonth.Year, currentMonth.Month, 1);
            var startDayOfWeek = (int)firstDay.DayOfWeek;
            var daysInMonth = DateTime.DaysInMonth(currentMonth.Year, currentMonth.Month);

            // 创建步数数据字典
            var stepsDict = monthlyData.ToDictionary(d => d.Date.Day, d => d.Steps);

            // 添加日期单元格
            for (int day = 1; day <= daysInMonth; day++)
            {
                var dayOfWeek = (startDayOfWeek + day - 1) % 7;
                var week = (startDayOfWeek + day - 1) / 7 + 1;

                var dayPanel = new StackPanel
                {
                    Margin = new Thickness(2),
                    VerticalAlignment = VerticalAlignment.Stretch,
                    HorizontalAlignment = HorizontalAlignment.Stretch
                };

                // 日期数字
                var dayText = new TextBlock
                {
                    Text = day.ToString(),
                    FontSize = 14,
                    FontWeight = FontWeights.Bold,
                    HorizontalAlignment = HorizontalAlignment.Left,
                    Foreground = new SolidColorBrush(Color.FromRgb(70, 130, 180)),
                    Margin = new Thickness(3, 2, 0, 0)
                };
                dayPanel.Children.Add(dayText);

                // 步数显示
                if (stepsDict.ContainsKey(day))
                {
                    var steps = stepsDict[day];
                    var stepsText = new TextBlock
                    {
                        Text = steps.ToString("N0"),
                        FontSize = 11,
                        FontWeight = FontWeights.SemiBold,
                        HorizontalAlignment = HorizontalAlignment.Center,
                        VerticalAlignment = VerticalAlignment.Center,
                        Margin = new Thickness(0, 3, 0, 0),
                        Foreground = GetStepsColorBrush(steps),
                        TextWrapping = TextWrapping.NoWrap
                    };
                    dayPanel.Children.Add(stepsText);
                }

                var dayBorder = new Border
                {
                    Background = new SolidColorBrush(Color.FromRgb(250, 250, 250)),
                    BorderBrush = new SolidColorBrush(Color.FromRgb(220, 220, 220)),
                    BorderThickness = new Thickness(1),
                    Child = dayPanel,
                    Height = 55, // 减少高度
                    CornerRadius = new CornerRadius(3),
                    Margin = new Thickness(1)
                };

                Grid.SetRow(dayBorder, week);
                Grid.SetColumn(dayBorder, dayOfWeek);
                grid.Children.Add(dayBorder);
            }

            return grid;
        }

        /// <summary>
        /// 根据步数获取颜色
        /// </summary>
        private Brush GetStepsColorBrush(int steps)
        {
            if (steps >= 10000) return Brushes.Green;
            if (steps >= 8000) return Brushes.Orange;
            if (steps >= 6000) return Brushes.Blue;
            return Brushes.Red;
        }

        /// <summary>
        /// 创建统计面板
        /// </summary>
        private StackPanel CreateStatsPanel(List<HealthData> monthlyData)
        {
            var panel = new StackPanel
            {
                Margin = new Thickness(10),
                Orientation = Orientation.Vertical
            };

            // 标题和导出按钮的容器
            var headerGrid = new Grid();
            headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(100) });

            // 标题
            var titleText = new TextBlock
            {
                Text = "步数统计",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(70, 130, 180)),
                VerticalAlignment = VerticalAlignment.Center
            };
            Grid.SetColumn(titleText, 0);
            headerGrid.Children.Add(titleText);

            // 导出按钮
            var exportButton = new Button
            {
                Content = "导出CSV",
                Width = 80,
                Height = 25,
                Background = new LinearGradientBrush(
                    Color.FromRgb(70, 130, 180),
                    Color.FromRgb(100, 149, 237),
                    90),
                Foreground = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(70, 130, 180)),
                BorderThickness = new Thickness(1),
                FontSize = 11,
                FontWeight = FontWeights.Bold,
                Cursor = System.Windows.Input.Cursors.Hand,
                VerticalAlignment = VerticalAlignment.Center
            };
            exportButton.Click += (s, e) => ExportMonthlyStepsData(monthlyData);
            Grid.SetColumn(exportButton, 1);
            headerGrid.Children.Add(exportButton);

            panel.Children.Add(headerGrid);

            if (monthlyData.Count == 0)
            {
                var noDataText = new TextBlock
                {
                    Text = "本月暂无步数记录",
                    FontSize = 14,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 20, 0, 0)
                };
                panel.Children.Add(noDataText);
                return panel;
            }

            // 本月总步数显示
            var totalSteps = monthlyData.Sum(d => d.Steps);
            var totalStepsPanel = new StackPanel
            {
                Orientation = Orientation.Horizontal,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 15, 0, 15)
            };

            var totalLabel = new TextBlock
            {
                Text = "本月总步数：",
                FontSize = 14,
                FontWeight = FontWeights.SemiBold,
                VerticalAlignment = VerticalAlignment.Center,
                Foreground = new SolidColorBrush(Color.FromRgb(70, 70, 70))
            };
            totalStepsPanel.Children.Add(totalLabel);

            var totalStepsText = new TextBlock
            {
                Text = totalSteps.ToString("N0"),
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(70, 130, 180)),
                Margin = new Thickness(10, 0, 0, 0),
                VerticalAlignment = VerticalAlignment.Center
            };
            totalStepsPanel.Children.Add(totalStepsText);

            var stepsUnit = new TextBlock
            {
                Text = " 步",
                FontSize = 14,
                FontWeight = FontWeights.SemiBold,
                VerticalAlignment = VerticalAlignment.Center,
                Foreground = new SolidColorBrush(Color.FromRgb(70, 70, 70)),
                Margin = new Thickness(5, 0, 0, 0)
            };
            totalStepsPanel.Children.Add(stepsUnit);

            panel.Children.Add(totalStepsPanel);

            // 分隔线
            var separator = new Border
            {
                Height = 1,
                Background = new SolidColorBrush(Color.FromRgb(220, 220, 220)),
                Margin = new Thickness(0, 10, 0, 15)
            };
            panel.Children.Add(separator);

            // 统计各个步数范围的天数（去掉6000步以下）
            var stats = new[]
            {
                new { Label = "6000-8000步", Color = new SolidColorBrush(Color.FromRgb(0, 123, 255)), Count = monthlyData.Count(d => d.Steps >= 6000 && d.Steps < 8000) },
                new { Label = "8000-10000步", Color = new SolidColorBrush(Color.FromRgb(255, 193, 7)), Count = monthlyData.Count(d => d.Steps >= 8000 && d.Steps < 10000) },
                new { Label = "10000步以上", Color = new SolidColorBrush(Color.FromRgb(40, 167, 69)), Count = monthlyData.Count(d => d.Steps >= 10000) }
            };

            foreach (var stat in stats)
            {
                var statGrid = new Grid();
                statGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(110) });
                statGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
                statGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(50) });
                statGrid.Margin = new Thickness(0, 3, 0, 3);

                // 标签
                var label = new TextBlock
                {
                    Text = stat.Label,
                    FontSize = 13,
                    FontWeight = FontWeights.SemiBold,
                    VerticalAlignment = VerticalAlignment.Center,
                    Foreground = new SolidColorBrush(Color.FromRgb(70, 70, 70))
                };
                Grid.SetColumn(label, 0);
                statGrid.Children.Add(label);

                // 进度条容器
                var progressBorder = new Border
                {
                    Background = new SolidColorBrush(Color.FromRgb(240, 240, 240)),
                    CornerRadius = new CornerRadius(8),
                    Height = 16,
                    VerticalAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(8, 0, 8, 0)
                };

                var progressBar = new ProgressBar
                {
                    Height = 14,
                    Maximum = monthlyData.Count > 0 ? monthlyData.Count : 1,
                    Value = stat.Count,
                    Foreground = stat.Color,
                    Background = Brushes.Transparent,
                    BorderThickness = new Thickness(0),
                    VerticalAlignment = VerticalAlignment.Center
                };
                progressBorder.Child = progressBar;
                Grid.SetColumn(progressBorder, 1);
                statGrid.Children.Add(progressBorder);

                // 天数
                var countText = new TextBlock
                {
                    Text = $"{stat.Count}天",
                    FontSize = 12,
                    FontWeight = FontWeights.Bold,
                    VerticalAlignment = VerticalAlignment.Center,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Foreground = stat.Color
                };
                Grid.SetColumn(countText, 2);
                statGrid.Children.Add(countText);

                panel.Children.Add(statGrid);
            }

            return panel;
        }

        /// <summary>
        /// 导出月度步数数据到CSV文件
        /// </summary>
        private void ExportMonthlyStepsData(List<HealthData> monthlyData)
        {
            try
            {
                // 创建保存文件对话框
                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "导出月度步数统计",
                    Filter = "CSV文件 (*.csv)|*.csv|所有文件 (*.*)|*.*",
                    DefaultExt = "csv",
                    FileName = $"月度步数统计_{DateTime.Now:yyyy年MM月}_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    var csvContent = new StringBuilder();

                    // 添加标题信息
                    csvContent.AppendLine($"月度步数统计报表");
                    csvContent.AppendLine($"统计月份,{DateTime.Now:yyyy年MM月}");
                    csvContent.AppendLine($"生成时间,{DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                    csvContent.AppendLine();

                    // 添加总体统计
                    var totalSteps = monthlyData.Sum(d => d.Steps);
                    var avgSteps = monthlyData.Count > 0 ? (int)(totalSteps / monthlyData.Count) : 0;
                    var maxSteps = monthlyData.Count > 0 ? monthlyData.Max(d => d.Steps) : 0;
                    var minSteps = monthlyData.Count > 0 ? monthlyData.Min(d => d.Steps) : 0;

                    csvContent.AppendLine("总体统计");
                    csvContent.AppendLine("项目,数值");
                    csvContent.AppendLine($"总步数,{totalSteps:N0}");
                    csvContent.AppendLine($"平均步数,{avgSteps:N0}");
                    csvContent.AppendLine($"最高步数,{maxSteps:N0}");
                    csvContent.AppendLine($"最低步数,{minSteps:N0}");
                    csvContent.AppendLine($"记录天数,{monthlyData.Count}");
                    csvContent.AppendLine();

                    // 添加分布统计
                    csvContent.AppendLine("步数分布统计");
                    csvContent.AppendLine("步数范围,天数,占比");

                    var stats = new[]
                    {
                        new { Label = "6000-8000步", Count = monthlyData.Count(d => d.Steps >= 6000 && d.Steps < 8000) },
                        new { Label = "8000-10000步", Count = monthlyData.Count(d => d.Steps >= 8000 && d.Steps < 10000) },
                        new { Label = "10000步以上", Count = monthlyData.Count(d => d.Steps >= 10000) }
                    };

                    foreach (var stat in stats)
                    {
                        var percentage = monthlyData.Count > 0 ? (double)stat.Count / monthlyData.Count * 100 : 0;
                        csvContent.AppendLine($"{stat.Label},{stat.Count},{percentage:F1}%");
                    }
                    csvContent.AppendLine();

                    // 添加详细数据
                    csvContent.AppendLine("详细数据");
                    csvContent.AppendLine("日期,步数,星期");

                    foreach (var data in monthlyData.OrderBy(d => d.Date))
                    {
                        var dayOfWeek = GetChineseDayOfWeek(data.Date.DayOfWeek);
                        csvContent.AppendLine($"{data.Date:yyyy-MM-dd},{data.Steps},{dayOfWeek}");
                    }

                    // 写入文件
                    File.WriteAllText(saveFileDialog.FileName, csvContent.ToString(), Encoding.UTF8);

                    MessageBox.Show($"数据已成功导出到：\n{saveFileDialog.FileName}", "导出成功",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出失败：{ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 获取中文星期名称
        /// </summary>
        private string GetChineseDayOfWeek(DayOfWeek dayOfWeek)
        {
            return dayOfWeek switch
            {
                DayOfWeek.Monday => "星期一",
                DayOfWeek.Tuesday => "星期二",
                DayOfWeek.Wednesday => "星期三",
                DayOfWeek.Thursday => "星期四",
                DayOfWeek.Friday => "星期五",
                DayOfWeek.Saturday => "星期六",
                DayOfWeek.Sunday => "星期日",
                _ => ""
            };
        }

        /// <summary>
        /// 加载指定月份的步数数据
        /// </summary>
        private List<HealthData> LoadMonthlyStepsData(string month)
        {
            var monthlyData = new List<HealthData>();
            var csvPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "HealthData", "health_data.csv");

            if (!File.Exists(csvPath))
                return monthlyData;

            try
            {
                var lines = File.ReadAllLines(csvPath);
                for (int i = 1; i < lines.Length; i++) // 跳过标题行
                {
                    var parts = lines[i].Split(',');
                    if (parts.Length >= 2)
                    {
                        // 实际格式：Date,Steps,SystolicPressure,DiastolicPressure,Notes
                        if (DateTime.TryParse(parts[0], out DateTime date) &&
                            int.TryParse(parts[1], out int steps))
                        {
                            if (date.ToString("yyyy-MM") == month)
                            {
                                monthlyData.Add(new HealthData
                                {
                                    Date = date,
                                    Steps = steps,
                                    Weight = 0 // 实际文件中没有体重数据
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"读取健康数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }

            return monthlyData;
        }

        /// <summary>
        /// 刷新血压数据按钮点击事件
        /// </summary>
        private async void RefreshBloodPressureButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadBloodPressureStatistics();
        }

        /// <summary>
        /// 加载血压统计数据
        /// </summary>
        private async Task LoadBloodPressureStatistics()
        {
            try
            {
                var today = DateTime.Today;
                var statistics = await _bloodPressureService.GetDailyStatisticsAsync(today);

                if (statistics.RecordCount > 0)
                {
                    // 更新统计显示
                    BpRecordCountText.Text = statistics.RecordCount.ToString();
                    BpAverageText.Text = statistics.AverageBloodPressureText;
                    BpMaxText.Text = statistics.MaxBloodPressureText;
                    BpAbnormalCountText.Text = statistics.AbnormalCount.ToString();

                    // 更新副标题
                    if (statistics.HasAbnormalRecords)
                    {
                        BloodPressureStatsSubtitle.Text = $"已录入 {statistics.RecordCount} 次，{statistics.AbnormalCount} 次异常";
                        BloodPressureStatsSubtitle.Foreground = new SolidColorBrush(Color.FromRgb(220, 53, 69));
                    }
                    else
                    {
                        BloodPressureStatsSubtitle.Text = $"已录入 {statistics.RecordCount} 次，血压正常";
                        BloodPressureStatsSubtitle.Foreground = new SolidColorBrush(Color.FromRgb(40, 167, 69));
                    }

                    // 根据是否有异常记录调整平均血压颜色
                    if (statistics.HasAbnormalRecords)
                    {
                        BpAverageText.Foreground = new SolidColorBrush(Color.FromRgb(255, 193, 7)); // 黄色警告
                    }
                    else
                    {
                        BpAverageText.Foreground = new SolidColorBrush(Color.FromRgb(40, 167, 69)); // 绿色正常
                    }
                }
                else
                {
                    // 没有数据时的默认显示
                    BpRecordCountText.Text = "0";
                    BpAverageText.Text = "--/--";
                    BpMaxText.Text = "--/--";
                    BpAbnormalCountText.Text = "0";
                    BloodPressureStatsSubtitle.Text = "今日暂无血压数据";
                    BloodPressureStatsSubtitle.Foreground = new SolidColorBrush(Color.FromRgb(160, 160, 255));
                    BpAverageText.Foreground = new SolidColorBrush(Color.FromRgb(40, 167, 69));
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"加载血压统计数据失败: {ex.Message}");
                // 发生错误时显示默认值
                BpRecordCountText.Text = "0";
                BpAverageText.Text = "--/--";
                BpMaxText.Text = "--/--";
                BpAbnormalCountText.Text = "0";
                BloodPressureStatsSubtitle.Text = "加载失败";
                BloodPressureStatsSubtitle.Foreground = new SolidColorBrush(Color.FromRgb(220, 53, 69));
            }
        }

        /// <summary>
        /// 计算单日平均步数
        /// </summary>
        private async Task<int> CalculateDailyAverageSteps(int year, int month, int currentDay)
        {
            try
            {
                // 获取本月所有健康数据
                var allData = await _healthService.GetHealthDataAsync();

                // 筛选本月截止到昨天的数据
                var monthlyData = allData.Where(h =>
                    h.Date.Year == year &&
                    h.Date.Month == month &&
                    h.Date.Day < currentDay).ToList(); // 截止到昨天

                if (monthlyData.Count == 0)
                {
                    return 0; // 如果没有历史数据，返回0
                }

                // 计算总步数和平均值
                var totalSteps = monthlyData.Sum(h => h.Steps);
                var averageSteps = (int)Math.Round((double)totalSteps / monthlyData.Count);

                return averageSteps;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"计算单日平均步数失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 更新目标进度提示
        /// </summary>
        private void UpdateTargetProgress(int monthlySteps, DateTime currentDate)
        {
            try
            {
                const int TARGET_STEPS = 280000; // 28万步目标

                // 计算本月总天数
                var daysInMonth = DateTime.DaysInMonth(currentDate.Year, currentDate.Month);

                // 计算已过去的天数（包括今天）
                var daysPassed = currentDate.Day;

                // 计算剩余天数
                var remainingDays = daysInMonth - daysPassed;

                // 计算目标完成度
                var progressPercentage = Math.Min(100, (double)monthlySteps / TARGET_STEPS * 100);

                // 计算每日还需步数
                var remainingSteps = Math.Max(0, TARGET_STEPS - monthlySteps);
                var dailyNeededSteps = remainingDays > 0 ? (int)Math.Ceiling((double)remainingSteps / remainingDays) : 0;

                // 更新UI
                Dispatcher.Invoke(() =>
                {
                    // 更新每日需要步数
                    DailyTargetStepsText.Text = dailyNeededSteps.ToString("N0");

                    // 更新进度百分比
                    TargetProgressText.Text = $"{progressPercentage:F1}%";

                    // 更新剩余天数
                    RemainingDaysText.Text = remainingDays > 0 ? $"本月剩余{remainingDays}天" : "本月已结束";

                    // 更新进度条
                    var progressBarWidth = 200 * (progressPercentage / 100);
                    ProgressBarFill.Width = progressBarWidth;

                    // 根据进度调整进度条颜色
                    var progressBrush = progressPercentage >= 100 ?
                        new SolidColorBrush((Color)ColorConverter.ConvertFromString("#4CAF50")) : // 绿色 - 已完成
                        progressPercentage >= 75 ?
                        new SolidColorBrush((Color)ColorConverter.ConvertFromString("#5D6EFF")) : // 蓝色 - 进展良好
                        progressPercentage >= 50 ?
                        new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF9800")) : // 橙色 - 需要努力
                        new SolidColorBrush((Color)ColorConverter.ConvertFromString("#FF5252"));   // 红色 - 进度落后

                    ProgressBarFill.Background = progressBrush;

                    // 更新发光效果颜色
                    if (ProgressBarFill.Effect is DropShadowEffect shadowEffect)
                    {
                        shadowEffect.Color = ((SolidColorBrush)progressBrush).Color;
                    }
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"更新目标进度失败: {ex.Message}");
            }
        }

        #endregion

        #region 地图功能

        /// <summary>
        /// 初始化地图
        /// </summary>
        private async void InitializeMap()
        {
            try
            {
                await MapWebView.EnsureCoreWebView2Async();
                _isMapInitialized = true;

                // 创建高德地图HTML页面
                var mapHtml = CreateMapHtml();
                MapWebView.NavigateToString(mapHtml);

                MapStatusText.Text = "地图加载完成，请选择KML文件";
            }
            catch (Exception ex)
            {
                MapStatusText.Text = $"地图初始化失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 创建高德地图HTML页面
        /// </summary>
        private string CreateMapHtml()
        {
            return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>高德地图KML轨迹显示</title>
    <script>
        window._AMapSecurityConfig = {{
            securityJSCode: '{AmapSecurityKey}'
        }};
    </script>
    <script src='https://webapi.amap.com/maps?v=1.4.15&key={AmapApiKey}&plugin=AMap.Scale,AMap.ToolBar,AMap.TileLayer.Satellite,AMap.TileLayer.RoadNet,AMap.PlaceSearch'></script>
    <style>
        body, html {{
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            font-family: 'Microsoft YaHei', sans-serif;
        }}
        #mapContainer {{
            width: 100%;
            height: 100vh;
        }}
        .info-panel {{
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 250px;
            z-index: 1000;
        }}
        .info-title {{
            font-weight: bold;
            margin-bottom: 5px;
            color: #333;
        }}
        .info-content {{
            font-size: 12px;
            color: #666;
        }}
    </style>
</head>
<body>
    <div style='margin-bottom: 10px; padding: 10px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px;'>
        <div style='display: flex; align-items: center; gap: 15px; flex-wrap: wrap;'>
            <!-- 图层选择 -->
            <div style='display: flex; align-items: center;'>
                <label style='margin-right: 10px; font-weight: bold; color: #495057;'>地图图层:</label>
                <select id='layerSelector' style='padding: 5px 10px; border: 1px solid #ced4da; border-radius: 4px; background: white;'>
                    <option value='standard'>标准图层</option>
                    <option value='satellite'>卫星图层</option>
                    <option value='3d'>3D图层</option>
                </select>
            </div>

            <!-- 地名搜索 -->
            <div style='display: flex; align-items: center;'>
                <label style='margin-right: 10px; font-weight: bold; color: #495057;'>地名搜索:</label>
                <input type='text' id='placeSearchInput' placeholder='输入地名进行搜索...' style='padding: 5px 10px; border: 1px solid #ced4da; border-radius: 4px; width: 200px; background: white;' />
                <button id='searchBtn' style='padding: 5px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; margin-left: 5px;'>搜索</button>
                <button id='clearSearchBtn' style='padding: 5px 10px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; margin-left: 5px;'>清除</button>
            </div>

            <!-- 操作按钮 -->
            <div style='display: flex; align-items: center;'>
                <button id='clearTrackBtn' style='padding: 5px 15px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;'>清除轨迹</button>
            </div>
        </div>

        <!-- 搜索结果显示区域 -->
        <div id='searchResults' style='display: none; margin-top: 10px; padding: 8px; background: white; border: 1px solid #dee2e6; border-radius: 4px; max-height: 150px; overflow-y: auto;'>
            <div style='font-weight: bold; margin-bottom: 5px; color: #495057;'>搜索结果:</div>
            <div id='searchResultsList'></div>
        </div>
    </div>
    <div id='mapContainer'></div>
    <div class='info-panel' id='infoPanel' style='display: none;'>
        <div class='info-title'>轨迹信息</div>
        <div class='info-content' id='infoContent'>暂无轨迹数据</div>
    </div>

    <script>
        let map;
        let trackPolylines = []; // 存储轨迹线
        let trackMarkers = []; // 存储标记点
        let searchMarkers = []; // 存储搜索标记
        let searchInfoWindows = []; // 存储搜索信息窗体

        // 初始化地图
        function initMap() {{
            console.log('开始初始化地图...');
            console.log('AMap对象状态:', typeof AMap);

            // 确保AMap已加载
            if (typeof AMap === 'undefined') {{
                console.error('AMap未加载');
                alert('高德地图API未加载，请检查网络连接');
                return;
            }}

            try {{
                console.log('创建地图实例...');
                map = new AMap.Map('mapContainer', {{
                    zoom: 10,
                    center: [116.397428, 39.90923], // 北京天安门
                    mapStyle: 'amap://styles/normal',
                    viewMode: '2D',
                    pitch: 0,
                    features: ['bg', 'road', 'building', 'point']
                }});

                console.log('地图实例创建成功:', map);

                // 添加地图控件
                try {{
                    // 检查控件是否可用
                    if (AMap.Scale) {{
                        map.addControl(new AMap.Scale());
                        console.log('比例尺控件添加成功');
                    }} else {{
                        console.warn('AMap.Scale控件不可用');
                    }}

                    if (AMap.ToolBar) {{
                        map.addControl(new AMap.ToolBar());
                        console.log('工具栏控件添加成功');
                    }} else {{
                        console.warn('AMap.ToolBar控件不可用');
                    }}
                }} catch (controlError) {{
                    console.warn('添加地图控件时出错:', controlError);
                    // 继续执行，不让控件错误阻止地图初始化
                }}

                // 初始化图层切换功能
                initLayerSelector();

                // 初始化清除轨迹按钮
                initClearTrackButton();

                // 初始化地名搜索功能
                initPlaceSearch();

                console.log('地图初始化完成');
            }} catch (error) {{
                console.error('地图初始化失败:', error);
                alert('地图初始化失败: ' + error.message);
            }}
        }}

        // 初始化图层选择器
        function initLayerSelector() {{
            const layerSelector = document.getElementById('layerSelector');
            if (layerSelector) {{
                layerSelector.addEventListener('change', function() {{
                    const selectedLayer = this.value;
                    switchMapLayer(selectedLayer);
                }});
            }}
        }}

        // 切换地图图层
        function switchMapLayer(layerType) {{
            if (!map) {{
                console.error('地图对象未初始化');
                return;
            }}

            console.log('开始切换图层:', layerType);
            console.log('当前地图对象:', map);
            console.log('AMap对象:', typeof AMap);

            try {{
                switch(layerType) {{
                    case 'standard':
                        console.log('切换到标准图层...');
                        map.setMapStyle('amap://styles/normal');
                        if (map.setViewMode) {{
                            map.setViewMode('2D');
                        }}
                        if (map.setPitch) {{
                            map.setPitch(0);
                        }}
                        console.log('标准图层设置完成');
                        break;

                    case 'satellite':
                        console.log('切换到卫星图层...');
                        console.log('检查AMap.TileLayer:', typeof AMap.TileLayer);

                        // 清除现有图层
                        map.clearMap();

                        // 创建卫星图层
                        if (AMap.TileLayer && AMap.TileLayer.Satellite) {{
                            console.log('创建卫星图层');
                            const satelliteLayer = new AMap.TileLayer.Satellite();
                            const roadLayer = new AMap.TileLayer.RoadNet();

                            map.add([satelliteLayer, roadLayer]);
                            console.log('卫星图层已添加');
                        }} else {{
                            console.log('使用样式切换方法');
                            map.setMapStyle('amap://styles/satellite');
                        }}

                        // 确保是2D模式
                        if (map.setViewMode) {{
                            map.setViewMode('2D');
                        }}
                        if (map.setPitch) {{
                            map.setPitch(0);
                        }}
                        console.log('卫星图层设置完成');
                        break;

                    case '3d':
                        console.log('切换到3D图层...');
                        console.log('检查setViewMode方法:', typeof map.setViewMode);
                        console.log('检查setPitch方法:', typeof map.setPitch);

                        // 先重置为标准图层
                        map.setMapStyle('amap://styles/normal');

                        // 延迟切换到3D，确保样式加载完成
                        setTimeout(function() {{
                            try {{
                                if (map.setViewMode) {{
                                    console.log('设置3D视图模式');
                                    map.setViewMode('3D');

                                    // 再延迟设置倾斜角度
                                    setTimeout(function() {{
                                        if (map.setPitch) {{
                                            console.log('设置倾斜角度');
                                            map.setPitch(60);
                                            console.log('3D设置完成，当前倾斜角度:', map.getPitch ? map.getPitch() : '未知');
                                        }}
                                    }}, 200);
                                }} else {{
                                    console.error('setViewMode方法不存在，尝试其他方法');
                                    // 尝试直接设置3D样式
                                    map.setMapStyle('amap://styles/normal');
                                }}
                            }} catch (e) {{
                                console.error('3D切换错误:', e);
                            }}
                        }}, 100);

                        console.log('3D图层切换已启动');
                        break;

                    default:
                        console.warn('未知的图层类型:', layerType);
                }}

                // 强制刷新地图
                setTimeout(function() {{
                    if (map.getViewMode) {{
                        console.log('当前视图模式:', map.getViewMode());
                    }}
                    if (map.getPitch) {{
                        console.log('当前倾斜角度:', map.getPitch());
                    }}
                    if (map.getMapStyle) {{
                        console.log('当前地图样式:', map.getMapStyle());
                    }}
                }}, 100);

            }} catch (error) {{
                console.error('切换图层时发生错误:', error);
                console.error('错误堆栈:', error.stack);
            }}
        }}

        // 初始化清除轨迹按钮
        function initClearTrackButton() {{
            const clearBtn = document.getElementById('clearTrackBtn');
            if (clearBtn) {{
                clearBtn.addEventListener('click', function() {{
                    clearTrack();
                    hideTrackInfo();
                    console.log('手动清除轨迹');
                }});
            }}
        }}

        // 隐藏轨迹信息面板
        function hideTrackInfo() {{
            const infoPanel = document.getElementById('infoPanel');
            if (infoPanel) {{
                infoPanel.style.display = 'none';
            }}
        }}

        // 地名搜索功能（使用已声明的变量）

        // 初始化地名搜索功能
        function initPlaceSearch() {{
            console.log('初始化地名搜索功能');

            const searchBtn = document.getElementById('searchBtn');
            const clearSearchBtn = document.getElementById('clearSearchBtn');
            const searchInput = document.getElementById('placeSearchInput');

            console.log('搜索按钮:', searchBtn);
            console.log('清除按钮:', clearSearchBtn);
            console.log('搜索输入框:', searchInput);

            if (searchBtn) {{
                searchBtn.onclick = function() {{
                    console.log('搜索按钮被点击');
                    const keyword = searchInput ? searchInput.value.trim() : '';
                    console.log('搜索关键词:', keyword);
                    if (keyword) {{
                        searchPlace(keyword);
                    }} else {{
                        alert('请输入要搜索的地名');
                    }}
                }};
                console.log('搜索按钮事件绑定成功');
            }} else {{
                console.error('未找到搜索按钮');
            }}

            if (clearSearchBtn) {{
                clearSearchBtn.onclick = function() {{
                    console.log('清除按钮被点击');
                    clearSearchResults();
                }};
                console.log('清除按钮事件绑定成功');
            }} else {{
                console.error('未找到清除按钮');
            }}

            if (searchInput) {{
                searchInput.onkeypress = function(e) {{
                    if (e.key === 'Enter') {{
                        console.log('回车键被按下');
                        const keyword = this.value.trim();
                        if (keyword) {{
                            searchPlace(keyword);
                        }}
                    }}
                }};
                console.log('搜索输入框事件绑定成功');
            }} else {{
                console.error('未找到搜索输入框');
            }}

            // 测试API是否可用
            setTimeout(function() {{
                console.log('测试高德地图API状态...');
                console.log('AMap:', typeof AMap);
                if (typeof AMap !== 'undefined') {{
                    console.log('AMap.PlaceSearch:', typeof AMap.PlaceSearch);
                    console.log('AMap版本:', AMap.v || 'unknown');

                    // 测试搜索功能
                    if (AMap.PlaceSearch) {{
                        console.log('PlaceSearch插件可用');
                        // 测试API连接
                        testApiConnection();
                    }} else {{
                        console.warn('PlaceSearch插件不可用，尝试动态加载...');
                        // 尝试动态加载PlaceSearch插件
                        AMap.plugin('AMap.PlaceSearch', function() {{
                            console.log('PlaceSearch插件动态加载成功');
                            testApiConnection();
                        }});
                    }}
                }} else {{
                    console.error('AMap未加载');
                }}
            }}, 1000);
        }}

        // 测试API连接
        function testApiConnection() {{
            console.log('测试API连接...');

            // 检查API密钥是否为默认值
            if ('{AmapApiKey}' === 'your_amap_api_key_here') {{
                showApiKeySetupDialog();
                return;
            }}

            try {{
                const testSearch = new AMap.PlaceSearch({{
                    pageSize: 1,
                    pageIndex: 1,
                    city: '北京',
                    citylimit: true
                }});

                testSearch.search('天安门', function(status, result) {{
                    console.log('API测试结果 - 状态:', status);
                    console.log('API测试结果 - 数据:', result);

                    if (status === 'complete') {{
                        console.log('✅ API连接正常');
                        // 显示成功提示
                        showApiStatus('API连接正常', 'success');
                    }} else if (status === 'error') {{
                        console.error('❌ API连接失败:', result);
                        if (result && result.info) {{
                            console.error('错误详情:', result.info);
                            showApiStatus('API错误: ' + result.info, 'error');

                            // 如果是密钥相关错误，显示设置对话框
                            if (result.info.includes('INVALID_USER_KEY') ||
                                result.info.includes('USER_KEY_PLAT_NOMATCH') ||
                                result.info.includes('INSUFFICIENT_PRIVILEGES')) {{
                                setTimeout(showApiKeySetupDialog, 2000);
                            }}
                        }} else {{
                            showApiStatus('API连接失败', 'error');
                        }}
                    }} else {{
                        console.warn('⚠️ API状态异常:', status);
                        showApiStatus('API状态异常: ' + status, 'warning');
                    }}
                }});
            }} catch (error) {{
                console.error('❌ API测试异常:', error);
                showApiStatus('API测试异常: ' + error.message, 'error');
            }}
        }}

        // 显示API密钥设置对话框
        function showApiKeySetupDialog() {{
            var message = '高德地图API密钥配置说明:' + String.fromCharCode(10) + String.fromCharCode(10) +
                         '当前使用的是示例API密钥，无法正常使用地图功能。' + String.fromCharCode(10) + String.fromCharCode(10) +
                         '请按照以下步骤申请并配置您的API密钥：' + String.fromCharCode(10) +
                         '1. 访问高德开放平台 (https://lbs.amap.com/)' + String.fromCharCode(10) +
                         '2. 注册并登录账号' + String.fromCharCode(10) +
                         '3. 进入控制台，创建新应用' + String.fromCharCode(10) +
                         '4. 添加Key，选择Web端(JS API)' + String.fromCharCode(10) +
                         '5. 将获得的Key和安全密钥配置到代码中' + String.fromCharCode(10) + String.fromCharCode(10) +
                         '配置位置：MainWindow.xaml.cs 文件中的 AmapApiKey 和 AmapSecurityKey 常量';
            alert(message);
        }}

        // 显示API状态
        function showApiStatus(message, type) {{
            // 创建状态显示元素
            let statusDiv = document.getElementById('apiStatus');
            if (!statusDiv) {{
                statusDiv = document.createElement('div');
                statusDiv.id = 'apiStatus';
                statusDiv.style.cssText = 'position: fixed; top: 10px; left: 10px; padding: 10px; border-radius: 5px; z-index: 2000; font-size: 12px; max-width: 300px;';
                document.body.appendChild(statusDiv);
            }}

            // 设置样式和内容
            statusDiv.textContent = message;
            if (type === 'success') {{
                statusDiv.style.backgroundColor = '#d4edda';
                statusDiv.style.color = '#155724';
                statusDiv.style.border = '1px solid #c3e6cb';
            }} else if (type === 'error') {{
                statusDiv.style.backgroundColor = '#f8d7da';
                statusDiv.style.color = '#721c24';
                statusDiv.style.border = '1px solid #f5c6cb';
            }} else if (type === 'warning') {{
                statusDiv.style.backgroundColor = '#fff3cd';
                statusDiv.style.color = '#856404';
                statusDiv.style.border = '1px solid #ffeaa7';
            }}

            // 5秒后自动隐藏
            setTimeout(function() {{
                if (statusDiv && statusDiv.parentNode) {{
                    statusDiv.parentNode.removeChild(statusDiv);
                }}
            }}, 5000);
        }}

        // 搜索地名
        function searchPlace(keyword) {{
            console.log('搜索地名:', keyword);
            console.log('AMap对象:', typeof AMap);
            console.log('AMap.PlaceSearch:', typeof AMap.PlaceSearch);

            if (typeof AMap === 'undefined') {{
                console.error('AMap未加载');
                alert('高德地图API未加载，请刷新页面重试');
                return;
            }}

            // 确保PlaceSearch插件已加载
            function doSearch() {{
                try {{
                    const placeSearch = new AMap.PlaceSearch({{
                        pageSize: 10,
                        pageIndex: 1,
                        city: '全国',
                        citylimit: false,
                        extensions: 'all'
                    }});

                    console.log('PlaceSearch对象创建成功:', placeSearch);

                    placeSearch.search(keyword, function(status, result) {{
                        console.log('搜索状态:', status);
                        console.log('搜索结果:', result);

                        if (status === 'complete') {{
                            if (result.poiList && result.poiList.pois && result.poiList.pois.length > 0) {{
                                console.log('找到', result.poiList.pois.length, '个结果');
                                showSearchResults(result.poiList.pois);
                            }} else {{
                                console.log('搜索完成但无结果');
                                showSearchResults([]);
                                alert('未找到相关地名，请尝试其他关键词');
                            }}
                        }} else if (status === 'error') {{
                            console.error('搜索错误详情:', result);
                            let errorMsg = '搜索出错';
                            let errorCode = '';

                            if (result && result.info) {{
                                errorMsg += '：' + result.info;
                                errorCode = result.info;
                                console.log('错误信息:', result.info);
                            }}
                            if (result && result.message) {{
                                errorMsg += '，详情：' + result.message;
                                console.log('错误详情:', result.message);
                            }}

                            // 检查具体的错误类型
                            if (errorCode.includes('INVALID_USER_KEY')) {{
                                errorMsg = 'API密钥无效，请检查配置';
                                showApiStatus('API密钥无效', 'error');
                            }} else if (errorCode.includes('DAILY_QUERY_OVER_LIMIT')) {{
                                errorMsg = 'API调用次数超限，请稍后重试';
                                showApiStatus('API调用次数超限', 'error');
                            }} else if (errorCode.includes('ACCESS_TOO_FREQUENT')) {{
                                errorMsg = '请求过于频繁，请稍后重试';
                                showApiStatus('请求过于频繁', 'warning');
                            }} else if (errorCode.includes('INVALID_PARAMS')) {{
                                errorMsg = '搜索参数无效';
                                showApiStatus('搜索参数无效', 'error');
                            }} else if (errorCode.includes('INSUFFICIENT_PRIVILEGES')) {{
                                errorMsg = 'API权限不足，请检查服务配置';
                                showApiStatus('API权限不足', 'error');
                            }} else if (errorCode.includes('USER_KEY_PLAT_NOMATCH')) {{
                                errorMsg = 'API密钥平台不匹配';
                                showApiStatus('API密钥平台不匹配', 'error');
                            }} else {{
                                showApiStatus('搜索错误: ' + errorCode, 'error');
                            }}

                            showSearchResults([]);
                            alert(errorMsg);
                        }} else if (status === 'no_data') {{
                            console.log('无数据返回');
                            showSearchResults([]);
                            alert('未找到相关地名');
                        }} else {{
                            console.log('未知状态:', status);
                            showSearchResults([]);
                            alert('搜索失败，状态：' + status);
                        }}
                    }});
                }} catch (error) {{
                    console.error('创建PlaceSearch时出错:', error);
                    alert('搜索功能初始化失败：' + error.message);
                }}
            }}

            if (!AMap.PlaceSearch) {{
                console.log('PlaceSearch插件未加载，尝试动态加载...');
                AMap.plugin('AMap.PlaceSearch', function() {{
                    console.log('PlaceSearch插件动态加载成功');
                    doSearch();
                }});
            }} else {{
                doSearch();
            }}
        }}

        // 显示搜索结果
        function showSearchResults(pois) {{
            const resultsDiv = document.getElementById('searchResults');
            const listDiv = document.getElementById('searchResultsList');

            if (!resultsDiv || !listDiv) return;

            listDiv.innerHTML = '';

            if (pois.length === 0) {{
                resultsDiv.style.display = 'none';
                return;
            }}

            resultsDiv.style.display = 'block';

            for (let i = 0; i < pois.length; i++) {{
                const poi = pois[i];
                const item = document.createElement('div');
                item.style.cssText = 'padding:5px;margin:2px 0;background:#f8f9fa;border-radius:3px;cursor:pointer;border:1px solid transparent;';

                const name = poi.name || '未知地点';
                const address = poi.address || poi.district || '';
                const type = poi.type || '未知';

                const nameDiv = document.createElement('div');
                nameDiv.style.cssText = 'font-weight:bold;color:#007bff;';
                nameDiv.textContent = name;

                const addressDiv = document.createElement('div');
                addressDiv.style.cssText = 'font-size:12px;color:#6c757d;';
                addressDiv.textContent = address;

                const typeDiv = document.createElement('div');
                typeDiv.style.cssText = 'font-size:11px;color:#868e96;';
                typeDiv.textContent = '类型: ' + type;

                item.appendChild(nameDiv);
                item.appendChild(addressDiv);
                item.appendChild(typeDiv);

                item.onmouseenter = function() {{
                    this.style.backgroundColor = '#e9ecef';
                    this.style.borderColor = '#007bff';
                }};

                item.onmouseleave = function() {{
                    this.style.backgroundColor = '#f8f9fa';
                    this.style.borderColor = 'transparent';
                }};

                item.onclick = function() {{
                    locateToPlace(poi);
                }};

                listDiv.appendChild(item);
            }}
        }}

        // 定位到地点
        function locateToPlace(poi) {{
            console.log('定位到:', poi);

            if (!poi.location) {{
                alert('该地点坐标信息缺失');
                return;
            }}

            const lng = poi.location.lng || poi.location.getLng();
            const lat = poi.location.lat || poi.location.getLat();

            map.setCenter([lng, lat]);
            map.setZoom(15);

            clearSearchMarkers();

            const marker = new AMap.Marker({{
                position: [lng, lat],
                title: poi.name,
                icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_bs.png'
            }});

            map.add(marker);
            searchMarkers.push(marker);

            const content = document.createElement('div');
            content.style.cssText = 'padding:10px;min-width:200px;';

            const titleDiv = document.createElement('div');
            titleDiv.style.cssText = 'font-weight:bold;font-size:14px;color:#333;margin-bottom:5px;';
            titleDiv.textContent = poi.name;

            const addressDiv = document.createElement('div');
            addressDiv.style.cssText = 'font-size:12px;color:#666;margin-bottom:3px;';
            addressDiv.textContent = '地址: ' + (poi.address || '未知');

            const typeDiv = document.createElement('div');
            typeDiv.style.cssText = 'font-size:12px;color:#666;margin-bottom:3px;';
            typeDiv.textContent = '类型: ' + (poi.type || '未知');

            const coordDiv = document.createElement('div');
            coordDiv.style.cssText = 'font-size:12px;color:#666;';
            coordDiv.textContent = '坐标: ' + lng.toFixed(6) + ', ' + lat.toFixed(6);

            content.appendChild(titleDiv);
            content.appendChild(addressDiv);
            content.appendChild(typeDiv);
            content.appendChild(coordDiv);

            const infoWindow = new AMap.InfoWindow({{
                content: content,
                offset: new AMap.Pixel(0, -30)
            }});

            infoWindow.open(map, [lng, lat]);
            searchInfoWindows.push(infoWindow);

            document.getElementById('searchResults').style.display = 'none';
        }}

        // 清除搜索结果
        function clearSearchResults() {{
            const searchInput = document.getElementById('placeSearchInput');
            if (searchInput) searchInput.value = '';

            const resultsDiv = document.getElementById('searchResults');
            if (resultsDiv) resultsDiv.style.display = 'none';

            clearSearchMarkers();
        }}

        // 清除搜索标记
        function clearSearchMarkers() {{
            if (searchMarkers.length > 0) {{
                for (let i = 0; i < searchMarkers.length; i++) {{
                    map.remove(searchMarkers[i]);
                }}
                searchMarkers = [];
            }}

            if (searchInfoWindows.length > 0) {{
                for (let i = 0; i < searchInfoWindows.length; i++) {{
                    searchInfoWindows[i].close();
                }}
                searchInfoWindows = [];
            }}
        }}

        // 加载KML数据 - 手动解析方式
        function loadKML(kmlContent) {{
            try {{
                // 确保AMap已加载
                if (typeof AMap === 'undefined') {{
                    console.error('AMap未加载');
                    alert('地图API未加载完成，请稍后重试');
                    return;
                }}

                // 清除之前的轨迹
                clearTrack();

                // 解析KML内容
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(kmlContent, 'text/xml');

                // 检查解析是否成功
                if (xmlDoc.getElementsByTagName('parsererror').length > 0) {{
                    throw new Error('KML文件格式错误');
                }}

                // 提取坐标数据
                const coordinates = extractCoordinatesFromKML(xmlDoc);

                if (coordinates.length === 0) {{
                    alert('KML文件中没有找到有效的轨迹数据');
                    return;
                }}

                // 绘制轨迹
                drawTrackOnMap(coordinates);

                // 显示轨迹信息
                showTrackInfo(coordinates);

                console.log('KML加载完成，共找到', coordinates.length, '个轨迹点');

            }} catch (error) {{
                console.error('加载KML时发生错误:', error);
                alert('加载KML时发生错误: ' + error.message);
            }}
        }}

        // 从KML文档中提取坐标
        function extractCoordinatesFromKML(xmlDoc) {{
            const coordinates = [];

            // 查找所有的coordinates元素
            const coordElements = xmlDoc.getElementsByTagName('coordinates');

            for (let i = 0; i < coordElements.length; i++) {{
                const coordText = coordElements[i].textContent.trim();
                if (coordText) {{
                    // 解析坐标字符串
                    const points = parseCoordinateString(coordText);
                    if (points.length > 0) {{
                        coordinates.push(...points);
                    }}
                }}
            }}

            return coordinates;
        }}

        // 解析坐标字符串
        function parseCoordinateString(coordText) {{
            const points = [];
            const lines = coordText.split(/\\s+/);

            for (let line of lines) {{
                line = line.trim();
                if (line) {{
                    const parts = line.split(',');
                    if (parts.length >= 2) {{
                        const lng = parseFloat(parts[0]);
                        const lat = parseFloat(parts[1]);

                        if (!isNaN(lng) && !isNaN(lat)) {{
                            points.push([lng, lat]);
                        }}
                    }}
                }}
            }}

            return points;
        }}

        // 在地图上绘制轨迹
        function drawTrackOnMap(coordinates) {{
            if (coordinates.length === 0) return;

            // 分析轨迹数据，识别连续的轨迹段
            const trackSegments = analyzeTrackSegments(coordinates);

            // 为每个轨迹段创建单独的轨迹线
            trackSegments.forEach(function(segment, index) {{
                if (segment.length > 1) {{
                    const polyline = new AMap.Polyline({{
                        path: segment,
                        strokeColor: '#FF5722',
                        strokeWeight: 4,
                        strokeOpacity: 0.8,
                        strokeStyle: 'solid'
                    }});

                    map.add(polyline);
                    trackPolylines.push(polyline);
                }}
            }});

            // 添加起点标记（第一个轨迹段的起点）
            if (trackSegments.length > 0 && trackSegments[0].length > 0) {{
                const startMarker = new AMap.Marker({{
                    position: trackSegments[0][0],
                    icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',
                    title: '起点'
                }});

                map.add(startMarker);
                trackMarkers.push(startMarker);
            }}

            // 添加终点标记（最后一个轨迹段的终点）
            if (trackSegments.length > 0) {{
                const lastSegment = trackSegments[trackSegments.length - 1];
                if (lastSegment.length > 0) {{
                    const endMarker = new AMap.Marker({{
                        position: lastSegment[lastSegment.length - 1],
                        icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png',
                        title: '终点'
                    }});

                    map.add(endMarker);
                    trackMarkers.push(endMarker);
                }}
            }}

            // 自动调整地图视野
            map.setFitView(trackPolylines.concat(trackMarkers));
        }}

        // 分析轨迹段，避免起终点直线连接
        function analyzeTrackSegments(coordinates) {{
            if (coordinates.length <= 1) return [coordinates];

            const segments = [];
            let currentSegment = [coordinates[0]];
            const maxDistance = 1000; // 最大连续距离（米）

            for (let i = 1; i < coordinates.length; i++) {{
                const distance = AMap.GeometryUtil.distance(
                    coordinates[i - 1],
                    coordinates[i]
                );

                // 如果距离过大，认为是新的轨迹段
                if (distance > maxDistance) {{
                    // 保存当前轨迹段
                    if (currentSegment.length > 1) {{
                        segments.push(currentSegment);
                    }}
                    // 开始新的轨迹段
                    currentSegment = [coordinates[i]];
                }} else {{
                    // 继续当前轨迹段
                    currentSegment.push(coordinates[i]);
                }}
            }}

            // 添加最后一个轨迹段
            if (currentSegment.length > 1) {{
                segments.push(currentSegment);
            }}

            // 如果没有找到合适的轨迹段，返回原始数据作为单个段
            return segments.length > 0 ? segments : [coordinates];
        }}

        // 显示轨迹信息
        function showTrackInfo(coordinates) {{
            const infoPanel = document.getElementById('infoPanel');
            const infoContent = document.getElementById('infoContent');

            const pointCount = coordinates.length;
            const trackCount = trackPolylines.length;

            // 计算轨迹总长度（只计算实际绘制的轨迹段）
            let totalDistance = 0;
            trackPolylines.forEach(function(polyline) {{
                const path = polyline.getPath();
                for (let i = 1; i < path.length; i++) {{
                    const distance = AMap.GeometryUtil.distance(path[i-1], path[i]);
                    totalDistance += distance;
                }}
            }});

            infoContent.innerHTML =
                '<div>轨迹段数: ' + trackCount + '</div>' +
                '<div>轨迹点数: ' + pointCount + '</div>' +
                '<div>有效长度: ' + (totalDistance / 1000).toFixed(2) + ' km</div>' +
                '<div>加载时间: ' + new Date().toLocaleTimeString() + '</div>';

            infoPanel.style.display = 'block';
        }}

        // 清除轨迹
        function clearTrack() {{
            try {{
                // 清除轨迹线
                if (trackPolylines.length > 0) {{
                    map.remove(trackPolylines);
                    trackPolylines = [];
                }}

                // 清除标记点
                if (trackMarkers.length > 0) {{
                    map.remove(trackMarkers);
                    trackMarkers = [];
                }}

                const infoPanel = document.getElementById('infoPanel');
                if (infoPanel) {{
                    infoPanel.style.display = 'none';
                }}

                console.log('轨迹已清除');
            }} catch (error) {{
                console.error('清除轨迹时发生错误:', error);
            }}
        }}

        // 页面加载完成后初始化地图
        window.onload = function() {{
            console.log('页面加载完成，开始初始化地图...');
            console.log('当前AMap状态:', typeof AMap);

            // 等待AMap完全加载
            if (typeof AMap !== 'undefined') {{
                console.log('AMap已加载，立即初始化地图');
                initMap();
            }} else {{
                console.log('AMap未加载，等待2秒后重试...');
                // 如果AMap还未加载，等待一段时间后重试
                setTimeout(function() {{
                    console.log('重试检查AMap状态:', typeof AMap);
                    if (typeof AMap !== 'undefined') {{
                        console.log('AMap加载成功，开始初始化地图');
                        initMap();
                    }} else {{
                        console.error('AMap加载超时');
                        alert('地图加载失败，请检查网络连接或刷新页面重试');
                    }}
                }}, 2000);
            }}
        }};

        // 暴露函数给外部调用
        window.loadKML = loadKML;
        window.clearTrack = clearTrack;
    </script>
</body>
</html>";
        }

        /// <summary>
        /// WebView2核心初始化完成事件
        /// </summary>
        private void MapWebView_CoreWebView2InitializationCompleted(object sender, CoreWebView2InitializationCompletedEventArgs e)
        {
            if (e.IsSuccess)
            {
                MapStatusText.Text = "WebView2初始化成功";
            }
            else
            {
                MapStatusText.Text = $"WebView2初始化失败: {e.InitializationException?.Message}";
            }
        }

        /// <summary>
        /// WebView2导航完成事件
        /// </summary>
        private void MapWebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
        {
            if (e.IsSuccess)
            {
                MapStatusText.Text = "地图加载完成，请选择KML文件";
            }
            else
            {
                MapStatusText.Text = "地图加载失败";
            }
        }

        /// <summary>
        /// 选择KML文件按钮点击事件
        /// </summary>
        private async void SelectKmlButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "选择KML文件",
                    Filter = "KML文件 (*.kml)|*.kml|所有文件 (*.*)|*.*",
                    FilterIndex = 1
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    var filePath = openFileDialog.FileName;
                    var fileName = Path.GetFileName(filePath);

                    MapStatusText.Text = "正在加载KML文件...";
                    KmlFileNameText.Text = fileName;

                    // 读取KML文件内容
                    var kmlContent = await File.ReadAllTextAsync(filePath);
                    _currentKmlContent = kmlContent;

                    // 验证KML格式
                    if (!IsValidKml(kmlContent))
                    {
                        MapStatusText.Text = "无效的KML文件格式";
                        KmlFileNameText.Text = "";
                        return;
                    }

                    // 在地图中加载KML
                    if (_isMapInitialized && MapWebView.CoreWebView2 != null)
                    {
                        var escapedKml = kmlContent.Replace("'", "\\'").Replace("\r\n", "\\n").Replace("\n", "\\n");
                        await MapWebView.CoreWebView2.ExecuteScriptAsync($"loadKML('{escapedKml}')");

                        MapStatusText.Text = $"KML文件加载成功: {fileName}";
                    }
                    else
                    {
                        MapStatusText.Text = "地图未初始化，请稍后重试";
                    }
                }
            }
            catch (Exception ex)
            {
                MapStatusText.Text = $"加载KML文件失败: {ex.Message}";
                KmlFileNameText.Text = "";
                MessageBox.Show($"加载KML文件时发生错误:\n{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 清除轨迹按钮点击事件
        /// </summary>
        private async void ClearMapButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_isMapInitialized && MapWebView.CoreWebView2 != null)
                {
                    await MapWebView.CoreWebView2.ExecuteScriptAsync("clearTrack()");
                    MapStatusText.Text = "轨迹已清除";
                    KmlFileNameText.Text = "";
                    _currentKmlContent = string.Empty;
                }
                else
                {
                    MapStatusText.Text = "地图未初始化";
                }
            }
            catch (Exception ex)
            {
                MapStatusText.Text = $"清除轨迹失败: {ex.Message}";
                MessageBox.Show($"清除轨迹时发生错误:\n{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 验证KML文件格式
        /// </summary>
        private bool IsValidKml(string kmlContent)
        {
            try
            {
                var doc = XDocument.Parse(kmlContent);
                var kmlNamespace = XNamespace.Get("http://www.opengis.net/kml/2.2");

                // 检查是否包含kml根元素
                return doc.Root?.Name.LocalName.ToLower() == "kml" ||
                       doc.Root?.Name == kmlNamespace + "kml";
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region 新闻推送相关方法

        /// <summary>
        /// 处理新闻推送按钮点击事件
        /// </summary>
        private async void NewsButton_Click(object sender, RoutedEventArgs e)
        {
            // 切换到新闻视图
            SwitchView(NewsContent);

            // 默认显示国际频道
            SwitchToInternationalTab();

            // 如果还没有加载过新闻，则自动加载
            if (NewsItemsControl.ItemsSource == null)
            {
                await LoadNewsData();
            }
        }

        /// <summary>
        /// 处理刷新新闻按钮点击事件
        /// </summary>
        private async void RefreshNewsButton_Click(object sender, RoutedEventArgs e)
        {
            // 根据当前选中的tab刷新对应的数据
            if (InternationalContent.Visibility == Visibility.Visible)
            {
                await LoadNewsData();
            }
            else if (HistoryContent.Visibility == Visibility.Visible)
            {
                await LoadHistoryNewsData();
            }
            else if (MilanContent.Visibility == Visibility.Visible)
            {
                await LoadMilanNewsData();
            }
            else if (CultureContent.Visibility == Visibility.Visible)
            {
                await LoadCultureNewsData();
            }
            else if (WorldViewContent.Visibility == Visibility.Visible)
            {
                await LoadWorldViewNewsData();
            }
            else if (PrivateHistoryContent.Visibility == Visibility.Visible)
            {
                await LoadPrivateHistoryNewsData();
            }
        }

        /// <summary>
        /// 国际频道Tab点击事件
        /// </summary>
        private async void InternationalTab_Click(object sender, RoutedEventArgs e)
        {
            SwitchToInternationalTab();

            // 如果还没有加载过国际新闻，则自动加载
            if (NewsItemsControl.ItemsSource == null)
            {
                await LoadNewsData();
            }
        }

        /// <summary>
        /// 历史频道Tab点击事件
        /// </summary>
        private async void HistoryTab_Click(object sender, RoutedEventArgs e)
        {
            SwitchToHistoryTab();

            // 如果还没有加载过历史新闻，则自动加载
            if (HistoryNewsItemsControl.ItemsSource == null)
            {
                await LoadHistoryNewsData();
            }
        }

        /// <summary>
        /// 米兰频道Tab点击事件
        /// </summary>
        private async void MilanTab_Click(object sender, RoutedEventArgs e)
        {
            SwitchToMilanTab();

            // 如果还没有加载过米兰新闻，则自动加载
            if (MilanNewsItemsControl.ItemsSource == null)
            {
                await LoadMilanNewsData();
            }
        }

        /// <summary>
        /// 文化频道Tab点击事件
        /// </summary>
        private async void CultureTab_Click(object sender, RoutedEventArgs e)
        {
            SwitchToCultureTab();

            // 如果还没有加载过文化新闻数据，则自动加载
            if (CultureNewsItemsControl.ItemsSource == null)
            {
                await LoadCultureNewsData();
            }
        }

        /// <summary>
        /// 澎湃世界观Tab点击事件
        /// </summary>
        private async void WorldViewTab_Click(object sender, RoutedEventArgs e)
        {
            SwitchToWorldViewTab();

            // 如果还没有加载过澎湃世界观新闻数据，则自动加载
            if (WorldViewNewsItemsControl.ItemsSource == null)
            {
                await LoadWorldViewNewsData();
            }
        }

        /// <summary>
        /// 私家历史Tab点击事件
        /// </summary>
        private async void PrivateHistoryTab_Click(object sender, RoutedEventArgs e)
        {
            SwitchToPrivateHistoryTab();

            // 如果还没有加载过私家历史新闻数据，则自动加载
            if (PrivateHistoryNewsItemsControl.ItemsSource == null)
            {
                await LoadPrivateHistoryNewsData();
            }
        }

        /// <summary>
        /// 切换到国际频道
        /// </summary>
        private void SwitchToInternationalTab()
        {
            // 更新Tab按钮样式
            InternationalTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#5D6EFF"));
            HistoryTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            MilanTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            CultureTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            WorldViewTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            PrivateHistoryTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));

            // 切换内容显示
            InternationalContent.Visibility = Visibility.Visible;
            HistoryContent.Visibility = Visibility.Collapsed;
            MilanContent.Visibility = Visibility.Collapsed;
            CultureContent.Visibility = Visibility.Collapsed;
            WorldViewContent.Visibility = Visibility.Collapsed;
            PrivateHistoryContent.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// 切换到历史频道
        /// </summary>
        private void SwitchToHistoryTab()
        {
            // 更新Tab按钮样式
            InternationalTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            HistoryTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#5D6EFF"));
            MilanTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            CultureTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            WorldViewTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            PrivateHistoryTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));

            // 切换内容显示
            InternationalContent.Visibility = Visibility.Collapsed;
            HistoryContent.Visibility = Visibility.Visible;
            MilanContent.Visibility = Visibility.Collapsed;
            CultureContent.Visibility = Visibility.Collapsed;
            WorldViewContent.Visibility = Visibility.Collapsed;
            PrivateHistoryContent.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// 切换到米兰频道
        /// </summary>
        private void SwitchToMilanTab()
        {
            // 更新Tab按钮样式
            InternationalTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            HistoryTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            MilanTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#5D6EFF"));
            CultureTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            WorldViewTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            PrivateHistoryTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));

            // 切换内容显示
            InternationalContent.Visibility = Visibility.Collapsed;
            HistoryContent.Visibility = Visibility.Collapsed;
            MilanContent.Visibility = Visibility.Visible;
            CultureContent.Visibility = Visibility.Collapsed;
            WorldViewContent.Visibility = Visibility.Collapsed;
            PrivateHistoryContent.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// 切换到文化频道
        /// </summary>
        private void SwitchToCultureTab()
        {
            // 更新Tab按钮样式
            InternationalTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            HistoryTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            MilanTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            CultureTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#5D6EFF"));
            WorldViewTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            PrivateHistoryTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));

            // 切换内容显示
            InternationalContent.Visibility = Visibility.Collapsed;
            HistoryContent.Visibility = Visibility.Collapsed;
            MilanContent.Visibility = Visibility.Collapsed;
            CultureContent.Visibility = Visibility.Visible;
            WorldViewContent.Visibility = Visibility.Collapsed;
            PrivateHistoryContent.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// 切换到澎湃世界观频道
        /// </summary>
        private void SwitchToWorldViewTab()
        {
            // 更新Tab按钮样式
            InternationalTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            HistoryTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            MilanTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            CultureTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            WorldViewTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#5D6EFF"));
            PrivateHistoryTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));

            // 切换内容显示
            InternationalContent.Visibility = Visibility.Collapsed;
            HistoryContent.Visibility = Visibility.Collapsed;
            MilanContent.Visibility = Visibility.Collapsed;
            CultureContent.Visibility = Visibility.Collapsed;
            WorldViewContent.Visibility = Visibility.Visible;
            PrivateHistoryContent.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// 切换到私家历史频道
        /// </summary>
        private void SwitchToPrivateHistoryTab()
        {
            // 更新Tab按钮样式
            InternationalTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            HistoryTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            MilanTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            CultureTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            WorldViewTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#364375"));
            PrivateHistoryTabButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString("#5D6EFF"));

            // 切换内容显示
            InternationalContent.Visibility = Visibility.Collapsed;
            HistoryContent.Visibility = Visibility.Collapsed;
            MilanContent.Visibility = Visibility.Collapsed;
            CultureContent.Visibility = Visibility.Collapsed;
            WorldViewContent.Visibility = Visibility.Collapsed;
            PrivateHistoryContent.Visibility = Visibility.Visible;
        }

        /// <summary>
        /// 预加载新闻数据（后台静默加载）
        /// </summary>
        private async Task PreloadNewsData()
        {
            try
            {
                // 获取新闻数据
                var newsList = await _newsService.GetNewsAsync();

                // 更新UI（静默更新，不显示加载指示器）
                if (newsList != null && newsList.Count > 0)
                {
                    NewsItemsControl.ItemsSource = newsList;
                    NoNewsDataText.Visibility = Visibility.Collapsed;

                    // 在调试输出中记录成功预加载
                    System.Diagnostics.Debug.WriteLine($"成功预加载 {newsList.Count} 条新闻");
                }
                else
                {
                    NoNewsDataText.Text = "暂时无法获取新闻数据，请稍后重试";
                    NoNewsDataText.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                // 预加载失败时不显示错误消息，只在调试输出中记录
                System.Diagnostics.Debug.WriteLine($"预加载新闻失败: {ex.Message}");
                NoNewsDataText.Text = "加载新闻失败，请检查网络连接后重试";
                NoNewsDataText.Visibility = Visibility.Visible;
            }
        }

        /// <summary>
        /// 加载新闻数据
        /// </summary>
        private async Task LoadNewsData()
        {
            try
            {
                // 显示加载指示器
                NewsLoadingPanel.Visibility = Visibility.Visible;
                NoNewsDataText.Visibility = Visibility.Collapsed;

                // 获取新闻数据
                var newsList = await _newsService.GetNewsAsync();

                // 更新UI
                if (newsList != null && newsList.Count > 0)
                {
                    NewsItemsControl.ItemsSource = newsList;
                    NoNewsDataText.Visibility = Visibility.Collapsed;
                }
                else
                {
                    NewsItemsControl.ItemsSource = null;
                    NoNewsDataText.Text = "暂时无法获取新闻数据，请稍后重试";
                    NoNewsDataText.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载新闻数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                NoNewsDataText.Text = "加载新闻失败，请检查网络连接后重试";
                NoNewsDataText.Visibility = Visibility.Visible;
            }
            finally
            {
                // 隐藏加载指示器
                NewsLoadingPanel.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 加载历史新闻数据
        /// </summary>
        private async Task LoadHistoryNewsData()
        {
            try
            {
                // 显示加载指示器
                HistoryLoadingPanel.Visibility = Visibility.Visible;
                NoHistoryDataText.Visibility = Visibility.Collapsed;

                // 获取历史新闻数据
                var newsList = await _newsService.GetHistoryNewsAsync();

                // 更新UI
                if (newsList != null && newsList.Count > 0)
                {
                    HistoryNewsItemsControl.ItemsSource = newsList;
                    NoHistoryDataText.Visibility = Visibility.Collapsed;
                }
                else
                {
                    HistoryNewsItemsControl.ItemsSource = null;
                    NoHistoryDataText.Text = "暂时无法获取历史新闻数据，请稍后重试";
                    NoHistoryDataText.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载历史新闻数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                NoHistoryDataText.Text = "加载历史新闻失败，请检查网络连接后重试";
                NoHistoryDataText.Visibility = Visibility.Visible;
            }
            finally
            {
                // 隐藏加载指示器
                HistoryLoadingPanel.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 加载AC米兰新闻数据
        /// </summary>
        private async Task LoadMilanNewsData()
        {
            try
            {
                // 显示加载指示器
                MilanLoadingPanel.Visibility = Visibility.Visible;
                NoMilanDataText.Visibility = Visibility.Collapsed;

                // 获取AC米兰新闻数据
                var newsList = await _newsService.GetMilanNewsAsync();

                // 更新UI
                if (newsList != null && newsList.Count > 0)
                {
                    MilanNewsItemsControl.ItemsSource = newsList;
                    NoMilanDataText.Visibility = Visibility.Collapsed;
                }
                else
                {
                    MilanNewsItemsControl.ItemsSource = null;
                    NoMilanDataText.Text = "暂时无法获取AC米兰新闻数据，请稍后重试";
                    NoMilanDataText.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载AC米兰新闻数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                NoMilanDataText.Text = "加载AC米兰新闻失败，请检查网络连接后重试";
                NoMilanDataText.Visibility = Visibility.Visible;
            }
            finally
            {
                // 隐藏加载指示器
                MilanLoadingPanel.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 加载文化新闻数据
        /// </summary>
        private async Task LoadCultureNewsData()
        {
            try
            {
                // 显示加载指示器
                CultureLoadingPanel.Visibility = Visibility.Visible;
                NoCultureDataText.Visibility = Visibility.Collapsed;

                // 获取文化新闻数据
                var cultureNewsList = await _newsService.GetCultureNewsAsync();

                // 更新UI
                if (cultureNewsList != null && cultureNewsList.Count > 0)
                {
                    CultureNewsItemsControl.ItemsSource = cultureNewsList;
                    NoCultureDataText.Visibility = Visibility.Collapsed;
                }
                else
                {
                    CultureNewsItemsControl.ItemsSource = null;
                    NoCultureDataText.Text = "暂时无法获取文化新闻数据，请稍后重试";
                    NoCultureDataText.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载文化新闻数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                NoCultureDataText.Text = "加载文化新闻失败，请检查网络连接后重试";
                NoCultureDataText.Visibility = Visibility.Visible;
            }
            finally
            {
                // 隐藏加载指示器
                CultureLoadingPanel.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 加载澎湃世界观新闻数据
        /// </summary>
        private async Task LoadWorldViewNewsData()
        {
            try
            {
                // 显示加载指示器
                WorldViewLoadingPanel.Visibility = Visibility.Visible;
                NoWorldViewDataText.Visibility = Visibility.Collapsed;

                // 获取澎湃世界观新闻数据
                var worldViewNewsList = await _newsService.GetPengpaiWorldViewNewsAsync();

                // 更新UI
                if (worldViewNewsList != null && worldViewNewsList.Count > 0)
                {
                    WorldViewNewsItemsControl.ItemsSource = worldViewNewsList;
                    NoWorldViewDataText.Visibility = Visibility.Collapsed;
                }
                else
                {
                    WorldViewNewsItemsControl.ItemsSource = null;
                    NoWorldViewDataText.Text = "暂时无法获取澎湃世界观新闻数据，请稍后重试";
                    NoWorldViewDataText.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载澎湃世界观新闻数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                NoWorldViewDataText.Text = "加载澎湃世界观新闻失败，请检查网络连接后重试";
                NoWorldViewDataText.Visibility = Visibility.Visible;
            }
            finally
            {
                // 隐藏加载指示器
                WorldViewLoadingPanel.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 加载私家历史新闻数据
        /// </summary>
        private async Task LoadPrivateHistoryNewsData()
        {
            try
            {
                // 显示加载指示器
                PrivateHistoryLoadingPanel.Visibility = Visibility.Visible;
                NoPrivateHistoryDataText.Visibility = Visibility.Collapsed;

                // 获取私家历史新闻数据
                var privateHistoryNewsList = await _newsService.GetPrivateHistoryNewsAsync();

                // 更新UI
                if (privateHistoryNewsList != null && privateHistoryNewsList.Count > 0)
                {
                    PrivateHistoryNewsItemsControl.ItemsSource = privateHistoryNewsList;
                    NoPrivateHistoryDataText.Visibility = Visibility.Collapsed;
                }
                else
                {
                    PrivateHistoryNewsItemsControl.ItemsSource = null;
                    NoPrivateHistoryDataText.Text = "暂时无法获取私家历史新闻数据，请稍后重试";
                    NoPrivateHistoryDataText.Visibility = Visibility.Visible;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载私家历史新闻数据失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                NoPrivateHistoryDataText.Text = "加载私家历史新闻失败，请检查网络连接后重试";
                NoPrivateHistoryDataText.Visibility = Visibility.Visible;
            }
            finally
            {
                // 隐藏加载指示器
                PrivateHistoryLoadingPanel.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 处理新闻项点击事件
        /// </summary>
        private void NewsItem_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed && sender is Border border)
            {
                // 检查是否是普通新闻项
                var newsItem = border.Tag as NewsItem;
                if (newsItem != null)
                {
                    // 打开新闻详情窗口
                    var detailWindow = new NewsDetailWindow(newsItem)
                    {
                        Owner = this
                    };
                    detailWindow.ShowDialog();

                    // 刷新列表以更新已读状态
                    if (InternationalContent.Visibility == Visibility.Visible &&
                        NewsItemsControl.ItemsSource is List<NewsItem> newsList)
                    {
                        NewsItemsControl.Items.Refresh();
                    }
                    else if (HistoryContent.Visibility == Visibility.Visible &&
                             HistoryNewsItemsControl.ItemsSource is List<NewsItem> historyNewsList)
                    {
                        HistoryNewsItemsControl.Items.Refresh();
                    }
                    else if (MilanContent.Visibility == Visibility.Visible &&
                             MilanNewsItemsControl.ItemsSource is List<NewsItem> milanNewsList)
                    {
                        MilanNewsItemsControl.Items.Refresh();
                    }
                    return;
                }

                // 检查是否是文化新闻项
                var cultureNewsItem = border.Tag as CultureNewsItem;
                if (cultureNewsItem != null)
                {
                    // 将文化新闻项转换为NewsItem以便在详情窗口中显示
                    var convertedNewsItem = new NewsItem
                    {
                        Title = cultureNewsItem.Title,
                        Summary = cultureNewsItem.Summary,
                        Url = cultureNewsItem.Url,
                        Source = cultureNewsItem.Source,
                        PublishTime = cultureNewsItem.PublishTime,
                        Category = cultureNewsItem.Category,
                        NewsId = $"culture_{cultureNewsItem.Rank}_{DateTime.Now.Ticks}"
                    };

                    // 打开新闻详情窗口
                    var detailWindow = new NewsDetailWindow(convertedNewsItem)
                    {
                        Owner = this
                    };
                    detailWindow.ShowDialog();

                    // 刷新文化新闻列表
                    if (CultureContent.Visibility == Visibility.Visible &&
                        CultureNewsItemsControl.ItemsSource is List<CultureNewsItem> cultureNewsList)
                    {
                        CultureNewsItemsControl.Items.Refresh();
                    }
                }
            }
        }

        #endregion
    }
}