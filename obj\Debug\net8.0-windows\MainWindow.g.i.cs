﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "A860753BDFAFB2EDCAF834B47E85A2793E7C8100"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using LiveCharts.Wpf;
using Microsoft.Web.WebView2.Wpf;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using WpfApp;
using WpfApp.Controls;


namespace WpfApp {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 822 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MinimizeButton;
        
        #line default
        #line hidden
        
        
        #line 828 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MaximizeButton;
        
        #line default
        #line hidden
        
        
        #line 834 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        
        #line 979 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DashboardContent;
        
        #line default
        #line hidden
        
        
        #line 999 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddHealthDataButton;
        
        #line default
        #line hidden
        
        
        #line 1000 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddBloodPressureButton;
        
        #line default
        #line hidden
        
        
        #line 1001 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MonthlyStatsButton;
        
        #line default
        #line hidden
        
        
        #line 1022 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TodayStepsText;
        
        #line default
        #line hidden
        
        
        #line 1045 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LatestSystolicText;
        
        #line default
        #line hidden
        
        
        #line 1047 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LatestDiastolicText;
        
        #line default
        #line hidden
        
        
        #line 1070 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MonthlyStepsText;
        
        #line default
        #line hidden
        
        
        #line 1076 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DailyAverageStepsText;
        
        #line default
        #line hidden
        
        
        #line 1112 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart StepsChart;
        
        #line default
        #line hidden
        
        
        #line 1148 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart BloodPressureChart;
        
        #line default
        #line hidden
        
        
        #line 1194 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DailyTargetStepsText;
        
        #line default
        #line hidden
        
        
        #line 1208 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TargetProgressText;
        
        #line default
        #line hidden
        
        
        #line 1214 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ProgressBarFill;
        
        #line default
        #line hidden
        
        
        #line 1223 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RemainingDaysText;
        
        #line default
        #line hidden
        
        
        #line 1257 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BloodPressureStatsSubtitle;
        
        #line default
        #line hidden
        
        
        #line 1261 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshBloodPressureButton;
        
        #line default
        #line hidden
        
        
        #line 1288 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BpRecordCountText;
        
        #line default
        #line hidden
        
        
        #line 1298 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BpAverageText;
        
        #line default
        #line hidden
        
        
        #line 1308 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BpMaxText;
        
        #line default
        #line hidden
        
        
        #line 1318 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock BpAbnormalCountText;
        
        #line default
        #line hidden
        
        
        #line 1331 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MovieContent;
        
        #line default
        #line hidden
        
        
        #line 1352 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportMoviesButton;
        
        #line default
        #line hidden
        
        
        #line 1363 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MovieSearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 1366 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SearchButton;
        
        #line default
        #line hidden
        
        
        #line 1376 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button FilterButton;
        
        #line default
        #line hidden
        
        
        #line 1383 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.Popup FilterOptionsPopup;
        
        #line default
        #line hidden
        
        
        #line 1398 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MovieLoadingProgressGrid;
        
        #line default
        #line hidden
        
        
        #line 1404 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar MovieLoadingProgressBar;
        
        #line default
        #line hidden
        
        
        #line 1411 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl MovieItemsControl;
        
        #line default
        #line hidden
        
        
        #line 1527 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalMoviesText;
        
        #line default
        #line hidden
        
        
        #line 1537 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PageButton1;
        
        #line default
        #line hidden
        
        
        #line 1538 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PageButton2;
        
        #line default
        #line hidden
        
        
        #line 1539 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PageButton3;
        
        #line default
        #line hidden
        
        
        #line 1540 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PageButton4;
        
        #line default
        #line hidden
        
        
        #line 1541 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PageButton5;
        
        #line default
        #line hidden
        
        
        #line 1551 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PageJumpTextBox;
        
        #line default
        #line hidden
        
        
        #line 1561 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid StockContent;
        
        #line default
        #line hidden
        
        
        #line 1578 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshStocksButton;
        
        #line default
        #line hidden
        
        
        #line 1587 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.WrapPanel StockItemsPanel;
        
        #line default
        #line hidden
        
        
        #line 1591 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid StockLoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 1593 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StockLoadingText;
        
        #line default
        #line hidden
        
        
        #line 1608 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StockQueryTextBox;
        
        #line default
        #line hidden
        
        
        #line 1616 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StockQueryPlaceholder;
        
        #line default
        #line hidden
        
        
        #line 1630 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button QueryStockButton;
        
        #line default
        #line hidden
        
        
        #line 1669 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StockNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 1677 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StockNamePlaceholder;
        
        #line default
        #line hidden
        
        
        #line 1688 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox StockSharesTextBox;
        
        #line default
        #line hidden
        
        
        #line 1696 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StockSharesPlaceholder;
        
        #line default
        #line hidden
        
        
        #line 1710 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddStockButton;
        
        #line default
        #line hidden
        
        
        #line 1726 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView CustomStockListView;
        
        #line default
        #line hidden
        
        
        #line 1778 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid AIToolContent;
        
        #line default
        #line hidden
        
        
        #line 1798 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectKmlButton;
        
        #line default
        #line hidden
        
        
        #line 1805 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearMapButton;
        
        #line default
        #line hidden
        
        
        #line 1834 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MapStatusText;
        
        #line default
        #line hidden
        
        
        #line 1838 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock KmlFileNameText;
        
        #line default
        #line hidden
        
        
        #line 1845 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal Microsoft.Web.WebView2.Wpf.WebView2 MapWebView;
        
        #line default
        #line hidden
        
        
        #line 1856 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid FilmContent;
        
        #line default
        #line hidden
        
        
        #line 1874 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MovieQueryTextBox;
        
        #line default
        #line hidden
        
        
        #line 1913 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MovieQueryButton;
        
        #line default
        #line hidden
        
        
        #line 1958 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MovieQueryLoadingGrid;
        
        #line default
        #line hidden
        
        
        #line 1970 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel MovieSearchResultsPanel;
        
        #line default
        #line hidden
        
        
        #line 1971 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SearchResultsTitle;
        
        #line default
        #line hidden
        
        
        #line 1973 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl SearchResultsItemsControl;
        
        #line default
        #line hidden
        
        
        #line 2041 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border MovieQueryResultBorder;
        
        #line default
        #line hidden
        
        
        #line 2058 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Media.ImageBrush MoviePosterImage;
        
        #line default
        #line hidden
        
        
        #line 2064 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieTitleText;
        
        #line default
        #line hidden
        
        
        #line 2072 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieDirectorText;
        
        #line default
        #line hidden
        
        
        #line 2081 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieCastText;
        
        #line default
        #line hidden
        
        
        #line 2090 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieGenreText;
        
        #line default
        #line hidden
        
        
        #line 2099 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieCountryText;
        
        #line default
        #line hidden
        
        
        #line 2108 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieReleaseDateText;
        
        #line default
        #line hidden
        
        
        #line 2117 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieRatingText;
        
        #line default
        #line hidden
        
        
        #line 2125 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MovieOverviewText;
        
        #line default
        #line hidden
        
        
        #line 2128 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveToDbButton;
        
        #line default
        #line hidden
        
        
        #line 2167 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MessagesContent;
        
        #line default
        #line hidden
        
        
        #line 2185 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportDirectorsButton;
        
        #line default
        #line hidden
        
        
        #line 2226 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DirectorsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 2322 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CoffeeContent;
        
        #line default
        #line hidden
        
        
        #line 2386 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid CoffeeDataGrid;
        
        #line default
        #line hidden
        
        
        #line 2521 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CoffeeLoadingProgress;
        
        #line default
        #line hidden
        
        
        #line 2530 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid VolcanoContent;
        
        #line default
        #line hidden
        
        
        #line 2560 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar VolcanoProgressBar;
        
        #line default
        #line hidden
        
        
        #line 2567 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer VolcanoScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 2569 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl VolcanoMessagesControl;
        
        #line default
        #line hidden
        
        
        #line 2644 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox VolcanoMessageInputBox;
        
        #line default
        #line hidden
        
        
        #line 2659 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock VolcanoMessageInputPlaceholder;
        
        #line default
        #line hidden
        
        
        #line 2672 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SendVolcanoMessageButton;
        
        #line default
        #line hidden
        
        
        #line 2700 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CoffeeAnalysisContent;
        
        #line default
        #line hidden
        
        
        #line 2759 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart MonthlyExpenseChart;
        
        #line default
        #line hidden
        
        
        #line 2810 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.PieChart CountryDistributionChart;
        
        #line default
        #line hidden
        
        
        #line 2858 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.PieChart CoffeeTypeChart;
        
        #line default
        #line hidden
        
        
        #line 2906 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal LiveCharts.Wpf.CartesianChart CoffeeStatusChart;
        
        #line default
        #line hidden
        
        
        #line 2920 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid AnalysisLoadingProgress;
        
        #line default
        #line hidden
        
        
        #line 2929 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MusicContent;
        
        #line default
        #line hidden
        
        
        #line 2946 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MusicCountText;
        
        #line default
        #line hidden
        
        
        #line 2953 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MusicSearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 2956 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MusicSearchButton;
        
        #line default
        #line hidden
        
        
        #line 2965 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddMusicButton;
        
        #line default
        #line hidden
        
        
        #line 2974 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DownloadImagesButton;
        
        #line default
        #line hidden
        
        
        #line 2983 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AnalyzeImagesButton;
        
        #line default
        #line hidden
        
        
        #line 2994 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MusicLoadingProgressGrid;
        
        #line default
        #line hidden
        
        
        #line 3000 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar MusicLoadingProgressBar;
        
        #line default
        #line hidden
        
        
        #line 3006 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl MusicItemsControl;
        
        #line default
        #line hidden
        
        
        #line 3069 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoMusicDataText;
        
        #line default
        #line hidden
        
        
        #line 3075 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid NewsContent;
        
        #line default
        #line hidden
        
        
        #line 3092 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshNewsButton;
        
        #line default
        #line hidden
        
        
        #line 3115 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InternationalTabButton;
        
        #line default
        #line hidden
        
        
        #line 3146 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button HistoryTabButton;
        
        #line default
        #line hidden
        
        
        #line 3177 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button MilanTabButton;
        
        #line default
        #line hidden
        
        
        #line 3208 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CultureTabButton;
        
        #line default
        #line hidden
        
        
        #line 3239 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button WorldViewTabButton;
        
        #line default
        #line hidden
        
        
        #line 3270 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrivateHistoryTabButton;
        
        #line default
        #line hidden
        
        
        #line 3308 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid InternationalContent;
        
        #line default
        #line hidden
        
        
        #line 3319 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid NewsLoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 3331 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl NewsItemsControl;
        
        #line default
        #line hidden
        
        
        #line 3396 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoNewsDataText;
        
        #line default
        #line hidden
        
        
        #line 3402 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid HistoryContent;
        
        #line default
        #line hidden
        
        
        #line 3413 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid HistoryLoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 3425 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl HistoryNewsItemsControl;
        
        #line default
        #line hidden
        
        
        #line 3490 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoHistoryDataText;
        
        #line default
        #line hidden
        
        
        #line 3496 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MilanContent;
        
        #line default
        #line hidden
        
        
        #line 3507 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MilanLoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 3519 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl MilanNewsItemsControl;
        
        #line default
        #line hidden
        
        
        #line 3634 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoMilanDataText;
        
        #line default
        #line hidden
        
        
        #line 3640 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CultureContent;
        
        #line default
        #line hidden
        
        
        #line 3651 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CultureLoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 3663 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl CultureNewsItemsControl;
        
        #line default
        #line hidden
        
        
        #line 3730 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoCultureDataText;
        
        #line default
        #line hidden
        
        
        #line 3736 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid WorldViewContent;
        
        #line default
        #line hidden
        
        
        #line 3747 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid WorldViewLoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 3759 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl WorldViewNewsItemsControl;
        
        #line default
        #line hidden
        
        
        #line 3874 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoWorldViewDataText;
        
        #line default
        #line hidden
        
        
        #line 3880 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid PrivateHistoryContent;
        
        #line default
        #line hidden
        
        
        #line 3891 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid PrivateHistoryLoadingPanel;
        
        #line default
        #line hidden
        
        
        #line 3903 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ItemsControl PrivateHistoryNewsItemsControl;
        
        #line default
        #line hidden
        
        
        #line 4018 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NoPrivateHistoryDataText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WpfAdmin;V1.0.0.0;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 806 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.Border_Mousedown);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MinimizeButton = ((System.Windows.Controls.Button)(target));
            
            #line 824 "..\..\..\MainWindow.xaml"
            this.MinimizeButton.Click += new System.Windows.RoutedEventHandler(this.MinimizeButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.MaximizeButton = ((System.Windows.Controls.Button)(target));
            
            #line 830 "..\..\..\MainWindow.xaml"
            this.MaximizeButton.Click += new System.Windows.RoutedEventHandler(this.MaximizeButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 836 "..\..\..\MainWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 859 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.HealthButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 866 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NotesButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 873 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MusicDataButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 880 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ListingButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 888 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DataAnalysisButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 895 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CoffeeButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 903 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CoffeeAnalysisButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 910 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MusicButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 917 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.WeatherButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 925 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NewsButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 937 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 947 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MessagesButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 956 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.FilmButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 965 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.LogoutButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.DashboardContent = ((System.Windows.Controls.Grid)(target));
            
            #line 979 "..\..\..\MainWindow.xaml"
            this.DashboardContent.MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.Border_Mousedown);
            
            #line default
            #line hidden
            return;
            case 20:
            this.AddHealthDataButton = ((System.Windows.Controls.Button)(target));
            
            #line 999 "..\..\..\MainWindow.xaml"
            this.AddHealthDataButton.Click += new System.Windows.RoutedEventHandler(this.AddHealthDataButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.AddBloodPressureButton = ((System.Windows.Controls.Button)(target));
            
            #line 1000 "..\..\..\MainWindow.xaml"
            this.AddBloodPressureButton.Click += new System.Windows.RoutedEventHandler(this.AddBloodPressureButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.MonthlyStatsButton = ((System.Windows.Controls.Button)(target));
            
            #line 1001 "..\..\..\MainWindow.xaml"
            this.MonthlyStatsButton.Click += new System.Windows.RoutedEventHandler(this.MonthlyStatsButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.TodayStepsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.LatestSystolicText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.LatestDiastolicText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.MonthlyStepsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.DailyAverageStepsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.StepsChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 29:
            this.BloodPressureChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 30:
            this.DailyTargetStepsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 31:
            this.TargetProgressText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.ProgressBarFill = ((System.Windows.Controls.Border)(target));
            return;
            case 33:
            this.RemainingDaysText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.BloodPressureStatsSubtitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 35:
            this.RefreshBloodPressureButton = ((System.Windows.Controls.Button)(target));
            
            #line 1264 "..\..\..\MainWindow.xaml"
            this.RefreshBloodPressureButton.Click += new System.Windows.RoutedEventHandler(this.RefreshBloodPressureButton_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            this.BpRecordCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 37:
            this.BpAverageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 38:
            this.BpMaxText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 39:
            this.BpAbnormalCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 40:
            this.MovieContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 41:
            this.ExportMoviesButton = ((System.Windows.Controls.Button)(target));
            
            #line 1355 "..\..\..\MainWindow.xaml"
            this.ExportMoviesButton.Click += new System.Windows.RoutedEventHandler(this.ExportMovies_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            this.MovieSearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1365 "..\..\..\MainWindow.xaml"
            this.MovieSearchTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.MovieSearchTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 43:
            this.SearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 1367 "..\..\..\MainWindow.xaml"
            this.SearchButton.Click += new System.Windows.RoutedEventHandler(this.SearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 44:
            this.FilterButton = ((System.Windows.Controls.Button)(target));
            
            #line 1376 "..\..\..\MainWindow.xaml"
            this.FilterButton.Click += new System.Windows.RoutedEventHandler(this.FilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 45:
            this.FilterOptionsPopup = ((System.Windows.Controls.Primitives.Popup)(target));
            return;
            case 46:
            
            #line 1387 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RatingFilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 47:
            
            #line 1389 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TimeFilterButton_Click);
            
            #line default
            #line hidden
            return;
            case 48:
            this.MovieLoadingProgressGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 49:
            this.MovieLoadingProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 50:
            this.MovieItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 53:
            this.TotalMoviesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 54:
            
            #line 1533 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PreviousPage_Click);
            
            #line default
            #line hidden
            return;
            case 55:
            this.PageButton1 = ((System.Windows.Controls.Button)(target));
            
            #line 1537 "..\..\..\MainWindow.xaml"
            this.PageButton1.Click += new System.Windows.RoutedEventHandler(this.PageButton_Click);
            
            #line default
            #line hidden
            return;
            case 56:
            this.PageButton2 = ((System.Windows.Controls.Button)(target));
            
            #line 1538 "..\..\..\MainWindow.xaml"
            this.PageButton2.Click += new System.Windows.RoutedEventHandler(this.PageButton_Click);
            
            #line default
            #line hidden
            return;
            case 57:
            this.PageButton3 = ((System.Windows.Controls.Button)(target));
            
            #line 1539 "..\..\..\MainWindow.xaml"
            this.PageButton3.Click += new System.Windows.RoutedEventHandler(this.PageButton_Click);
            
            #line default
            #line hidden
            return;
            case 58:
            this.PageButton4 = ((System.Windows.Controls.Button)(target));
            
            #line 1540 "..\..\..\MainWindow.xaml"
            this.PageButton4.Click += new System.Windows.RoutedEventHandler(this.PageButton_Click);
            
            #line default
            #line hidden
            return;
            case 59:
            this.PageButton5 = ((System.Windows.Controls.Button)(target));
            
            #line 1541 "..\..\..\MainWindow.xaml"
            this.PageButton5.Click += new System.Windows.RoutedEventHandler(this.PageButton_Click);
            
            #line default
            #line hidden
            return;
            case 60:
            
            #line 1543 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NextPage_Click);
            
            #line default
            #line hidden
            return;
            case 61:
            this.PageJumpTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 62:
            
            #line 1553 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.JumpToPage_Click);
            
            #line default
            #line hidden
            return;
            case 63:
            this.StockContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 64:
            this.RefreshStocksButton = ((System.Windows.Controls.Button)(target));
            
            #line 1580 "..\..\..\MainWindow.xaml"
            this.RefreshStocksButton.Click += new System.Windows.RoutedEventHandler(this.RefreshStocks_Click);
            
            #line default
            #line hidden
            return;
            case 65:
            this.StockItemsPanel = ((System.Windows.Controls.WrapPanel)(target));
            return;
            case 66:
            this.StockLoadingPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 67:
            this.StockLoadingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 68:
            this.StockQueryTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1614 "..\..\..\MainWindow.xaml"
            this.StockQueryTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.StockQueryTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 1615 "..\..\..\MainWindow.xaml"
            this.StockQueryTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.StockQueryTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 69:
            this.StockQueryPlaceholder = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 70:
            this.QueryStockButton = ((System.Windows.Controls.Button)(target));
            
            #line 1631 "..\..\..\MainWindow.xaml"
            this.QueryStockButton.Click += new System.Windows.RoutedEventHandler(this.QueryStockButton_Click);
            
            #line default
            #line hidden
            return;
            case 71:
            this.StockNameTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1675 "..\..\..\MainWindow.xaml"
            this.StockNameTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.StockNameTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 1676 "..\..\..\MainWindow.xaml"
            this.StockNameTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.StockNameTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 72:
            this.StockNamePlaceholder = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 73:
            this.StockSharesTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1694 "..\..\..\MainWindow.xaml"
            this.StockSharesTextBox.GotFocus += new System.Windows.RoutedEventHandler(this.StockSharesTextBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 1695 "..\..\..\MainWindow.xaml"
            this.StockSharesTextBox.LostFocus += new System.Windows.RoutedEventHandler(this.StockSharesTextBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 74:
            this.StockSharesPlaceholder = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 75:
            this.AddStockButton = ((System.Windows.Controls.Button)(target));
            
            #line 1711 "..\..\..\MainWindow.xaml"
            this.AddStockButton.Click += new System.Windows.RoutedEventHandler(this.AddStockButton_Click);
            
            #line default
            #line hidden
            return;
            case 76:
            this.CustomStockListView = ((System.Windows.Controls.ListView)(target));
            return;
            case 79:
            this.AIToolContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 80:
            this.SelectKmlButton = ((System.Windows.Controls.Button)(target));
            
            #line 1798 "..\..\..\MainWindow.xaml"
            this.SelectKmlButton.Click += new System.Windows.RoutedEventHandler(this.SelectKmlButton_Click);
            
            #line default
            #line hidden
            return;
            case 81:
            this.ClearMapButton = ((System.Windows.Controls.Button)(target));
            
            #line 1805 "..\..\..\MainWindow.xaml"
            this.ClearMapButton.Click += new System.Windows.RoutedEventHandler(this.ClearMapButton_Click);
            
            #line default
            #line hidden
            return;
            case 82:
            this.MapStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 83:
            this.KmlFileNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 84:
            this.MapWebView = ((Microsoft.Web.WebView2.Wpf.WebView2)(target));
            
            #line 1846 "..\..\..\MainWindow.xaml"
            this.MapWebView.NavigationCompleted += new System.EventHandler<Microsoft.Web.WebView2.Core.CoreWebView2NavigationCompletedEventArgs>(this.MapWebView_NavigationCompleted);
            
            #line default
            #line hidden
            
            #line 1847 "..\..\..\MainWindow.xaml"
            this.MapWebView.CoreWebView2InitializationCompleted += new System.EventHandler<Microsoft.Web.WebView2.Core.CoreWebView2InitializationCompletedEventArgs>(this.MapWebView_CoreWebView2InitializationCompleted);
            
            #line default
            #line hidden
            return;
            case 85:
            this.FilmContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 86:
            this.MovieQueryTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 1882 "..\..\..\MainWindow.xaml"
            this.MovieQueryTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.MovieQueryTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 87:
            this.MovieQueryButton = ((System.Windows.Controls.Button)(target));
            
            #line 1922 "..\..\..\MainWindow.xaml"
            this.MovieQueryButton.Click += new System.Windows.RoutedEventHandler(this.MovieQueryButton_Click);
            
            #line default
            #line hidden
            return;
            case 88:
            this.MovieQueryLoadingGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 89:
            this.MovieSearchResultsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 90:
            this.SearchResultsTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 91:
            this.SearchResultsItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 93:
            this.MovieQueryResultBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 94:
            this.MoviePosterImage = ((System.Windows.Media.ImageBrush)(target));
            return;
            case 95:
            this.MovieTitleText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 96:
            this.MovieDirectorText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 97:
            this.MovieCastText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 98:
            this.MovieGenreText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 99:
            this.MovieCountryText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 100:
            this.MovieReleaseDateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 101:
            this.MovieRatingText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 102:
            this.MovieOverviewText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 103:
            this.SaveToDbButton = ((System.Windows.Controls.Button)(target));
            
            #line 2136 "..\..\..\MainWindow.xaml"
            this.SaveToDbButton.Click += new System.Windows.RoutedEventHandler(this.SaveToDbButton_Click);
            
            #line default
            #line hidden
            return;
            case 104:
            this.MessagesContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 105:
            this.ExportDirectorsButton = ((System.Windows.Controls.Button)(target));
            
            #line 2188 "..\..\..\MainWindow.xaml"
            this.ExportDirectorsButton.Click += new System.Windows.RoutedEventHandler(this.ExportDirectors_Click);
            
            #line default
            #line hidden
            return;
            case 106:
            this.DirectorsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 108:
            this.CoffeeContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 109:
            
            #line 2330 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TypeButton_Click);
            
            #line default
            #line hidden
            return;
            case 110:
            
            #line 2338 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PriceButton_Click);
            
            #line default
            #line hidden
            return;
            case 111:
            
            #line 2346 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.WeightButton_Click);
            
            #line default
            #line hidden
            return;
            case 112:
            
            #line 2354 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CountryButton_Click);
            
            #line default
            #line hidden
            return;
            case 113:
            
            #line 2361 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TimeButton_Click);
            
            #line default
            #line hidden
            return;
            case 114:
            
            #line 2368 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QueryButton_Click);
            
            #line default
            #line hidden
            return;
            case 115:
            
            #line 2377 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.OrderAddButton_Click);
            
            #line default
            #line hidden
            return;
            case 116:
            this.CoffeeDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 119:
            this.CoffeeLoadingProgress = ((System.Windows.Controls.Grid)(target));
            return;
            case 120:
            this.VolcanoContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 121:
            this.VolcanoProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 122:
            this.VolcanoScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 123:
            this.VolcanoMessagesControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 125:
            this.VolcanoMessageInputBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 2654 "..\..\..\MainWindow.xaml"
            this.VolcanoMessageInputBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.VolcanoMessageInputBox_KeyDown);
            
            #line default
            #line hidden
            
            #line 2656 "..\..\..\MainWindow.xaml"
            this.VolcanoMessageInputBox.GotFocus += new System.Windows.RoutedEventHandler(this.VolcanoMessageInputBox_GotFocus);
            
            #line default
            #line hidden
            
            #line 2657 "..\..\..\MainWindow.xaml"
            this.VolcanoMessageInputBox.LostFocus += new System.Windows.RoutedEventHandler(this.VolcanoMessageInputBox_LostFocus);
            
            #line default
            #line hidden
            return;
            case 126:
            this.VolcanoMessageInputPlaceholder = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 127:
            this.SendVolcanoMessageButton = ((System.Windows.Controls.Button)(target));
            
            #line 2673 "..\..\..\MainWindow.xaml"
            this.SendVolcanoMessageButton.Click += new System.Windows.RoutedEventHandler(this.SendVolcanoMessageButton_Click);
            
            #line default
            #line hidden
            return;
            case 128:
            this.CoffeeAnalysisContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 129:
            this.MonthlyExpenseChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 130:
            this.CountryDistributionChart = ((LiveCharts.Wpf.PieChart)(target));
            return;
            case 131:
            this.CoffeeTypeChart = ((LiveCharts.Wpf.PieChart)(target));
            return;
            case 132:
            this.CoffeeStatusChart = ((LiveCharts.Wpf.CartesianChart)(target));
            return;
            case 133:
            this.AnalysisLoadingProgress = ((System.Windows.Controls.Grid)(target));
            return;
            case 134:
            this.MusicContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 135:
            this.MusicCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 136:
            this.MusicSearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 2955 "..\..\..\MainWindow.xaml"
            this.MusicSearchTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.MusicSearchTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 137:
            this.MusicSearchButton = ((System.Windows.Controls.Button)(target));
            
            #line 2957 "..\..\..\MainWindow.xaml"
            this.MusicSearchButton.Click += new System.Windows.RoutedEventHandler(this.MusicSearchButton_Click);
            
            #line default
            #line hidden
            return;
            case 138:
            this.AddMusicButton = ((System.Windows.Controls.Button)(target));
            
            #line 2965 "..\..\..\MainWindow.xaml"
            this.AddMusicButton.Click += new System.Windows.RoutedEventHandler(this.AddMusicButton_Click);
            
            #line default
            #line hidden
            return;
            case 139:
            this.DownloadImagesButton = ((System.Windows.Controls.Button)(target));
            
            #line 2974 "..\..\..\MainWindow.xaml"
            this.DownloadImagesButton.Click += new System.Windows.RoutedEventHandler(this.DownloadImagesButton_Click);
            
            #line default
            #line hidden
            return;
            case 140:
            this.AnalyzeImagesButton = ((System.Windows.Controls.Button)(target));
            
            #line 2983 "..\..\..\MainWindow.xaml"
            this.AnalyzeImagesButton.Click += new System.Windows.RoutedEventHandler(this.AnalyzeImagesButton_Click);
            
            #line default
            #line hidden
            return;
            case 141:
            this.MusicLoadingProgressGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 142:
            this.MusicLoadingProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 143:
            this.MusicItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 146:
            this.NoMusicDataText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 147:
            this.NewsContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 148:
            this.RefreshNewsButton = ((System.Windows.Controls.Button)(target));
            
            #line 3093 "..\..\..\MainWindow.xaml"
            this.RefreshNewsButton.Click += new System.Windows.RoutedEventHandler(this.RefreshNewsButton_Click);
            
            #line default
            #line hidden
            return;
            case 149:
            this.InternationalTabButton = ((System.Windows.Controls.Button)(target));
            
            #line 3117 "..\..\..\MainWindow.xaml"
            this.InternationalTabButton.Click += new System.Windows.RoutedEventHandler(this.InternationalTab_Click);
            
            #line default
            #line hidden
            return;
            case 150:
            this.HistoryTabButton = ((System.Windows.Controls.Button)(target));
            
            #line 3148 "..\..\..\MainWindow.xaml"
            this.HistoryTabButton.Click += new System.Windows.RoutedEventHandler(this.HistoryTab_Click);
            
            #line default
            #line hidden
            return;
            case 151:
            this.MilanTabButton = ((System.Windows.Controls.Button)(target));
            
            #line 3179 "..\..\..\MainWindow.xaml"
            this.MilanTabButton.Click += new System.Windows.RoutedEventHandler(this.MilanTab_Click);
            
            #line default
            #line hidden
            return;
            case 152:
            this.CultureTabButton = ((System.Windows.Controls.Button)(target));
            
            #line 3210 "..\..\..\MainWindow.xaml"
            this.CultureTabButton.Click += new System.Windows.RoutedEventHandler(this.CultureTab_Click);
            
            #line default
            #line hidden
            return;
            case 153:
            this.WorldViewTabButton = ((System.Windows.Controls.Button)(target));
            
            #line 3241 "..\..\..\MainWindow.xaml"
            this.WorldViewTabButton.Click += new System.Windows.RoutedEventHandler(this.WorldViewTab_Click);
            
            #line default
            #line hidden
            return;
            case 154:
            this.PrivateHistoryTabButton = ((System.Windows.Controls.Button)(target));
            
            #line 3272 "..\..\..\MainWindow.xaml"
            this.PrivateHistoryTabButton.Click += new System.Windows.RoutedEventHandler(this.PrivateHistoryTab_Click);
            
            #line default
            #line hidden
            return;
            case 155:
            this.InternationalContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 156:
            this.NewsLoadingPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 157:
            this.NewsItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 159:
            this.NoNewsDataText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 160:
            this.HistoryContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 161:
            this.HistoryLoadingPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 162:
            this.HistoryNewsItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 164:
            this.NoHistoryDataText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 165:
            this.MilanContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 166:
            this.MilanLoadingPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 167:
            this.MilanNewsItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 169:
            this.NoMilanDataText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 170:
            this.CultureContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 171:
            this.CultureLoadingPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 172:
            this.CultureNewsItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 174:
            this.NoCultureDataText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 175:
            this.WorldViewContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 176:
            this.WorldViewLoadingPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 177:
            this.WorldViewNewsItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 179:
            this.NoWorldViewDataText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 180:
            this.PrivateHistoryContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 181:
            this.PrivateHistoryLoadingPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 182:
            this.PrivateHistoryNewsItemsControl = ((System.Windows.Controls.ItemsControl)(target));
            return;
            case 184:
            this.NoPrivateHistoryDataText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 51:
            
            #line 1493 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewMovieDetail_Click);
            
            #line default
            #line hidden
            break;
            case 52:
            
            #line 1495 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditMovie_Click);
            
            #line default
            #line hidden
            break;
            case 77:
            
            #line 1751 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditStockButton_Click);
            
            #line default
            #line hidden
            break;
            case 78:
            
            #line 1762 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveStockButton_Click);
            
            #line default
            #line hidden
            break;
            case 92:
            
            #line 2009 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewMovieDetails_Click);
            
            #line default
            #line hidden
            break;
            case 107:
            
            #line 2283 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewDirectorInfo_Click);
            
            #line default
            #line hidden
            break;
            case 117:
            
            #line 2500 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewMemoButton_Click);
            
            #line default
            #line hidden
            break;
            case 118:
            
            #line 2508 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditCoffee_Click);
            
            #line default
            #line hidden
            break;
            case 124:
            
            #line 2614 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyVolcanoMessage_Click);
            
            #line default
            #line hidden
            break;
            case 144:
            
            #line 3051 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditMusic_Click);
            
            #line default
            #line hidden
            break;
            case 145:
            
            #line 3055 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteMusic_Click);
            
            #line default
            #line hidden
            break;
            case 158:
            
            #line 3335 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.NewsItem_MouseDown);
            
            #line default
            #line hidden
            break;
            case 163:
            
            #line 3429 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.NewsItem_MouseDown);
            
            #line default
            #line hidden
            break;
            case 168:
            
            #line 3531 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.NewsItem_MouseDown);
            
            #line default
            #line hidden
            break;
            case 173:
            
            #line 3667 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.NewsItem_MouseDown);
            
            #line default
            #line hidden
            break;
            case 178:
            
            #line 3771 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.NewsItem_MouseDown);
            
            #line default
            #line hidden
            break;
            case 183:
            
            #line 3915 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseDown += new System.Windows.Input.MouseButtonEventHandler(this.NewsItem_MouseDown);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

