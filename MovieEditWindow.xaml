﻿<Window x:Class="WpfApp.MovieEditWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="编辑电影信息" Height="800" Width="800"
        Background="#364375"
        WindowStyle="None"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">
    <Window.Resources>
        <!-- 标签样式 -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#8890AD"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Margin" Value="0,10,0,5"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>

        <!-- 内容样式 -->
        <Style x:Key="ContentStyle" TargetType="TextBox">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="Background" Value="#1E2746"/>
            <Setter Property="BorderBrush" Value="#3D4B80"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,5"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <!-- 多行文本框样式 -->
        <Style x:Key="MultilineTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource ContentStyle}">
            <Setter Property="AcceptsReturn" Value="True"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
            <Setter Property="Height" Value="150"/>
            <Setter Property="VerticalContentAlignment" Value="Top"/>
        </Style>

        <!-- 按钮样式 -->
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#3498db"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#2980b9"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Background="#293153" Margin="20" CornerRadius="15">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <TextBlock x:Name="MovieTitle" Text="编辑电影信息" FontSize="24" FontWeight="Bold" Foreground="White" Margin="0,0,0,20"/>

            <!-- 内容区域 -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧海报区域 -->
                <Border Width="280" Height="400" CornerRadius="10" Margin="0,0,20,0">
                    <Border.Effect>
                        <DropShadowEffect ShadowDepth="3" BlurRadius="5" Opacity="0.3" Color="Black"/>
                    </Border.Effect>
                    <Image x:Name="PosterImage" Stretch="UniformToFill"/>
                </Border>

                <!-- 右侧信息区域 -->
                <ScrollViewer Grid.Column="1" VerticalScrollBarVisibility="Auto">
                    <StackPanel>
                        <!-- 评分 -->
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <TextBlock Text="评分：" Style="{StaticResource LabelStyle}" Margin="0"/>
                            <Border Background="#FFDD33" CornerRadius="4" Padding="8,2" Margin="10,0,0,0">
                                <TextBlock x:Name="RatingText" Text="9.0" FontWeight="Bold" Foreground="#333333"/>
                            </Border>
                        </StackPanel>

                        <!-- 导演 -->
                        <TextBlock Text="导演：" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="DirectorTextBox" Style="{StaticResource ContentStyle}" IsReadOnly="True"/>

                        <!-- 国家 -->
                        <TextBlock Text="国家：" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="CountryTextBox" Style="{StaticResource ContentStyle}" IsReadOnly="True"/>

                        <!-- 详细介绍 -->
                        <TextBlock Text="详细介绍：" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="IntroTextBox" Style="{StaticResource MultilineTextBoxStyle}"/>

                        <!-- 磁力链接 -->
                        <TextBlock Text="磁力链接：" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="MagnetTextBox" Style="{StaticResource ContentStyle}"/>

                        <!-- 豆瓣链接 -->
                        <TextBlock Text="豆瓣链接：" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="DoubanTextBox" Style="{StaticResource ContentStyle}"/>

                        <!-- 字幕链接 -->
                        <TextBlock Text="字幕链接：" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="SublinkTextBox" Style="{StaticResource ContentStyle}"/>

                        <!-- 获得奖项 -->
                        <TextBlock Text="获得奖项：" Style="{StaticResource LabelStyle}"/>
                        <TextBox x:Name="AwardsTextBox" Style="{StaticResource ContentStyle}" Height="60" TextWrapping="Wrap" AcceptsReturn="True"/>
                    </StackPanel>
                </ScrollViewer>
            </Grid>

            <!-- 底部按钮区域 -->
            <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
                <Button Content="取消" Style="{StaticResource ButtonStyle}" Click="CancelButton_Click"/>
                <Button Content="保存" Style="{StaticResource ButtonStyle}" Click="SaveButton_Click"/>
            </StackPanel>
        </Grid>
    </Border>
</Window>
