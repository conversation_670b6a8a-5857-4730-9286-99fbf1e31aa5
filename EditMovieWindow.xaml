<Window x:Class="WpfAdmin.EditMovieWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WpfAdmin"
        mc:Ignorable="d"
        Title="编辑电影" Height="650" Width="550"
        WindowStyle="None"
        ResizeMode="NoResize"
        Background="Transparent"
        AllowsTransparency="True">
    <Border CornerRadius="20" Background="#202020" MouseDown="Border_Mousedown">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Border Background="#303030" CornerRadius="20,20,0,0">
                <Grid>
                    <TextBlock Text="编辑电影信息" VerticalAlignment="Center" Margin="15,0,0,0" Foreground="White" FontSize="16"/>
                    <Button x:Name="CloseButton" HorizontalAlignment="Right" VerticalAlignment="Center" Width="30" Height="30"
                            Background="Transparent" BorderThickness="0" Click="CloseButton_Click">
                        <Button.Content>
                            <TextBlock Text="✕" Foreground="White" FontSize="18" VerticalAlignment="Center" HorizontalAlignment="Center"/>
                        </Button.Content>
                    </Button>
                </Grid>
            </Border>

            <!-- 内容区域 -->
            <ScrollViewer Grid.Row="1" Margin="15" VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <!-- 电影标题 -->
                    <TextBlock Text="电影标题:" Foreground="White" Margin="0,10,0,5"/>
                    <TextBox x:Name="TitleTextBox" Height="30" FontSize="14" Padding="5"/>

                    <!-- 导演 -->
                    <TextBlock Text="导演:" Foreground="White" Margin="0,10,0,5"/>
                    <TextBox x:Name="DirectorTextBox" Height="30" FontSize="14" Padding="5"/>

                    <!-- 国家/地区 -->
                    <TextBlock Text="国家/地区:" Foreground="White" Margin="0,10,0,5"/>
                    <TextBox x:Name="CountryTextBox" Height="30" FontSize="14" Padding="5"/>

                    <!-- 简介 -->
                    <TextBlock Text="简介:" Foreground="White" Margin="0,10,0,5"/>
                    <TextBox x:Name="IntroTextBox" Height="80" FontSize="14" Padding="5" TextWrapping="Wrap" AcceptsReturn="True"/>

                    <!-- 评分 -->
                    <TextBlock Text="评分:" Foreground="White" Margin="0,10,0,5"/>
                    <TextBox x:Name="RatingTextBox" Height="30" FontSize="14" Padding="5"/>

                    <!-- 磁力链接 -->
                    <TextBlock Text="磁力链接:" Foreground="White" Margin="0,10,0,5"/>
                    <TextBox x:Name="MagnetTextBox" Height="60" FontSize="14" Padding="5" TextWrapping="Wrap" AcceptsReturn="True"/>

                    <!-- 字幕链接 -->
                    <TextBlock Text="字幕链接:" Foreground="White" Margin="0,10,0,5"/>
                    <TextBox x:Name="SubtitleTextBox" Height="60" FontSize="14" Padding="5" TextWrapping="Wrap" AcceptsReturn="True"/>

                    <!-- 豆瓣链接 -->
                    <TextBlock Text="豆瓣链接:" Foreground="White" Margin="0,10,0,5"/>
                    <TextBox x:Name="DoubanTextBox" Height="30" FontSize="14" Padding="5"/>

                    <!-- 海报URL -->
                    <TextBlock Text="海报URL:" Foreground="White" Margin="0,10,0,5"/>
                    <TextBox x:Name="PosterUrlTextBox" Height="30" FontSize="14" Padding="5"/>

                    <!-- 发行日期 -->
                    <TextBlock Text="发行日期:" Foreground="White" Margin="0,10,0,5"/>
                    <DatePicker x:Name="ReleaseDatePicker" Height="30" FontSize="14"/>

                    <!-- 获得奖项 -->
                    <TextBlock Text="获得奖项:" Foreground="White" Margin="0,10,0,5"/>
                    <TextBox x:Name="AwardsTextBox" Height="60" FontSize="14" Padding="5" TextWrapping="Wrap" AcceptsReturn="True"/>

                    <!-- 保存按钮 -->
                    <Grid Margin="0,20,0,10">
                        <Button x:Name="SaveButton" Content="保存" Height="40" Width="120"
                                Background="#3498db" Foreground="White" FontSize="16"
                                Click="SaveButton_Click" Cursor="Hand"/>
                        <ProgressBar x:Name="SaveProgressBar" IsIndeterminate="True" Height="5"
                                    Visibility="Collapsed" Foreground="#3498db" Background="Transparent"/>
                    </Grid>
                </StackPanel>
            </ScrollViewer>
        </Grid>
    </Border>
</Window>