﻿<Window x:Class="WpfApp.AddCoffeeWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="添加咖啡数据" Height="800" Width="500"
        Background="#F5E9D9" WindowStyle="None" WindowStartupLocation="CenterScreen">
    <Window.Resources>
        <!-- 统一的样式资源 -->

        <Style TargetType="Button">
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="10,10"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="MinWidth" Value="80"/>
        </Style>

        <!-- 文本框样式 -->
        <Style x:Key="FlatTextBoxStyle" TargetType="TextBox">
            <Setter Property="Background" Value="#F8F1E7"/>
            <!-- 略浅于背景的咖啡色 -->
            <Setter Property="Foreground" Value="#5D4037"/>
            <!-- 深咖啡色文字 -->
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Height" Value="35"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                            CornerRadius="4"
                            BorderThickness="0"
                            BorderBrush="Transparent">
                            <Border.Effect>
                                <DropShadowEffect ShadowDepth="1" BlurRadius="3" Opacity="0.2" Color="#5D4037"/>
                            </Border.Effect>
                            <ScrollViewer x:Name="PART_ContentHost" Margin="{TemplateBinding Padding}"
                                      VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 下拉框样式 -->
        <Style x:Key="FlatComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Background" Value="#F8F1E7"/>
            <Setter Property="Foreground" Value="#5D4037"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="Height" Value="35"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBox">
                        <Grid>
                            <Border Background="{TemplateBinding Background}"
                                    CornerRadius="4"
                                    BorderThickness="0">
                                <Border.Effect>
                                    <DropShadowEffect ShadowDepth="1" BlurRadius="3" Opacity="0.2" Color="#5D4037"/>
                                </Border.Effect>
                            </Border>
                            <ToggleButton x:Name="ToggleButton"
                                          BorderBrush="Transparent"
                                          Background="Transparent"
                                          Focusable="false"
                                          IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                          ClickMode="Press">
                                <ToggleButton.Template>
                                    <ControlTemplate TargetType="ToggleButton">
                                        <Grid Background="Transparent">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition />
                                                <ColumnDefinition Width="30" />
                                            </Grid.ColumnDefinitions>
                                            <Border Grid.ColumnSpan="2" Background="Transparent"/>
                                            <Path x:Name="Arrow"
                                                  Grid.Column="1"
                                                  Fill="#5D4037"
                                                  HorizontalAlignment="Center"
                                                  VerticalAlignment="Center"
                                                  Data="M 0 0 L 8 8 L 16 0 Z"/>
                                        </Grid>
                                    </ControlTemplate>
                                </ToggleButton.Template>
                            </ToggleButton>
                            <ContentPresenter Name="ContentSite"
                                              IsHitTestVisible="False"
                                              Content="{TemplateBinding SelectionBoxItem}"
                                              ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                              ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                              Margin="10,0,30,0"
                                              VerticalAlignment="Center"/>
                            <Popup Name="Popup"
                                   Placement="Bottom"
                                   IsOpen="{TemplateBinding IsDropDownOpen}"
                                   AllowsTransparency="True"
                                   Focusable="False"
                                   PopupAnimation="Slide">
                                <Grid Name="DropDown"
                                      SnapsToDevicePixels="True"
                                      MinWidth="{TemplateBinding ActualWidth}"
                                      MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                    <Border x:Name="DropDownBorder"
                                            Background="#F8F1E7"
                                            CornerRadius="4"
                                            Margin="0,1,0,0">
                                        <Border.Effect>
                                            <DropShadowEffect ShadowDepth="2" BlurRadius="5" Opacity="0.2" Color="#5D4037"/>
                                        </Border.Effect>
                                    </Border>
                                    <ScrollViewer Margin="4,6,4,6" SnapsToDevicePixels="True">
                                        <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained" />
                                    </ScrollViewer>
                                </Grid>
                            </Popup>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 日期选择器样式 -->
        <Style x:Key="FlatDatePickerStyle" TargetType="DatePicker">
            <Setter Property="Background" Value="#F8F1E7"/>
            <Setter Property="Foreground" Value="#5D4037"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="Height" Value="35"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="DatePicker">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4"
                                BorderThickness="0">
                            <Border.Effect>
                                <DropShadowEffect ShadowDepth="1" BlurRadius="3" Opacity="0.2" Color="#5D4037"/>
                            </Border.Effect>
                            <!--<Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="61*"/>
                                    <ColumnDefinition Width="148*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>-->
                                <DatePicker x:Name="datePicker" SelectedDate="{Binding SelectedDate, Mode=TwoWay}" Grid.ColumnSpan="2" />
                                <!--<DatePickerTextBox x:Name="PART_TextBox"
                                                  Grid.Column="0"
                                                  Foreground="{TemplateBinding Foreground}"
                                                  Background="Transparent"
                                                  BorderThickness="0"
                                                  Padding="{TemplateBinding Padding}"
                                                  VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"/>
                                <Button x:Name="PART_Button"
                                        Grid.Column="1"
                                        Width="30"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        Padding="0">
                                    <Path Fill="#5D4037"
                                          Data="M8,12H16V4H8V12M10,6H14V10H10V6M4,12H6V14H4V12M4,10H6V12H4V10M4,8H6V10H4V8M4,6H6V8H4V6M4,4H6V6H4V4M18,6H20V8H18V6M18,4H20V6H18V4M18,2H20V4H18V2M18,8H20V10H18V8M18,12H20V14H18V12M18,10H20V12H18V10M12,18H14V20H12V18M10,18H12V20H10V18M8,18H10V20H8V18M6,18H8V20H6V18M4,18H6V20H4V18M16,18H18V20H16V18M14,18H16V20H14V18M16,16H18V18H16V16M8,16H10V18H8V16M4,16H6V18H4V16M6,16H8V18H6V16M10,16H12V18H10V16M12,16H14V18H12V16M14,16H16V18H14V16Z"
                                          Stretch="Uniform"
                                          Width="16"
                                          Height="16"/>
                                </Button>-->
                            <!--</Grid>-->
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 按钮样式 -->
        <Style x:Key="FlatButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#8D6E63"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="25,10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="MinWidth" Value="40"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4"
                                BorderThickness="0">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                            <Border.Effect>
                                <DropShadowEffect ShadowDepth="1" BlurRadius="3" Opacity="0.3" Color="#5D4037"/>
                            </Border.Effect>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#6D4C41"/>
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <TranslateTransform Y="-2"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#5D4037"/>
                                <Setter Property="RenderTransform">
                                    <Setter.Value>
                                        <TranslateTransform Y="0"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
            <Setter Property="RenderTransform">
                <Setter.Value>
                    <TranslateTransform/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="TextBlock">
            <Setter Property="Margin" Value="0,5,0,5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Foreground" Value="#5D4037"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
        </Style>

        <!-- 自定义滚动条拖拽块样式 -->
        <Style x:Key="CustomScrollBarThumbStyle" TargetType="{x:Type Thumb}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Thumb}">
                        <Grid>
                            <Border Background="#8D6E63" CornerRadius="4" Opacity="0.8"/>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 自定义滚动条样式 -->
        <Style x:Key="CustomScrollBarStyle" TargetType="{x:Type ScrollBar}">
            <Setter Property="Width" Value="8"/>
            <Setter Property="Background" Value="#D7CCC8"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ScrollBar}">
                        <Grid>
                            <Border Background="{TemplateBinding Background}" CornerRadius="4" Opacity="0.6"/>
                            <Track x:Name="PART_Track" IsDirectionReversed="True">
                                <Track.Thumb>
                                    <Thumb Style="{StaticResource CustomScrollBarThumbStyle}"/>
                                </Track.Thumb>
                            </Track>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 自定义滚动视图样式 -->
        <Style x:Key="CustomScrollViewerStyle" TargetType="{x:Type ScrollViewer}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ScrollViewer}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <ScrollContentPresenter Grid.Column="0" Grid.Row="0" Margin="0,0,8,0"/>

                            <ScrollBar x:Name="PART_VerticalScrollBar"
                                      Grid.Column="1" Grid.Row="0"
                                      Orientation="Vertical"
                                      Value="{TemplateBinding VerticalOffset}"
                                      Maximum="{TemplateBinding ScrollableHeight}"
                                      ViewportSize="{TemplateBinding ViewportHeight}"
                                      Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                                      Style="{StaticResource CustomScrollBarStyle}"/>

                            <ScrollBar x:Name="PART_HorizontalScrollBar"
                                      Grid.Column="0" Grid.Row="1"
                                      Orientation="Horizontal"
                                      Value="{TemplateBinding HorizontalOffset}"
                                      Maximum="{TemplateBinding ScrollableWidth}"
                                      ViewportSize="{TemplateBinding ViewportWidth}"
                                      Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                                      Style="{StaticResource CustomScrollBarStyle}"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 自定义TextBox样式 -->
        <Style x:Key="CustomTextBoxStyle" TargetType="{x:Type TextBox}">
            <Setter Property="Background" Value="#F8F1E7"/>
            <Setter Property="Foreground" Value="#5D4037"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="0,0,0,15"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type TextBox}">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4"
                                BorderThickness="0">
                            <Border.Effect>
                                <DropShadowEffect ShadowDepth="1" BlurRadius="3" Opacity="0.2" Color="#5D4037"/>
                            </Border.Effect>
                            <ScrollViewer x:Name="PART_ContentHost"
                                         Style="{StaticResource CustomScrollViewerStyle}"
                                         Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border BorderBrush="#D7CCC8" BorderThickness="1" Margin="10" CornerRadius="5" Background="#F5E9D9">
        <Border.Effect>
            <DropShadowEffect Color="#5D4037" ShadowDepth="2" BlurRadius="5" Opacity="0.2"/>
        </Border.Effect>

        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 标题区域 -->
            <TextBlock Grid.Row="0" Text="添加咖啡数据" FontSize="20" FontWeight="Bold"
                       Foreground="#4E342E" Margin="0,0,0,20" HorizontalAlignment="Center"/>

            <!-- 表单区域 -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto"
                         Style="{StaticResource CustomScrollViewerStyle}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- 名称 (占满整行) -->
                    <StackPanel Grid.Row="0" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,0,10">
                        <TextBlock Text="名称" />
                        <TextBox x:Name="txtName" Style="{StaticResource FlatTextBoxStyle}"/>
                    </StackPanel>

                    <!-- 价格 -->
                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="价格" />
                        <TextBox x:Name="txtPrice" Text="0" Style="{StaticResource FlatTextBoxStyle}"/>
                    </StackPanel>

                    <!-- 重量 -->
                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,0,0,0">
                        <TextBlock Text="重量" />
                        <TextBox x:Name="txtWeight" Text="1000" Style="{StaticResource FlatTextBoxStyle}"/>
                    </StackPanel>

                    <!-- 状态 -->
                    <StackPanel Grid.Row="2" Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="状态" />
                        <ComboBox x:Name="cmbStatus" SelectedIndex="0" Style="{StaticResource FlatComboBoxStyle}">
                            <ComboBoxItem Content="在路上" Tag="road"/>
                            <ComboBoxItem Content="已到货" Tag="new"/>
                            <ComboBoxItem Content="进行中" Tag="ing"/>
                            <ComboBoxItem Content="已完成" Tag="over"/>
                        </ComboBox>
                    </StackPanel>
                    <!-- 处理方式 -->
                    <StackPanel Grid.Row="2" Grid.Column="1" Margin="10,0,0,0">
                        <TextBlock Text="处理方式" />
                        <ComboBox x:Name="cmbProcessingMethod" SelectedIndex="0" Style="{StaticResource FlatComboBoxStyle}">
                            <ComboBoxItem Content="日晒" />
                            <ComboBoxItem Content="水洗" />
                            <ComboBoxItem Content="蜜处理" />
                            <ComboBoxItem Content="厌氧发酵" />
                        </ComboBox>
                    </StackPanel>

                    <!-- 链接 (占满整行) -->
                    <StackPanel Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,0,10">
                        <TextBlock Text="链接" />
                        <TextBox x:Name="txtLink" Style="{StaticResource FlatTextBoxStyle}"/>
                    </StackPanel>

                    <!-- 国家 -->
                    <StackPanel Grid.Row="4" Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="国家" />
                        <TextBox x:Name="txtCountry" Style="{StaticResource FlatTextBoxStyle}"/>
                    </StackPanel>

                    <!-- 日期 -->
                    <StackPanel Grid.Row="4" Grid.Column="1" Margin="10,0,0,0">
                        <TextBlock Text="日期" />
                        <TextBlock x:Name="txtDate" VerticalAlignment="Center" Margin="8,0,0,0"/>
                        <DatePicker x:Name="datePicker" SelectedDate="{Binding SelectedDate, Mode=TwoWay}"
                                Style="{StaticResource FlatDatePickerStyle}"/>
                    </StackPanel>

                    <!-- 资料 (占满整行) -->
                    <StackPanel Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="2" Margin="0,0,0,10">
                        <TextBlock Text="资料" />
                        <TextBox x:Name="txtInfo" Height="100" TextWrapping="Wrap" AcceptsReturn="True"
                             VerticalContentAlignment="Top" VerticalScrollBarVisibility="Auto"
                             Style="{StaticResource CustomTextBoxStyle}"/>
                    </StackPanel>
                </Grid>
            </ScrollViewer>

            <!-- 按钮区域 -->
            <Grid Grid.Row="2" Margin="0,10,0,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <Button x:Name="CancelButton" Content="取消" Grid.Column="0"
                    Background="#BCAAA4" Click="BtnCancel_Click"/>

                <Button x:Name="ConfirmButton" Content="确认" Grid.Column="1"
                    Background="#795548" Click="BtnConfirm_Click"/>
            </Grid>


        </Grid>
    </Border>
</Window>


