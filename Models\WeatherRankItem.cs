using System;

namespace WpfApp.Models
{
    /// <summary>
    /// 天气排行项数据模型
    /// </summary>
    public class WeatherRankItem
    {
        /// <summary>
        /// 排名
        /// </summary>
        public int Rank { get; set; }

        /// <summary>
        /// 城市名称
        /// </summary>
        public string CityName { get; set; } = string.Empty;

        /// <summary>
        /// 温度值（摄氏度）
        /// </summary>
        public int Temperature { get; set; }

        /// <summary>
        /// 是否为高温（用于区分颜色显示）
        /// </summary>
        public bool IsHot { get; set; }

        /// <summary>
        /// 省份或地区
        /// </summary>
        public string Province { get; set; } = string.Empty;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }

        /// <summary>
        /// 数据来源
        /// </summary>
        public string Source { get; set; } = "中国天气网";

        /// <summary>
        /// 构造函数
        /// </summary>
        public WeatherRankItem()
        {
            UpdateTime = DateTime.Now;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="rank">排名</param>
        /// <param name="cityName">城市名称</param>
        /// <param name="temperature">温度</param>
        public WeatherRankItem(int rank, string cityName, int temperature)
        {
            Rank = rank;
            CityName = cityName;
            Temperature = temperature;
            IsHot = temperature > 0; // 0度以上为高温，0度以下为低温
            UpdateTime = DateTime.Now;
        }
    }
}
