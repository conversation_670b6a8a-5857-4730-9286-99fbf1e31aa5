using System;
using System.Threading.Tasks;
using WpfApp.Services;

namespace WpfApp
{
    /// <summary>
    /// 测试澎湃历史新闻功能
    /// </summary>
    class TestWeatherRank
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== 澎湃历史新闻数据获取测试 ===");
            Console.WriteLine("测试多种数据获取方法：HTML解析、正则表达式、模拟数据");
            Console.WriteLine();

            var newsService = new NewsService();

            try
            {
                Console.WriteLine("开始获取澎湃历史新闻数据...");
                Console.WriteLine("正在尝试从澎湃新闻获取私家历史频道数据...");

                var cultureNewsList = await newsService.GetCultureNewsAsync();

                Console.WriteLine($"\n✅ 成功获取 {cultureNewsList.Count} 条文化新闻数据：");
                Console.WriteLine();

                Console.WriteLine("排名\t标题\t\t\t\t作者\t\t发布时间\t\t数据来源");
                Console.WriteLine("".PadRight(120, '-'));

                foreach (var item in cultureNewsList)
                {
                    var titleDisplay = item.Title.Length > 30 ? item.Title.Substring(0, 30) + "..." : item.Title.PadRight(33);
                    var authorDisplay = (item.Author ?? "").PadRight(10);
                    var timeDisplay = item.PublishTime.ToString("MM-dd HH:mm");
                    var sourceDisplay = (item.Source ?? "").PadRight(20);

                    Console.WriteLine($"{item.Rank}\t{titleDisplay}\t{authorDisplay}\t{timeDisplay}\t\t{sourceDisplay}");
                }

                Console.WriteLine();
                Console.WriteLine("📊 数据统计：");
                var realDataCount = cultureNewsList.Count(x => x.Source != null && !x.Source.Contains("模拟"));
                var mockDataCount = cultureNewsList.Count - realDataCount;
                Console.WriteLine($"   - 真实数据: {realDataCount} 条");
                Console.WriteLine($"   - 模拟数据: {mockDataCount} 条");

                if (realDataCount > 0)
                {
                    Console.WriteLine($"   - 最新文章: {cultureNewsList.OrderByDescending(x => x.PublishTime).First().Title}");
                    Console.WriteLine($"   - 最早文章: {cultureNewsList.OrderBy(x => x.PublishTime).First().Title}");
                }

                Console.WriteLine();
                Console.WriteLine("✅ 测试完成！");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"详细错误信息:");
                Console.WriteLine(ex.ToString());
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
