﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net.Http;
using System.Security.Policy;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace WpfApp
{
    public class WeatherInfo
    {
        public string City { get; set; }
        public string Weather { get; set; }
        public string Temperature { get; set; }
        public string WeatherCode { get; set; }
        public string Humidity { get; set; }
        public string Wind { get; set; }
        public string UpdateTime { get; set; }
        public List<DailyForecast> Forecast { get; set; }
    }

    public class DailyForecast
    {
        public string Date { get; set; }
        public string DayWeather { get; set; }
        public string NightWeather { get; set; }
        public string DayTemp { get; set; }
        public string NightTemp { get; set; }
        public string DayWeatherCode { get; set; }
        public string NightWeatherCode { get; set; }
        public string Humidity { get; set; }
        public string Sunrise { get; set; }
        public string Sunset { get; set; }
        public string IconDay { get; set; }
        public string TextDay { get; set; }
        public string TempMax { get; set; }
        public string TempMin { get; set; }
        public string WindDirDay { get; set; }
        public string WindScaleDay { get; set; }
        public string Precip { get; set; }
    }

    public class WeatherService
    {
        private readonly HttpClient _httpClient;

        // API URLs
        private const string PRIMARY_API_URL = "http://t.weather.itboy.net/api/weather/city/{0}";
        private const string BACKUP_API_URL = "http://wthrcdn.etouch.cn/weather_mini?citykey={0}";
        private const string FORECAST_API_URL = "http://wthrcdn.etouch.cn/WeatherApi?citykey={0}";

        // 城市代码映射
        private readonly Dictionary<string, string> _cityCodes = new Dictionary<string, string>
        {
            { "北京", "101010100" },
            { "上海", "101020100" },
            { "广州", "101280101" },
            { "深圳", "101280601" },
            { "杭州", "101210101" },
            { "南京", "101190101" },
            { "武汉", "101200101" },
            { "成都", "101270101" },
            { "重庆", "101040100" },
            { "西安", "101110101" },
            { "天津", "101030100" },
            { "苏州", "101190401" },
            { "长沙", "101250101" },
            { "郑州", "101180101" },
            { "青岛", "101120201" },
            { "大连", "101070201" },
            { "宁波", "101210401" },
            { "厦门", "101230201" },
            { "福州", "101230101" },
            { "南昌", "101240101" },
            { "合肥", "101220101" },
            { "济南", "101120101" },
            { "太原", "101100101" },
            { "石家庄", "101090101" },
            { "哈尔滨", "101050101" },
            { "长春", "101060101" },
            { "沈阳", "101070101" },
            
            { "庆城", "101160401" },
            { "根河", "101081015" },
            { "漠河", "101050703" },
            { "呼伦贝尔", "101081001" },

            //广东省
            
            { "韶关", "101280201" },
            { "惠州", "101280301" },
            { "梅州", "101280401" },
            { "汕头", "101280501" },
            
            { "珠海", "101280701" },
            { "佛山", "101280800" },
            { "肇庆", "101280901" },
            { "湛江", "101281001" },
            { "江门", "101281101" },
            { "河源", "101281201" },
            { "清远", "101281301" },
            { "云浮", "101281401" },
            { "潮州", "101281501" },
            { "东莞", "101281601" },
            { "中山", "101281701" },
            { "阳江", "101281801" },
            { "揭阳", "101281901" },
            { "茂名", "101282001" },
            { "汕尾", "101282101" },


            //四川省
            
            { "攀枝花", "101270201" },
            { "自贡", "101270301" },
            { "绵阳", "101270401" },
            { "南充", "101270501" },
            { "达州", "101270601" },
            { "遂宁", "101270701" },
            { "广安", "101270801" },
            { "巴中", "101270901" },
            { "泸州", "101271001" },
            { "宜宾", "101271101" },
            { "内江", "101271201" },
            { "资阳", "101271301" },
            { "乐山", "101271401" },
            { "眉山", "101271501" },
            { "凉山", "101271601" },
            { "雅安", "101271701" },
            { "甘孜", "101271801" },
            { "阿坝", "101271901" },
            { "德阳", "101272001" },
            { "广元", "101272101" },


            //贵州省
            { "贵阳", "101260101" },
            { "遵义", "101260201" },
            { "安顺", "101260301" },
            { "黔南", "101260401" },
            { "黔东南", "101260501" },
            { "铜仁", "101260601" },
            { "毕节", "101260701" },
            { "六盘水", "101260801" },
            { "黔西南", "101260901" },


            //陕西省
            
            { "咸阳", "101110200" },
            { "延安", "101110300" },
            { "榆林", "101110401" },
            { "渭南", "101110501" },
            { "商洛", "101110601" },
            { "安康", "101110701" },
            { "汉中", "101110801" },
            { "宝鸡", "101110901" },
            { "铜川", "101111001" },
            { "杨凌", "101111101" },

            //新疆维吾尔自治区
            { "乌鲁木齐", "101130101" },
            { "克拉玛依", "101130201" },
            { "石河子", "101130301" },
            { "昌吉", "101130401" },
            { "吐鲁番", "101130501" },
            { "巴音郭楞", "101130601" },
            { "阿拉尔", "101130701" },
            { "阿克苏", "101130801" },
            { "喀什", "101130901" },
            { "伊犁", "101131001" },
            { "塔城", "101131101" },
            { "哈密", "101131201" },
            { "和田", "101131301" },
            { "阿勒泰", "101131401" },
            { "克州", "101131501" },
            { "博尔塔拉", "101131601" },
            { "图木舒克", "101131701" },
            { "五家渠", "101131801" },
            { "铁门关", "101131901" },

            //青海省
            { "西宁", "101150101" },
            { "海东", "101150201" },
            { "黄南", "101150301" },
            { "海南", "101150401" },
            { "果洛", "101150501" },
            { "玉树", "101150601" },
            { "海西", "101150701" },
            { "海北", "101150801" },

            //甘肃省
            { "兰州", "101160101" },
            { "庆阳", "101160401" },
            { "西峰", "101160402" },
            { "环县", "101160403" },
            { "定西", "101160201" },
            { "平凉", "101160301" },
            { "武威", "101160501" },
            { "金昌", "101160601" },
            { "张掖", "101160701" },
            { "酒泉", "101160801" },
            { "天水", "101160901" },
            { "陇南", "101161001" },
            { "临夏", "101161101" },
            { "甘南", "101161201" },
            { "白银", "101161301" },
            { "嘉峪关", "101161401" },


            { "昆明", "101290101" },
            { "腾冲", "101290506" },
            { "曲靖", "101290401" },
            { "保山", "101290501" },
            { "文山", "101290601" },
            { "玉溪", "101290701" },
            { "楚雄", "101290801" },
            { "普洱", "101290901" },
            { "昭通", "101291001" },
            { "临沧", "101291101" },
            { "怒江", "101291201" },
            { "迪庆", "101291301" },
            { "丽江", "101291401" },
            { "德宏", "101291501" },
            { "西双版纳", "101291601" },
            { "大理", "101290201" },
            { "红河", "101290301" },

        };

        public WeatherService()
        {
            _httpClient = new HttpClient();
            _httpClient.Timeout = TimeSpan.FromSeconds(10);
            // 设置默认请求头，指定接受的内容类型和编码
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
            _httpClient.DefaultRequestHeaders.Add("Accept-Charset", "utf-8");
        }

        // 获取城市代码
        public string GetCityCode(string city)
        {
            if (_cityCodes.TryGetValue(city, out string code))
            {
                return code;
            }
            return null;
        }

        // 获取天气信息
        public async Task<WeatherInfo> GetWeatherAsync(string city)
        {
            try
            {
                // 尝试使用主要API
                return await GetWeatherFromBackupApiAsync(city); 
            }
            catch (Exception ex)
            {
                Console.WriteLine($"主API错误: {ex.Message}");
                try
                {
                    // 如果主要API失败，尝试备用API
                    return await GetWeatherFromPrimaryApiAsync(city);
                }
                catch (Exception backupEx)
                {
                    Console.WriteLine($"备用API错误: {backupEx.Message}");
                    try
                    {
                        // 如果备用API也失败，尝试获取预报API
                        return await GetWeatherFromForecastApiAsync(city);
                    }
                    catch (Exception forecastEx)
                    {
                        Console.WriteLine($"预报API错误: {forecastEx.Message}");
                        // 所有API都失败，返回错误信息
                        return new WeatherInfo
                        {
                            City = "网络错误",
                            Weather = "请检查网络连接",
                            Temperature = "N/A",
                            WeatherCode = "99"
                        };
                    }
                }
            }
        }

        // 从主要API获取天气
        private async Task<WeatherInfo> GetWeatherFromPrimaryApiAsync(string city)
        {
            try
            {
                string cityCode = GetCityCode(city);
                if (string.IsNullOrEmpty(cityCode))
                {
                    return new WeatherInfo
                    {
                        City = "获取失败",
                        Weather = $"API错误: 无法获取 {city} 的天气数据",
                        Temperature = "N/A",
                        WeatherCode = "99"
                    };
                }

                string url = string.Format(PRIMARY_API_URL, cityCode);
                string response = await _httpClient.GetStringAsync(url);
                
                // 解析JSON响应
                using (JsonDocument doc = JsonDocument.Parse(response))
                {
                    JsonElement root = doc.RootElement;
                    
                    // 检查API返回状态
                    if (root.TryGetProperty("status", out JsonElement statusElement) && 
                        statusElement.GetInt32() != 200)
                    {
                        throw new Exception("API返回错误状态");
                    }

                    // 获取城市名称
                    string cityName = root.GetProperty("cityInfo").GetProperty("city").GetString();
                    
                    // 获取数据
                    JsonElement data = root.GetProperty("data");
                    
                    // 获取当前温度
                    string tempnow = data.GetProperty("wendu").GetString();
                    
                    // 获取今日天气类型
                    string weather = data.GetProperty("forecast")[0].GetProperty("type").GetString();
                    
                    // 根据天气文本确定天气代码
                    string weatherCode = GetWeatherCodeFromText(weather);

                    // 获取预报数据
                    JsonElement forecastElement = data.GetProperty("forecast");
                    List<DailyForecast> forecasts = new List<DailyForecast>();
                    
                    foreach (JsonElement day in forecastElement.EnumerateArray())
                    {
                        string dayWeather = day.GetProperty("type").GetString();
                        
                        // 提取日期并格式化为"MM-DD"形式，更易读
                        string dateStr = day.GetProperty("date").GetString();
                        string formattedDate = dateStr;
                        
                        // 尝试解析并格式化日期
                        if (DateTime.TryParse(dateStr, out DateTime date))
                        {
                            formattedDate = date.ToString("MM-dd") + " " + GetChineseWeekdayName(date); // 日期加星期几
                        }
                        
                        // 获取湿度、日出和日落时间
                        string humidity = day.TryGetProperty("humidity", out JsonElement humidityElement) 
                            ? humidityElement.GetString() + "%" 
                            : "N/A";
                        
                        string sunrise = day.TryGetProperty("sunrise", out JsonElement sunriseElement) 
                            ? sunriseElement.GetString() 
                            : "N/A";
                        
                        string sunset = day.TryGetProperty("sunset", out JsonElement sunsetElement) 
                            ? sunsetElement.GetString() 
                            : "N/A";
                        
                        forecasts.Add(new DailyForecast
                        {
                            Date = formattedDate,
                            DayWeather = dayWeather,
                            NightWeather = dayWeather, // 该API没有分白天/夜间天气
                            DayTemp = day.GetProperty("high").GetString().Replace("高温 ", ""),
                            NightTemp = day.GetProperty("low").GetString().Replace("低温 ", ""),
                            DayWeatherCode = GetWeatherCodeFromText(dayWeather),
                            NightWeatherCode = GetWeatherCodeFromText(dayWeather),
                            // 设置新属性
                            Humidity = humidity,
                            Sunrise = sunrise,
                            Sunset = sunset,
                            // 设置新增的属性
                            IconDay = GetWeatherCodeFromText(dayWeather),
                            TextDay = dayWeather,
                            TempMax = day.GetProperty("high").GetString().Replace("高温 ", ""),
                            TempMin = day.GetProperty("low").GetString().Replace("低温 ", ""),
                            WindDirDay = day.TryGetProperty("windDir", out JsonElement windDirElement) ? windDirElement.GetString() : "N/A",
                            WindScaleDay = day.TryGetProperty("windScale", out JsonElement windScaleElement) ? windScaleElement.GetString() : "N/A",
                            Precip = day.TryGetProperty("precip", out JsonElement precipElement) ? precipElement.GetString() : "0"
                        });
                    }
                    
                    return new WeatherInfo
                    {
                        City = cityName,
                        Weather = weather,
                        Temperature = tempnow,
                        WeatherCode = weatherCode,
                        Forecast = forecasts
                    };
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"主API处理错误: {ex.Message}");
            }
        }

        // 从备用API获取天气
        private async Task<WeatherInfo> GetWeatherFromBackupApiAsync(string city)
        {
            // 获取城市代码
            string cityCode = GetCityCode(city);
            if (string.IsNullOrEmpty(cityCode))
            {
                throw new Exception($"未找到城市 {city} 的代码");
            }
            
            // 和风天气API配置
            string apiKey = "9aaf5d6a287f463b82daf056daeb4033";
            string baseUrl = "https://devapi.qweather.com/v7/weather";
            
            // 构建实时天气请求URL
            string nowUrl = $"{baseUrl}/now?location={cityCode}&key={apiKey}";
            
            // 构建7天预报请求URL
            string forecastUrl = $"{baseUrl}/7d?location={cityCode}&key={apiKey}";
            
            try
            {
                // 获取实时天气数据 - 处理gzip压缩的响应
                HttpResponseMessage nowResponse = await _httpClient.GetAsync(nowUrl);
                nowResponse.EnsureSuccessStatusCode();
                
                // 获取预报数据 - 处理gzip压缩的响应
                HttpResponseMessage forecastResponse = await _httpClient.GetAsync(forecastUrl);
                forecastResponse.EnsureSuccessStatusCode();
                
                // 解压并读取实时天气数据
                string nowJsonString = await DecompressResponseAsync(nowResponse);
                
                // 解压并读取预报数据
                string forecastJsonString = await DecompressResponseAsync(forecastResponse);
                
                // 解析JSON响应
                using (JsonDocument nowDoc = JsonDocument.Parse(nowJsonString))
                using (JsonDocument forecastDoc = JsonDocument.Parse(forecastJsonString))
                {
                    JsonElement nowRoot = nowDoc.RootElement;
                    JsonElement forecastRoot = forecastDoc.RootElement;
                    
                    // 检查API返回状态
                    string nowStatus = nowRoot.GetProperty("code").GetString();
                    string forecastStatus = forecastRoot.GetProperty("code").GetString();
                    
                    if (nowStatus != "200" || forecastStatus != "200")
                    {
                        throw new Exception($"API返回错误状态: 实时天气状态 {nowStatus}, 预报状态 {forecastStatus}");
                    }
                    
                    // 获取实时天气数据
                    JsonElement nowData = nowRoot.GetProperty("now");
                    
                    string weather = nowData.GetProperty("text").GetString();
                    string temp = nowData.GetProperty("temp").GetString();
                    string weatherCode = nowData.GetProperty("icon").GetString();
                    string wind = nowData.GetProperty("windDir").GetString();
                    string currentHumidity = nowData.GetProperty("humidity").GetString() + '%';
                    string updateTime = nowData.GetProperty("obsTime").GetString();

                    // 获取预报数据
                    JsonElement dailyData = forecastRoot.GetProperty("daily");
                    List<DailyForecast> forecasts = new List<DailyForecast>();
                    
                    foreach (JsonElement day in dailyData.EnumerateArray())
                    {
                        // 提取日期并格式化为"MM-DD"形式，更易读
                        string dateStr = day.GetProperty("fxDate").GetString();
                        string formattedDate = dateStr;
                        
                        // 尝试格式化日期（如果可能）
                        if (DateTime.TryParse(dateStr, out DateTime date))
                        {
                            formattedDate = date.ToString("MM-dd") + " " + GetChineseWeekdayName(date); // 日期加星期几
                        }
                        
                        // 获取湿度、日出和日落时间
                        string humidity = day.TryGetProperty("humidity", out JsonElement humidityElement) 
                            ? humidityElement.GetString() + "%" 
                            : "N/A";
                        
                        string sunrise = day.TryGetProperty("sunrise", out JsonElement sunriseElement) 
                            ? sunriseElement.GetString() 
                            : "N/A";
                        
                        string sunset = day.TryGetProperty("sunset", out JsonElement sunsetElement) 
                            ? sunsetElement.GetString() 
                            : "N/A";
                        
                        forecasts.Add(new DailyForecast
                        {
                            Date = formattedDate,
                            DayWeather = day.GetProperty("textDay").GetString(),
                            NightWeather = day.GetProperty("textNight").GetString(),
                            DayTemp = day.GetProperty("tempMax").GetString() + "℃",
                            NightTemp = day.GetProperty("tempMin").GetString() + "℃",
                            DayWeatherCode = day.GetProperty("iconDay").GetString(),
                            NightWeatherCode = day.GetProperty("iconNight").GetString(),
                            // 设置新属性
                            Humidity = humidity,
                            Sunrise = sunrise,
                            Sunset = sunset,
                            // 设置新增的属性
                            IconDay = day.GetProperty("iconDay").GetString(),
                            TextDay = day.GetProperty("textDay").GetString(),
                            TempMax = day.GetProperty("tempMax").GetString() + "℃",
                            TempMin = day.GetProperty("tempMin").GetString() + "℃",
                            WindDirDay = day.TryGetProperty("windDirDay", out JsonElement windDirElement) ? windDirElement.GetString() : "N/A",
                            WindScaleDay = day.TryGetProperty("windScaleDay", out JsonElement windScaleElement) ? windScaleElement.GetString() : "N/A",
                            Precip = day.TryGetProperty("precip", out JsonElement precipElement) ? precipElement.GetString() : "0"
                        });
                    }

                    return new WeatherInfo
                    {
                        City = city,
                        Weather = weather,
                        Temperature = temp + "℃",
                        WeatherCode = weatherCode,
                        
                        Humidity = currentHumidity,
                        Wind = wind,
                        UpdateTime = updateTime,

                        Forecast = forecasts
                    };
                }
            }
            catch (HttpRequestException ex)
            {
                throw new Exception($"和风天气API请求失败: {ex.Message}");
            }
            catch (JsonException ex)
            {
                throw new Exception($"和风天气API返回数据解析失败: {ex.Message}");
            }
        }

        // 解压缩HTTP响应内容
        private async Task<string> DecompressResponseAsync(HttpResponseMessage response)
        {
            // Get the response stream
            using (var responseStream = await response.Content.ReadAsStreamAsync())
            {
                // Check if the content is gzip-encoded
                if (response.Content.Headers.ContentEncoding.Contains("gzip"))
                {
                    // Use GZipStream to decompress
                    using (var decompressedStream = new GZipStream(responseStream, CompressionMode.Decompress))
                    using (var reader = new StreamReader(decompressedStream, Encoding.UTF8))
                    {
                        return await reader.ReadToEndAsync();
                    }
                }
                else
                {
                    // If not gzip-compressed, read directly
                    using (var reader = new StreamReader(responseStream, Encoding.UTF8))
                    {
                        return await reader.ReadToEndAsync();
                    }
                }
            }
        }

        // 从预报API获取天气
        private async Task<WeatherInfo> GetWeatherFromForecastApiAsync(string city)
        {
            // 获取城市代码
            string cityCode = GetCityCode(city);
            if (string.IsNullOrEmpty(cityCode))
            {
                throw new Exception($"未找到城市 {city} 的代码");
            }
            
            string url = string.Format(FORECAST_API_URL, cityCode);
            string response = await _httpClient.GetStringAsync(url);
            
            // 解析XML响应 (简化处理，实际应使用XML解析)
            // 这里使用简单的字符串处理作为示例
            string cityName = ExtractXmlValue(response, "city");
            string weather = ExtractXmlValue(response, "weather");
            string temp = ExtractXmlValue(response, "temp");
            
            // 根据天气文本确定天气代码
            string code = GetWeatherCodeFromText(weather);
            
            // 简单提取预报数据
            List<DailyForecast> forecasts = ExtractForecastFromXml(response);
            
            return new WeatherInfo
            {
                City = cityName,
                Weather = weather,
                Temperature = temp,
                WeatherCode = code,
                Forecast = forecasts
            };
        }

        // 从XML中提取值 (简化实现)
        private string ExtractXmlValue(string xml, string tag)
        {
            string startTag = $"<{tag}>";
            string endTag = $"</{tag}>";
            int startIndex = xml.IndexOf(startTag) + startTag.Length;
            int endIndex = xml.IndexOf(endTag, startIndex);
            
            if (startIndex >= 0 && endIndex >= 0)
            {
                return xml.Substring(startIndex, endIndex - startIndex);
            }
            
            return "N/A";
        }

        // 从XML中提取预报数据 (简化实现)
        private List<DailyForecast> ExtractForecastFromXml(string xml)
        {
            List<DailyForecast> forecasts = new List<DailyForecast>();
            
            // 提取预报部分
            string forecastStartTag = "<forecast>";
            string forecastEndTag = "</forecast>";
            int forecastStart = xml.IndexOf(forecastStartTag);
            int forecastEnd = xml.IndexOf(forecastEndTag) + forecastEndTag.Length;
            
            if (forecastStart >= 0 && forecastEnd >= 0)
            {
                string forecastXml = xml.Substring(forecastStart, forecastEnd - forecastStart);
                
                // 提取每天的预报
                string dayStartTag = "<weather>";
                string dayEndTag = "</weather>";
                int dayStart = forecastXml.IndexOf(dayStartTag);
                
                while (dayStart >= 0)
                {
                    int dayEnd = forecastXml.IndexOf(dayEndTag, dayStart) + dayEndTag.Length;
                    string dayXml = forecastXml.Substring(dayStart, dayEnd - dayStart);
                    
                    string date = ExtractXmlValue(dayXml, "date");
                    string dayWeather = ExtractXmlValue(dayXml, "weather");
                    string dayTemp = ExtractXmlValue(dayXml, "temp");
                    string nightTemp = ExtractXmlValue(dayXml, "low");
                    
                    // 尝试格式化日期
                    string formattedDate = date;
                    if (DateTime.TryParse(date, out DateTime dateObj))
                    {
                        formattedDate = dateObj.ToString("MM-dd") + " " + GetChineseWeekdayName(dateObj); // 日期加星期几
                    }
                    
                    // 为湿度、日出和日落创建默认值
                    string humidity = "65%"; // 设置默认湿度值
                    string sunrise = "06:45"; // 设置默认日出时间
                    string sunset = "18:30"; // 设置默认日落时间
                    
                    // 尝试根据日期设置合理的日出日落时间
                    if (DateTime.TryParse(date, out DateTime dateTime))
                    {
                        // 根据月份设置大致的日出日落时间
                        int month = dateTime.Month;
                        if (month >= 4 && month <= 9) // 夏季时间
                        {
                            sunrise = "05:30";
                            sunset = "19:30";
                        }
                        else // 冬季时间
                        {
                            sunrise = "06:45";
                            sunset = "17:30";
                        }
                    }
                    
                    forecasts.Add(new DailyForecast
                    {
                        Date = formattedDate,
                        DayWeather = dayWeather,
                        NightWeather = dayWeather,
                        DayTemp = dayTemp,
                        NightTemp = nightTemp,
                        DayWeatherCode = GetWeatherCodeFromText(dayWeather),
                        NightWeatherCode = GetWeatherCodeFromText(dayWeather),
                        Humidity = humidity,
                        Sunrise = sunrise,
                        Sunset = sunset
                    });
                    
                    dayStart = dayEnd;
                }
            }
            
            return forecasts;
        }

        // 添加获取中文星期几名称的辅助方法
        private string GetChineseWeekdayName(DateTime date)
        {
            string[] weekdays = { "周日", "周一", "周二", "周三", "周四", "周五", "周六" };
            return weekdays[(int)date.DayOfWeek];
        }

        // 根据天气文本获取天气代码
        private string GetWeatherCodeFromText(string weatherText)
        {
            // 简化的天气代码映射
            if (weatherText.Contains("晴")) return "00";
            if (weatherText.Contains("多云")) return "01";
            if (weatherText.Contains("阴")) return "02";
            if (weatherText.Contains("雨")) return "07";
            if (weatherText.Contains("雪")) return "13";
            if (weatherText.Contains("雾")) return "18";
            if (weatherText.Contains("霾")) return "53";
            
            // 默认代码
            return "99";
        }
    }
}