﻿#pragma checksum "..\..\..\MonthlyStatsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "A1517D7AEFFF92A974CAC22F55F66BF4F1C99FF3"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace WpfAdmin {
    
    
    /// <summary>
    /// MonthlyStatsWindow
    /// </summary>
    public partial class MonthlyStatsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 23 "..\..\..\MonthlyStatsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrevMonthButton;
        
        #line default
        #line hidden
        
        
        #line 25 "..\..\..\MonthlyStatsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MonthYearText;
        
        #line default
        #line hidden
        
        
        #line 28 "..\..\..\MonthlyStatsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NextMonthButton;
        
        #line default
        #line hidden
        
        
        #line 34 "..\..\..\MonthlyStatsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid CalendarGrid;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\MonthlyStatsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid StatsGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/WpfAdmin;V1.0.0.0;component/monthlystatswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MonthlyStatsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PrevMonthButton = ((System.Windows.Controls.Button)(target));
            
            #line 24 "..\..\..\MonthlyStatsWindow.xaml"
            this.PrevMonthButton.Click += new System.Windows.RoutedEventHandler(this.PrevMonth_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MonthYearText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.NextMonthButton = ((System.Windows.Controls.Button)(target));
            
            #line 29 "..\..\..\MonthlyStatsWindow.xaml"
            this.NextMonthButton.Click += new System.Windows.RoutedEventHandler(this.NextMonth_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CalendarGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 5:
            this.StatsGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 6:
            
            #line 88 "..\..\..\MonthlyStatsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Close_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

