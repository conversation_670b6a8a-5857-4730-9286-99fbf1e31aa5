﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace WpfApp
{
    public partial class MemoWindow : Window
    {
        private readonly CoffeeItem coffeeItem;
        private const int TITLE_HEIGHT = 60;
        private const int FOOTER_HEIGHT = 50;
        private const int MEMO_MIN_HEIGHT = 200;
        private const int WINDOW_PADDING = 40;

        public MemoWindow(CoffeeItem coffeeItem)
        {
            InitializeComponent();
            this.coffeeItem = coffeeItem;

            // 设置标题
            TitleTextBlock.Text = $"{coffeeItem.Name ?? "咖啡详情"}";

            // 加载图片
            try
            {
                if (!string.IsNullOrEmpty(coffeeItem.Link))
                {
                    BitmapImage bitmapImage = new BitmapImage();
                    bitmapImage.BeginInit();
                    bitmapImage.UriSource = new Uri(coffeeItem.Link, UriKind.RelativeOrAbsolute);
                    bitmapImage.CacheOption = BitmapCacheOption.OnLoad; // 确保图片完全加载
                    bitmapImage.EndInit();
                    
                    if (bitmapImage.IsDownloading)
                    {
                        // 如果图片正在下载，添加下载完成事件
                        bitmapImage.DownloadCompleted += (s, e) => AdjustWindowSizeBasedOnImage(bitmapImage);
                    }
                    else
                    {
                        // 如果图片已经加载完成，直接调整窗口大小
                        AdjustWindowSizeBasedOnImage(bitmapImage);
                    }
                    
                    CoffeeImage.Source = bitmapImage;
                }
                else
                {
                    // 如果没有图片，设置一个默认图片或隐藏图片区域
                    CoffeeImage.Visibility = Visibility.Collapsed;
                }
            }
            catch (Exception)
            {
                CoffeeImage.Visibility = Visibility.Collapsed;
            }

            // 设置HTML内容 - 需要设置基本样式并将Memo内容放入HTML
            string memoContent = coffeeItem.Memo ?? "没有详细信息";

            string htmlContent = $@"
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset=""UTF-8"">
                <style>
                    body {{
                        font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Arial, sans-serif;
                        margin: 20px;
                        padding: 0;
                        color: #ffffff;
                        background-color: #121a30;
                        font-size: 16px;
                        line-height: 1.8;
                        text-rendering: optimizeLegibility;
                        -webkit-font-smoothing: antialiased;
                    }}

                    h1, h2, h3, h4, h5, h6 {{
                        color: #4A90E2;
                        font-weight: 600;
                        margin: 20px 0 15px 0;
                        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
                    }}

                    h1 {{ font-size: 28px; }}
                    h2 {{ font-size: 24px; }}
                    h3 {{ font-size: 20px; }}
                    h4 {{ font-size: 18px; }}
                    h5 {{ font-size: 16px; }}
                    h6 {{ font-size: 14px; }}

                    p {{
                        color: #e8e8e8;
                        margin: 12px 0;
                        text-align: justify;
                    }}

                    strong, b {{
                        color: #ffffff;
                        font-weight: 600;
                    }}

                    em, i {{
                        color: #b8d4f0;
                        font-style: italic;
                    }}

                    ul, ol {{
                        color: #e8e8e8;
                        margin: 15px 0;
                        padding-left: 25px;
                    }}

                    li {{
                        margin: 8px 0;
                        line-height: 1.6;
                    }}

                    table {{
                        width: 100%;
                        border-collapse: collapse;
                        margin: 20px 0;
                        background-color: rgba(26, 39, 71, 0.3);
                        border-radius: 8px;
                        overflow: hidden;
                    }}

                    th, td {{
                        border: 1px solid #4A5AE8;
                        padding: 12px 15px;
                        text-align: left;
                        color: #ffffff;
                    }}

                    th {{
                        background-color: #1a2747;
                        font-weight: 600;
                        color: #4A90E2;
                    }}

                    td {{
                        background-color: rgba(18, 26, 48, 0.5);
                    }}

                    blockquote {{
                        border-left: 4px solid #4A90E2;
                        margin: 20px 0;
                        padding: 15px 20px;
                        background-color: rgba(26, 39, 71, 0.3);
                        color: #d0d0d0;
                        font-style: italic;
                        border-radius: 0 8px 8px 0;
                    }}

                    code {{
                        background-color: rgba(74, 144, 226, 0.1);
                        color: #4A90E2;
                        padding: 2px 6px;
                        border-radius: 4px;
                        font-family: 'Consolas', 'Monaco', monospace;
                        font-size: 14px;
                    }}

                    pre {{
                        background-color: rgba(26, 39, 71, 0.5);
                        color: #e8e8e8;
                        padding: 15px;
                        border-radius: 8px;
                        overflow-x: auto;
                        font-family: 'Consolas', 'Monaco', monospace;
                        font-size: 14px;
                        line-height: 1.4;
                        border: 1px solid #4A5AE8;
                    }}

                    a {{
                        color: #4A90E2;
                        text-decoration: none;
                        border-bottom: 1px dotted #4A90E2;
                        transition: all 0.3s ease;
                    }}

                    a:hover {{
                        color: #ffffff;
                        border-bottom-color: #ffffff;
                    }}

                    hr {{
                        border: none;
                        height: 2px;
                        background: linear-gradient(to right, transparent, #4A90E2, transparent);
                        margin: 30px 0;
                    }}
                </style>
            </head>
            <body>
                {memoContent}
            </body>
            </html>";

            MemoWebBrowser.NavigateToString(htmlContent);
        }

        private void AdjustWindowSizeBasedOnImage(BitmapImage image)
        {
            if (image == null) return;

            // 获取图片的实际尺寸
            double imageWidth = image.PixelWidth;
            double imageHeight = image.PixelHeight;

            // 计算合适的窗口尺寸，考虑到标题栏、底部按钮和边距
            double windowWidth = Math.Min(imageWidth + WINDOW_PADDING, MaxWidth);
            double windowHeight = Math.Min(imageHeight + TITLE_HEIGHT + MEMO_MIN_HEIGHT + FOOTER_HEIGHT, MaxHeight);

            // 确保窗口尺寸不小于最小值
            windowWidth = Math.Max(windowWidth, MinWidth);
            windowHeight = Math.Max(windowHeight, MinHeight);

            // 设置窗口尺寸
            Width = windowWidth;
            Height = windowHeight;

            // 调整图片容器的高度
            var rowDefinitions = ((Grid)CoffeeImage.Parent).RowDefinitions;
            if (rowDefinitions.Count > 1)
            {
                // 设置图片行的高度
                double imageRowHeight = Math.Min(imageHeight, MaxHeight - TITLE_HEIGHT - MEMO_MIN_HEIGHT - FOOTER_HEIGHT);
                rowDefinitions[1].Height = new GridLength(imageRowHeight);
            }
        }

        private void CoffeeImage_Loaded(object sender, RoutedEventArgs e)
        {
            // 图片加载完成后，再次检查是否需要调整窗口大小
            if (CoffeeImage.Source is BitmapImage bitmapImage && !bitmapImage.IsDownloading)
            {
                AdjustWindowSizeBasedOnImage(bitmapImage);
            }
        }

        private void MemoWebBrowser_Loaded(object sender, RoutedEventArgs e)
        {
            // 设置WebBrowser的背景色为深蓝色主题
            try
            {
                dynamic document = MemoWebBrowser.Document;
                if (document != null)
                {
                    document.body.style.backgroundColor = "#121a30";
                    document.documentElement.style.backgroundColor = "#121a30";
                    document.body.style.color = "white";
                }
            }
            catch
            {
                // 忽略可能的JavaScript错误
            }
        }

        private void Border_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.LeftButton == MouseButtonState.Pressed)
            {
                DragMove();
            }
        }

        private void MinimizeButton_Click(object sender, RoutedEventArgs e)
        {
            WindowState = WindowState.Minimized;
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
