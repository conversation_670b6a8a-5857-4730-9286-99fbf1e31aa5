<Window x:Class="WpfApp.NewsDetailWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:wv2="clr-namespace:Microsoft.Web.WebView2.Wpf;assembly=Microsoft.Web.WebView2.Wpf"
        Title="新闻详情" Height="700" Width="900"
        WindowStyle="None"
        AllowsTransparency="True"
        WindowStartupLocation="CenterOwner"
        Background="Transparent">

    <Window.Resources>
        <!-- 自定义滚动条拖拽块样式 -->
        <Style x:Key="CustomScrollBarThumbStyle" TargetType="{x:Type Thumb}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Thumb}">
                        <Grid>
                            <Border Background="#4A5568" CornerRadius="4" Opacity="0.8"/>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 自定义滚动条样式 -->
        <Style x:Key="CustomScrollBarStyle" TargetType="{x:Type ScrollBar}">
            <Setter Property="Width" Value="8"/>
            <Setter Property="Background" Value="#2D3748"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ScrollBar}">
                        <Grid>
                            <Border Background="{TemplateBinding Background}" CornerRadius="4" Opacity="0.6"/>
                            <Track x:Name="PART_Track" IsDirectionReversed="True">
                                <Track.Thumb>
                                    <Thumb Style="{StaticResource CustomScrollBarThumbStyle}"/>
                                </Track.Thumb>
                            </Track>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 自定义滚动视图样式 -->
        <Style x:Key="CustomScrollViewerStyle" TargetType="{x:Type ScrollViewer}">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ScrollViewer}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="*"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <ScrollContentPresenter Grid.Column="0" Grid.Row="0" Margin="0,0,8,0"/>

                            <ScrollBar x:Name="PART_VerticalScrollBar"
                                      Grid.Column="1" Grid.Row="0"
                                      Orientation="Vertical"
                                      Value="{TemplateBinding VerticalOffset}"
                                      Maximum="{TemplateBinding ScrollableHeight}"
                                      ViewportSize="{TemplateBinding ViewportHeight}"
                                      Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                                      Style="{StaticResource CustomScrollBarStyle}"/>

                            <ScrollBar x:Name="PART_HorizontalScrollBar"
                                      Grid.Column="0" Grid.Row="1"
                                      Orientation="Horizontal"
                                      Value="{TemplateBinding HorizontalOffset}"
                                      Maximum="{TemplateBinding ScrollableWidth}"
                                      ViewportSize="{TemplateBinding ViewportWidth}"
                                      Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                                      Style="{StaticResource CustomScrollBarStyle}"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Border Background="#1E1E2E" CornerRadius="15" BorderBrush="#4A5568" BorderThickness="1">
        <Border.Effect>
            <DropShadowEffect ShadowDepth="5" BlurRadius="15" Opacity="0.3" Color="Black"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="50"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="60"/>
            </Grid.RowDefinitions>

            <!-- 标题栏 -->
            <Border Grid.Row="0" Background="#293153" CornerRadius="15,15,0,0" MouseDown="TitleBar_MouseDown">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0,0,0">
                        <TextBlock Text="&#xE789;" FontFamily="Segoe MDL2 Assets" FontSize="16"
                                   Foreground="#5D6EFF" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBlock x:Name="WindowTitle" Text="新闻详情" FontSize="16" Foreground="White" FontWeight="SemiBold"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <!-- 全屏按钮 -->
                        <Button x:Name="FullScreenButton" Width="40" Height="40"
                                Background="Transparent" BorderThickness="0" Click="FullScreenButton_Click"
                                Cursor="Hand" ToolTip="全屏显示">
                            <TextBlock x:Name="FullScreenIcon" Text="&#xE740;" FontFamily="Segoe MDL2 Assets"
                                       FontSize="14" Foreground="White"/>
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#4A5AE8"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                        </Button>

                        <!-- 关闭按钮 -->
                        <Button x:Name="CloseButton" Width="40" Height="40"
                                Background="Transparent" BorderThickness="0" Click="CloseButton_Click"
                                Cursor="Hand" ToolTip="关闭窗口">
                            <TextBlock Text="&#xE8BB;" FontFamily="Segoe MDL2 Assets"
                                       FontSize="14" Foreground="White"/>
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Background" Value="Transparent"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#FF4757"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                        </Button>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- 内容区域 -->
            <Grid Grid.Row="1" Margin="20">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 新闻标题 -->
                <TextBlock x:Name="NewsTitle" Grid.Row="0" Text="新闻标题" FontSize="24" FontWeight="Bold"
                           Foreground="White" TextWrapping="Wrap" Margin="0,0,0,15"/>

                <!-- 新闻信息 -->
                <Grid Grid.Row="1" Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="来源：" Foreground="#8890AD" FontSize="12"/>
                        <TextBlock x:Name="NewsSource" Text="凤凰网" Foreground="#A0A0FF" FontSize="12"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2" Orientation="Horizontal">
                        <TextBlock Text="时间：" Foreground="#8890AD" FontSize="12"/>
                        <TextBlock x:Name="NewsTime" Text="2024-06-24 10:30" Foreground="#A0A0FF" FontSize="12"/>
                    </StackPanel>
                </Grid>

                <!-- 分隔线 -->
                <Border Grid.Row="2" Height="1" Background="#364375" Margin="0,0,0,20"/>

                <!-- 显示模式切换按钮 -->
                <StackPanel Grid.Row="3" Orientation="Horizontal" Margin="0,0,0,15">
                    <Button x:Name="TextModeButton" Content="📄 文本模式" Background="#5D6EFF" Foreground="White"
                            BorderThickness="0" Padding="15,5" Margin="0,0,10,0" Click="TextModeButton_Click"
                            Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="#5D6EFF"/>
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" CornerRadius="4"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#4A5AE8"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <Button x:Name="WebModeButton" Content="🌐 网页模式" Background="#364375" Foreground="White"
                            BorderThickness="0" Padding="15,5" Click="WebModeButton_Click"
                            Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="#364375"/>
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" CornerRadius="4"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#4A5AE8"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </StackPanel>

                <!-- 内容显示区域 -->
                <Grid Grid.Row="4">
                    <!-- 文本模式 -->
                    <ScrollViewer x:Name="TextModeViewer" Style="{StaticResource CustomScrollViewerStyle}"
                                 VerticalScrollBarVisibility="Auto" Visibility="Visible">
                        <StackPanel>
                            <!-- 新闻内容 -->
                            <TextBlock x:Name="NewsContent" Text="新闻内容加载中..."
                                       FontFamily="Microsoft YaHei, 微软雅黑, PingFang SC, Helvetica Neue, Arial, sans-serif"
                                       FontSize="18"
                                       Foreground="#E0E0E0" TextWrapping="Wrap" LineHeight="30"
                                       Margin="0,10,0,10"/>

                            <!-- 加载指示器 -->
                            <Grid x:Name="LoadingPanel" Visibility="Visible" Margin="0,50,0,0">
                                <StackPanel HorizontalAlignment="Center">
                                    <ProgressBar IsIndeterminate="True" Width="200" Height="4"
                                                Background="Transparent" Foreground="#5D6EFF"/>
                                    <TextBlock Text="正在加载新闻内容..." Foreground="#8890AD"
                                              HorizontalAlignment="Center" Margin="0,10,0,0"/>
                                </StackPanel>
                            </Grid>

                            <!-- 提示信息 -->
                            <Border x:Name="TipPanel" Background="#364375" CornerRadius="8" Padding="15"
                                    Margin="0,20,0,0" Visibility="Collapsed">
                                <StackPanel>
                                    <TextBlock Text="💡 阅读提示" FontSize="14" FontWeight="Bold"
                                              Foreground="#5D6EFF" Margin="0,0,0,8"/>
                                    <TextBlock Text="当前显示的是新闻摘要内容。如需阅读完整原文，请切换到网页模式或点击下方【在浏览器中打开】按钮。"
                                              FontSize="12" Foreground="#B0B0B0" TextWrapping="Wrap" LineHeight="18"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ScrollViewer>

                    <!-- 网页模式 -->
                    <Grid x:Name="WebModeViewer" Visibility="Collapsed">
                        <wv2:WebView2 x:Name="NewsWebView" />

                        <!-- WebView2 加载指示器 -->
                        <Grid x:Name="WebLoadingPanel" Background="#1E1E2E" Visibility="Collapsed">
                            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                <ProgressBar IsIndeterminate="True" Width="200" Height="4"
                                            Background="Transparent" Foreground="#5D6EFF"/>
                                <TextBlock Text="正在加载网页..." Foreground="#8890AD"
                                          HorizontalAlignment="Center" Margin="0,10,0,0"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Grid>
            </Grid>

            <!-- 底部按钮区域 -->
            <Border Grid.Row="2" Background="#293153" CornerRadius="0,0,15,15">
                <Grid Margin="20,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 刷新内容按钮 -->
                    <Button x:Name="RefreshButton" Grid.Column="1" Content="🔄 刷新内容"
                            Background="#364375" Foreground="White" BorderThickness="0"
                            Padding="15,8" Margin="0,0,10,0" Click="RefreshButton_Click"
                            Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="#364375"/>
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" CornerRadius="6"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#4A5AE8"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <!-- 在浏览器中打开按钮 -->
                    <Button x:Name="OpenOriginalButton" Grid.Column="2" Content="🌐 在浏览器中打开"
                            Background="#5D6EFF" Foreground="White" BorderThickness="0"
                            Padding="20,8" Margin="0,0,10,0" Click="OpenOriginalButton_Click"
                            Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="#5D6EFF"/>
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" CornerRadius="6"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#4A5AE8"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>

                    <!-- 关闭按钮 -->
                    <Button x:Name="CloseContentButton" Grid.Column="3" Content="关闭"
                            Background="#6C757D" Foreground="White" BorderThickness="0"
                            Padding="20,8" Click="CloseButton_Click" Cursor="Hand">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Background" Value="#6C757D"/>
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" CornerRadius="6"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                                <Style.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter Property="Background" Value="#5A6268"/>
                                    </Trigger>
                                </Style.Triggers>
                            </Style>
                        </Button.Style>
                    </Button>
                </Grid>
            </Border>
        </Grid>
    </Border>
</Window>
