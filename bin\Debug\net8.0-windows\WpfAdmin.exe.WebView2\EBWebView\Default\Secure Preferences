{"extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\137.0.3296.68\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\137.0.3296.68\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "protection": {"macs": {"browser": {"show_home_button": "92F4037685B2638305F4874DC2A55E9B9D9FF730C5DFF0AA32EADD18012CB8F1"}, "default_search_provider_data": {"template_url_data": "4915E2BA2EAA520286BE7DA8F60914F7C16FD748B8B9D4D3FD7C0D899B4B088D"}, "edge": {"services": {"account_id": "B8DA2CE464EADD7641F86853D86B3868CDB251180F10B0FEE76EBC5D5F59CA5C", "last_username": "42E06D24239C5BCAE16244250DACB21E6B0586E60AD3D5EF497A57CDA73B0FD8"}}, "enterprise_signin": {"policy_recovery_token": "0E2BB587D04AB6306ED4D55FF887306DC58F7A83CF4E059A5FB460109C7F0522"}, "extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": "F05B4C3E7431157297855B50BFF2A5E1209880A85DD3B99AE21DBA38E3325E8B", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "A9E72C4FC44AC68C8BC3E25653C8D261992E722F4D3B15082FF39BB686420946"}, "ui": {"developer_mode": "D873C570FE2D0079CC978D06056A76B843DAAFCB4537F11E2C6323F76AFC05B1"}}, "google": {"services": {"last_signed_in_username": "9C55D7C93EF3C79563B71B9B4F5458D1001F2FBDA837C7E8CC7F9E8196EDCDE9"}}, "homepage": "627142CD12F09C09A0375F9ACE0CD1EF5BA1ED5514B01C8E8F3FB63FD40FB77D", "homepage_is_newtabpage": "98CF8D653090C5EC9D399BCBA195017000445E374DDE2803ACCE33209F1432CB", "media": {"cdm": {"origin_data": "9241FA20113DA82961BE686B97D2FD3BC3427AAB1D9D3A0C999057784B9F782B"}, "storage_id_salt": "C4741DCAC07EAC64077CC029A4586968B9B870D99BDBB3E429DF2622CACF0BEE"}, "pinned_tabs": "0BFFC0018673F6A3CBC3B6D701488756DEFF0128F6CF7F14F331651B454C07D4", "prefs": {"preference_reset_time": "7C778B0B288D1B7463A176809F72F6D482345C0DB67544BF5B59C80E5D528D1D"}, "safebrowsing": {"incidents_sent": "CDDCD15F01C2F3C3F5B53BD08697965231429E2AFEBEDDFE2C3C8F3DDF210DE3"}, "search_provider_overrides": "8CACD6065E57E10E396A81F6869B7574AA4A8FC790C8F7C770DE91F027222DCE", "session": {"restore_on_startup": "39979C8C6AEEAE166154463C8BE8F41BF92528E8C8D81171D47BEEDE2039FA81", "startup_urls": "5E4A7E929428531E9FFC5C13F0E256AAB5E15EDA97E6679CA7F1C50688D996E1"}}, "super_mac": "A147BCBAC3AE6D9BB523F8CABFD1FB585CF956541565804A8B843DC78A4384AD"}}