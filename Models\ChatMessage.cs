using System;

namespace WpfApp.Models
{
    public class ChatMessage
    {
        public string Sender { get; set; }
        public string Content { get; set; }
        public DateTime Time { get; set; }
        public bool IsUser { get; set; }
        public string Timestamp => Time.ToString("HH:mm");

        public ChatMessage(string sender, string content, bool isUser)
        {
            Sender = sender;
            Content = content;
            Time = DateTime.Now;
            IsUser = isUser;
        }
    }
}