// 地名搜索功能
let searchMarkers = [];
let searchInfoWindows = [];

// 初始化地名搜索功能
function initPlaceSearch() {
    console.log('初始化地名搜索功能');
    
    const searchBtn = document.getElementById('searchBtn');
    const clearSearchBtn = document.getElementById('clearSearchBtn');
    const searchInput = document.getElementById('placeSearchInput');
    
    if (searchBtn) {
        searchBtn.onclick = function() {
            const keyword = searchInput ? searchInput.value.trim() : '';
            if (keyword) {
                searchPlace(keyword);
            } else {
                alert('请输入要搜索的地名');
            }
        };
    }
    
    if (clearSearchBtn) {
        clearSearchBtn.onclick = function() {
            clearSearchResults();
        };
    }
    
    if (searchInput) {
        searchInput.onkeypress = function(e) {
            if (e.key === 'Enter') {
                const keyword = this.value.trim();
                if (keyword) {
                    searchPlace(keyword);
                }
            }
        };
    }
}

// 搜索地名
function searchPlace(keyword) {
    console.log('搜索地名:', keyword);
    
    if (typeof AMap === 'undefined') {
        console.error('AMap未加载');
        alert('高德地图API未加载，请刷新页面重试');
        return;
    }
    
    // 确保PlaceSearch插件已加载
    if (!AMap.PlaceSearch) {
        console.log('PlaceSearch插件未加载，尝试动态加载...');
        AMap.plugin(['AMap.PlaceSearch'], function() {
            console.log('PlaceSearch插件动态加载成功');
            doSearchWithPlugin(keyword);
        });
    } else {
        doSearchWithPlugin(keyword);
    }
}

// 使用PlaceSearch插件执行搜索
function doSearchWithPlugin(keyword) {
    try {
        const placeSearch = new AMap.PlaceSearch({
            pageSize: 10,
            pageIndex: 1,
            city: '全国',
            citylimit: false,
            extensions: 'all'
        });
        
        console.log('PlaceSearch对象创建成功');
        
        placeSearch.search(keyword, function(status, result) {
            console.log('搜索状态:', status, '结果:', result);
            
            if (status === 'complete' && result.poiList && result.poiList.pois.length > 0) {
                showSearchResults(result.poiList.pois);
            } else {
                showSearchResults([]);
                alert('未找到相关地名');
            }
        });
    } catch (error) {
        console.error('搜索执行错误:', error);
        alert('搜索功能执行失败: ' + error.message);
    }
}

// 显示搜索结果
function showSearchResults(pois) {
    const resultsDiv = document.getElementById('searchResults');
    const listDiv = document.getElementById('searchResultsList');
    
    if (!resultsDiv || !listDiv) return;
    
    listDiv.innerHTML = '';
    
    if (pois.length === 0) {
        resultsDiv.style.display = 'none';
        return;
    }
    
    resultsDiv.style.display = 'block';
    
    for (let i = 0; i < pois.length; i++) {
        const poi = pois[i];
        const item = document.createElement('div');
        item.style.cssText = 'padding:5px;margin:2px 0;background:#f8f9fa;border-radius:3px;cursor:pointer;border:1px solid transparent;';
        
        const name = poi.name || '未知地点';
        const address = poi.address || poi.district || '';
        const type = poi.type || '未知';
        
        const nameDiv = document.createElement('div');
        nameDiv.style.cssText = 'font-weight:bold;color:#007bff;';
        nameDiv.textContent = name;
        
        const addressDiv = document.createElement('div');
        addressDiv.style.cssText = 'font-size:12px;color:#6c757d;';
        addressDiv.textContent = address;
        
        const typeDiv = document.createElement('div');
        typeDiv.style.cssText = 'font-size:11px;color:#868e96;';
        typeDiv.textContent = '类型: ' + type;
        
        item.appendChild(nameDiv);
        item.appendChild(addressDiv);
        item.appendChild(typeDiv);
        
        item.onmouseenter = function() {
            this.style.backgroundColor = '#e9ecef';
            this.style.borderColor = '#007bff';
        };
        
        item.onmouseleave = function() {
            this.style.backgroundColor = '#f8f9fa';
            this.style.borderColor = 'transparent';
        };
        
        item.onclick = function() {
            locateToPlace(poi);
        };
        
        listDiv.appendChild(item);
    }
}

// 定位到地点
function locateToPlace(poi) {
    console.log('定位到:', poi);
    
    if (!poi.location) {
        alert('该地点坐标信息缺失');
        return;
    }
    
    const lng = poi.location.lng || poi.location.getLng();
    const lat = poi.location.lat || poi.location.getLat();
    
    map.setCenter([lng, lat]);
    map.setZoom(15);
    
    clearSearchMarkers();
    
    const marker = new AMap.Marker({
        position: [lng, lat],
        title: poi.name,
        icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_bs.png'
    });
    
    map.add(marker);
    searchMarkers.push(marker);
    
    const content = document.createElement('div');
    content.style.cssText = 'padding:10px;min-width:200px;';
    
    const titleDiv = document.createElement('div');
    titleDiv.style.cssText = 'font-weight:bold;font-size:14px;color:#333;margin-bottom:5px;';
    titleDiv.textContent = poi.name;
    
    const addressDiv = document.createElement('div');
    addressDiv.style.cssText = 'font-size:12px;color:#666;margin-bottom:3px;';
    addressDiv.textContent = '地址: ' + (poi.address || '未知');
    
    const typeDiv = document.createElement('div');
    typeDiv.style.cssText = 'font-size:12px;color:#666;margin-bottom:3px;';
    typeDiv.textContent = '类型: ' + (poi.type || '未知');
    
    const coordDiv = document.createElement('div');
    coordDiv.style.cssText = 'font-size:12px;color:#666;';
    coordDiv.textContent = '坐标: ' + lng.toFixed(6) + ', ' + lat.toFixed(6);
    
    content.appendChild(titleDiv);
    content.appendChild(addressDiv);
    content.appendChild(typeDiv);
    content.appendChild(coordDiv);
    
    const infoWindow = new AMap.InfoWindow({
        content: content,
        offset: new AMap.Pixel(0, -30)
    });
    
    infoWindow.open(map, [lng, lat]);
    searchInfoWindows.push(infoWindow);
    
    document.getElementById('searchResults').style.display = 'none';
}

// 清除搜索结果
function clearSearchResults() {
    const searchInput = document.getElementById('placeSearchInput');
    if (searchInput) searchInput.value = '';
    
    const resultsDiv = document.getElementById('searchResults');
    if (resultsDiv) resultsDiv.style.display = 'none';
    
    clearSearchMarkers();
}

// 清除搜索标记
function clearSearchMarkers() {
    if (searchMarkers.length > 0) {
        for (let i = 0; i < searchMarkers.length; i++) {
            map.remove(searchMarkers[i]);
        }
        searchMarkers = [];
    }
    
    if (searchInfoWindows.length > 0) {
        for (let i = 0; i < searchInfoWindows.length; i++) {
            searchInfoWindows[i].close();
        }
        searchInfoWindows = [];
    }
}

